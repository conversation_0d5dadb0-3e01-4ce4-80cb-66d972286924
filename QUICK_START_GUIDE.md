# 🚀 دليل البدء السريع - نظام إدارة الموظفين

## ⚡ تشغيل سريع (5 دقائق)

### 1. تشغيل لوحة التحكم الإدارية

```bash
# الانتقال لمجلد Laravel
cd DossBackend

# تثبيت المتطلبات (مرة واحدة فقط)
composer install

# نسخ ملف الإعدادات
cp .env.example .env

# إنشاء مفتاح التطبيق
php artisan key:generate

# إعداد قاعدة البيانات
php artisan migrate --seed

# تشغيل الخادم
php artisan serve
```

**الآن افتح المتصفح على:** http://localhost:8000

### 2. تشغيل التطبيق المحمول

```bash
# الانتقال لمجلد Flutter
cd doss_employee_app

# تثبيت المتطلبات
flutter pub get

# تشغيل التطبيق
flutter run
```

## 👤 حسابات تجريبية

### للوحة التحكم الإدارية
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### للتطبيق المحمول
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

*(يوجد 50 موظف تجريبي: employee1@ إلى employee50@)*

## 🎯 أول خطوات الاختبار

### في لوحة التحكم:
1. سجل دخول بحساب الإدارة
2. راجع الإحصائيات في الصفحة الرئيسية
3. تصفح قائمة الموظفين
4. راجع تقارير الحضور
5. راجع طلبات الإجازات

### في التطبيق المحمول:
1. سجل دخول بحساب موظف
2. راجع لوحة التحكم الرئيسية
3. جرب تسجيل الحضور/الانصراف
4. تصفح تاريخ الحضور والإحصائيات
5. راجع رصيد الإجازات
6. راجع كشف الراتب

## 🔧 إعدادات مهمة

### قاعدة البيانات
في ملف `.env`:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=doss_pharmacy
DB_USERNAME=root
DB_PASSWORD=
```

### رابط API في التطبيق
في ملف `lib/constants/api_constants.dart`:
```dart
static const String baseUrl = 'http://********:8000/api';
// للمحاكي: ********
// للجهاز الحقيقي: عنوان IP الخاص بك
```

## 📱 اختبار على الجهاز الحقيقي

### للأندرويد:
1. فعل وضع المطور في الجهاز
2. فعل USB Debugging
3. وصل الجهاز بالكمبيوتر
4. غير رابط API إلى عنوان IP الخاص بك
5. شغل `flutter run`

### للآيفون:
1. افتح المشروع في Xcode
2. اختر جهازك من القائمة
3. اضغط Run

## 🐛 حل المشاكل الشائعة

### مشكلة قاعدة البيانات
```bash
# إعادة إنشاء قاعدة البيانات
php artisan migrate:fresh --seed
```

### مشكلة الصلاحيات
```bash
# إعطاء صلاحيات للمجلدات
chmod -R 775 storage bootstrap/cache
```

### مشكلة Flutter
```bash
# تنظيف وإعادة تثبيت
flutter clean
flutter pub get
```

### مشكلة الاتصال بـ API
- تأكد من تشغيل خادم Laravel
- تحقق من رابط API في التطبيق
- تأكد من عدم وجود Firewall يمنع الاتصال

## 📋 قائمة مراجعة سريعة

### قبل التسليم للعميل:
- [ ] تشغيل لوحة التحكم بنجاح
- [ ] تشغيل التطبيق المحمول بنجاح
- [ ] اختبار تسجيل الدخول
- [ ] اختبار الحضور والانصراف
- [ ] اختبار طلب الإجازات
- [ ] اختبار عرض الرواتب
- [ ] مراجعة البيانات التجريبية
- [ ] التأكد من عمل جميع الروابط

### للعميل للمراجعة:
- [ ] مراجعة التصميم والألوان
- [ ] مراجعة النصوص والترجمات
- [ ] اختبار جميع المميزات
- [ ] التأكد من صحة البيانات
- [ ] طلب أي تعديلات مطلوبة

## 📞 الدعم السريع

### أخطاء شائعة وحلولها:

**خطأ: "Connection refused"**
- تأكد من تشغيل خادم Laravel
- تحقق من رابط API

**خطأ: "Database not found"**
- أنشئ قاعدة البيانات يدوياً
- شغل `php artisan migrate`

**خطأ: "Permission denied"**
- أعط صلاحيات للمجلدات
- تأكد من صلاحيات الكتابة

**التطبيق لا يتصل بالخادم:**
- تحقق من عنوان IP
- تأكد من عدم وجود Firewall
- جرب على المحاكي أولاً

## 🎉 مبروك!

إذا وصلت هنا وكل شيء يعمل، فقد نجحت في تشغيل النظام!

الآن يمكنك:
- استكشاف جميع المميزات
- اختبار السيناريوهات المختلفة
- إضافة بياناتك الحقيقية
- تخصيص الإعدادات حسب احتياجاتك

---

**للمساعدة الإضافية، راجع `CLIENT_DELIVERY_GUIDE.md`**
