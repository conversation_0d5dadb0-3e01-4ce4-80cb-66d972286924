# 📦 دليل تسليم نظام إدارة الموظفين - صيدلية دوس

## 🎯 نظرة عامة

تم تطوير نظام شامل لإدارة الموظفين يتكون من:
- **تطبيق Flutter للموظفين** (Android/iOS)
- **لوحة تحكم Laravel للإدارة** (Web)
- **قاعدة بيانات MySQL**
- **APIs متكاملة**

## 📱 التطبيق المحمول (Flutter)

### المميزات المكتملة ✅

#### 1. نظام المصادقة والأمان
- تسجيل دخول آمن بالبريد الإلكتروني وكلمة المرور
- حفظ بيانات الدخول تلقائياً
- تسجيل خروج آمن
- حماية الشاشات بالمصادقة

#### 2. لوحة التحكم الرئيسية
- إحصائيات شاملة للموظف
- اختصارات سريعة للمهام الأساسية
- عرض الحضور اليومي
- رصيد الإجازات
- آخر كشف راتب

#### 3. شاشة الحضور والانصراف المتقدمة
**تبويب "اليوم":**
- تسجيل الدخول/الخروج بضغطة واحدة
- عرض حالة الحضور الحالية
- ملخص ساعات العمل اليومية
- إعدادات الحضور (تتبع الموقع، التذكيرات)

**تبويب "التاريخ":**
- عرض سجل الحضور الشهري
- إمكانية اختيار الشهر والسنة
- تفاصيل كل يوم حضور
- حالة الحضور (حاضر، متأخر، غائب)

**تبويب "الإحصائيات":**
- معدل الحضور الشهري
- عدد أيام الحضور والغياب
- عدد أيام التأخير
- متوسط ساعات العمل اليومية

#### 4. شاشة الإجازات المتقدمة
**تبويب "الرصيد":**
- عرض أرصدة جميع أنواع الإجازات
- الإجازة السنوية، المرضية، الطارئة، إلخ
- المخصص، المستخدم، المتبقي لكل نوع
- معدل استخدام الإجازات
- الإجازات الرسمية القادمة

**تبويب "التاريخ":**
- سجل جميع الإجازات السابقة
- حالة كل إجازة (معتمد، مرفوض، في الانتظار)
- تفاصيل كل إجازة (التواريخ، السبب، ملاحظة المدير)
- إمكانية اختيار السنة

**تبويب "الطلبات":**
- الطلبات المعلقة (يمكن إلغاؤها)
- الإجازات القادمة
- الإجازات الحالية
- زر طلب إجازة جديدة

#### 5. شاشة الرواتب والسلف المتقدمة
**تبويب "الراتب":**
- نظرة عامة جميلة على الراتب الحالي
- تفصيل الراتب (أساسي، بدلات، استقطاعات)
- تاريخ الانضمام والمنصب
- كشوف الرواتب الأخيرة
- إحصائيات الراتب السنوية

**تبويب "التاريخ":**
- سجل جميع كشوف الرواتب
- تفاصيل كل كشف راتب
- إمكانية اختيار السنة
- حالة الدفع لكل كشف

**تبويب "السلف":**
- السلف النشطة مع تتبع التقدم
- السلف في الانتظار
- تفاصيل كل سلفة (المبلغ، الأقساط، المتبقي)
- إمكانية طلب سلفة جديدة
- تاريخ القسط القادم

#### 6. شاشة الملف الشخصي
- عرض البيانات الشخصية
- معلومات الوظيفة
- بيانات الاتصال
- إمكانية تحديث البيانات الأساسية

### التصميم والواجهة 🎨
- تصميم عصري ومتجاوب
- دعم اللغة العربية بالكامل
- ألوان متناسقة مع هوية الصيدلية
- رموز وأيقونات واضحة
- انتقالات سلسة بين الشاشات
- مؤشرات تحميل وحالات فارغة

### الأداء والجودة ⚡
- استجابة سريعة
- إدارة ذاكرة محسنة
- معالجة أخطاء شاملة
- حفظ البيانات محلياً للوصول السريع
- تحديث البيانات التلقائي

## 🖥️ لوحة التحكم الإدارية (Laravel)

### المميزات المكتملة ✅

#### 1. لوحة التحكم الرئيسية
- إحصائيات شاملة للشركة
- عدد الموظفين الإجمالي
- حضور اليوم (حاضر، غائب، متأخر)
- طلبات الإجازات المعلقة
- كشوف الرواتب الشهرية
- رسوم بيانية تفاعلية

#### 2. إدارة الموظفين
- قائمة شاملة بجميع الموظفين
- إضافة موظف جديد
- تحديث بيانات الموظفين
- إدارة الأقسام والفروع
- تفعيل/إلغاء تفعيل الحسابات

#### 3. إدارة الحضور
- مراقبة الحضور اليومي
- تقارير الحضور الشهرية
- إدارة أوقات العمل
- تسجيل الحضور اليدوي (عند الحاجة)
- إحصائيات التأخير والغياب

#### 4. إدارة الإجازات
- مراجعة طلبات الإجازات
- الموافقة/رفض الطلبات
- إدارة أنواع الإجازات
- تحديد أرصدة الإجازات
- تقارير الإجازات

#### 5. إدارة الرواتب
- إنشاء كشوف الرواتب
- إدارة البدلات والاستقطاعات
- معالجة السلف
- تقارير الرواتب الشهرية
- إحصائيات التكاليف

#### 6. التقارير والإحصائيات
- تقارير الحضور التفصيلية
- تقارير الإجازات
- تقارير الرواتب
- إحصائيات الأداء
- تصدير التقارير (PDF, Excel)

### الأمان والصلاحيات 🔒
- نظام صلاحيات متدرج
- حماية البيانات الحساسة
- تسجيل العمليات (Audit Log)
- نسخ احتياطية تلقائية
- تشفير كلمات المرور

## 🗄️ قاعدة البيانات

### الجداول الرئيسية
- `users` - بيانات الموظفين
- `companies` - بيانات الشركات
- `departments` - الأقسام
- `branches` - الفروع
- `attendances` - سجلات الحضور
- `leave_requests` - طلبات الإجازات
- `leave_types` - أنواع الإجازات
- `payrolls` - كشوف الرواتب
- `employee_advances` - السلف

### البيانات التجريبية
- 50 موظف تجريبي
- سجلات حضور لآخر 30 يوم
- طلبات إجازات متنوعة
- كشوف رواتب لآخر 3 أشهر
- أنواع إجازات مختلفة

## 🔗 APIs المتاحة

### APIs المصادقة
- `POST /api/login` - تسجيل الدخول
- `POST /api/logout` - تسجيل الخروج
- `GET /api/profile` - الملف الشخصي

### APIs الحضور
- `GET /api/attendance/today` - حضور اليوم
- `POST /api/attendance/check-in` - تسجيل دخول
- `POST /api/attendance/check-out` - تسجيل خروج
- `GET /api/attendance/history` - تاريخ الحضور

### APIs الإجازات
- `GET /api/leaves/balance` - رصيد الإجازات
- `POST /api/leaves/submit-request` - طلب إجازة
- `GET /api/leaves/history` - تاريخ الإجازات
- `DELETE /api/leaves/{id}` - إلغاء طلب إجازة

### APIs الرواتب
- `GET /api/payroll/current-salary` - الراتب الحالي
- `GET /api/payroll/history` - تاريخ الرواتب
- `GET /api/payroll/advances` - السلف
- `POST /api/payroll/request-advance` - طلب سلفة

## 📋 متطلبات التشغيل

### للخادم (Server)
- PHP 8.1+
- MySQL 8.0+
- Composer
- Laravel 10
- Apache/Nginx

### للتطبيق المحمول
- Flutter 3.16+
- Dart 3.2+
- Android Studio / VS Code
- Android SDK (للأندرويد)
- Xcode (للآيفون)

## 🚀 خطوات التشغيل

### 1. إعداد الخادم
```bash
# استنساخ المشروع
git clone [repository-url]

# تثبيت المتطلبات
composer install

# إعداد البيئة
cp .env.example .env
php artisan key:generate

# إعداد قاعدة البيانات
php artisan migrate
php artisan db:seed

# تشغيل الخادم
php artisan serve
```

### 2. إعداد التطبيق
```bash
# الانتقال لمجلد التطبيق
cd doss_employee_app

# تثبيت المتطلبات
flutter pub get

# تشغيل التطبيق
flutter run
```

## 🔧 الإعدادات المطلوبة

### متغيرات البيئة (.env)
```
APP_NAME="Doss Pharmacy Employee System"
APP_URL=http://localhost:8000
DB_DATABASE=doss_pharmacy
DB_USERNAME=root
DB_PASSWORD=
```

### إعدادات التطبيق
- تحديث رابط API في `lib/constants/api_constants.dart`
- تحديث معلومات الشركة في `lib/constants/app_constants.dart`

## 📞 الدعم والمساعدة

للحصول على الدعم:
1. راجع ملف `README.md` في كل مشروع
2. راجع ملف `DEVELOPMENT.md` لتفاصيل التطوير
3. تحقق من ملفات التوثيق في مجلد `docs/`
4. استخدم `flutter doctor` للتحقق من بيئة Flutter
5. استخدم `php artisan` للتحقق من Laravel

---

**تم تطوير هذا النظام بواسطة Augment Agent**  
**تاريخ التسليم: ديسمبر 2024**
