import '../constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/leave.dart';
import 'api_service.dart';

class LeaveService {
  static final LeaveService _instance = LeaveService._internal();
  factory LeaveService() => _instance;
  LeaveService._internal();

  final ApiService _apiService = ApiService();

  // Get leave balance
  Future<ApiResponse<List<LeaveBalance>>> getLeaveBalance() async {
    try {
      final response = await _apiService.get<List<dynamic>>(
        ApiEndpoints.leaveBalance,
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final balanceList = response.data!
            .map((item) => LeaveBalance.fromJson(item))
            .toList();
        
        return ApiResponse.success(balanceList, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب رصيد الإجازات: $e');
    }
  }

  // Get leave history
  Future<ApiResponse<List<Leave>>> getLeaveHistory({
    String? status,
    int? year,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (status != null) queryParams['status'] = status;
      if (year != null) queryParams['year'] = year;

      final response = await _apiService.get<List<dynamic>>(
        ApiEndpoints.leaveHistory,
        queryParameters: queryParams,
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final leaveList = response.data!
            .map((item) => Leave.fromJson(item))
            .toList();
        
        return ApiResponse.success(leaveList, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب تاريخ الإجازات: $e');
    }
  }

  // Submit leave request
  Future<ApiResponse<Map<String, dynamic>>> submitLeaveRequest(
    LeaveRequest request,
  ) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.submitLeaveRequest,
        data: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تقديم طلب الإجازة: $e');
    }
  }

  // Cancel leave request
  Future<ApiResponse<void>> cancelLeaveRequest(int leaveId) async {
    try {
      final response = await _apiService.put<void>(
        '${ApiEndpoints.cancelLeaveRequest}/$leaveId',
      );

      return response;
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء إلغاء طلب الإجازة: $e');
    }
  }

  // Get leave types
  Future<ApiResponse<List<LeaveType>>> getLeaveTypes() async {
    try {
      final response = await _apiService.get<List<dynamic>>(
        ApiEndpoints.leaveTypes,
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final typesList = response.data!
            .map((item) => LeaveType.fromJson(item))
            .toList();
        
        return ApiResponse.success(typesList, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب أنواع الإجازات: $e');
    }
  }

  // Get holidays
  Future<ApiResponse<List<Holiday>>> getHolidays({int? year}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (year != null) queryParams['year'] = year;

      final response = await _apiService.get<List<dynamic>>(
        ApiEndpoints.holidays,
        queryParameters: queryParams,
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final holidaysList = response.data!
            .map((item) => Holiday.fromJson(item))
            .toList();
        
        return ApiResponse.success(holidaysList, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب الإجازات الرسمية: $e');
    }
  }

  // Get leave summary
  Future<ApiResponse<LeaveSummary>> getLeaveSummary({int? year}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (year != null) queryParams['year'] = year;

      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.leaveSummary,
        queryParameters: queryParams,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final summary = LeaveSummary.fromJson(response.data!);
        return ApiResponse.success(summary, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب ملخص الإجازات: $e');
    }
  }

  // Validate leave request
  String? validateLeaveRequest(LeaveRequest request) {
    // Check if leave from is not in the past
    if (request.leaveFrom.isBefore(DateTime.now().subtract(Duration(days: 1)))) {
      return 'لا يمكن طلب إجازة في تاريخ سابق';
    }

    // Check if leave to is after leave from
    if (request.leaveTo.isBefore(request.leaveFrom)) {
      return 'تاريخ انتهاء الإجازة يجب أن يكون بعد تاريخ البداية';
    }

    // Check if reason is provided
    if (request.reason.trim().isEmpty) {
      return 'يجب تقديم سبب للإجازة';
    }

    // Check if reason is not too short
    if (request.reason.trim().length < 10) {
      return 'سبب الإجازة قصير جداً';
    }

    // Check if number of days is reasonable
    if (request.numberOfDays > 30) {
      return 'لا يمكن طلب إجازة أكثر من 30 يوماً';
    }

    return null; // Valid
  }

  // Calculate leave days (excluding weekends and holidays)
  int calculateLeaveDays(
    DateTime from,
    DateTime to, {
    List<Holiday>? holidays,
  }) {
    int days = 0;
    DateTime current = from;

    while (current.isBefore(to) || current.isAtSameMomentAs(to)) {
      // Skip weekends (Friday = 5, Saturday = 6)
      if (current.weekday != DateTime.friday && current.weekday != DateTime.saturday) {
        // Check if it's not a holiday
        bool isHoliday = false;
        if (holidays != null) {
          isHoliday = holidays.any((holiday) =>
              holiday.eventDate.year == current.year &&
              holiday.eventDate.month == current.month &&
              holiday.eventDate.day == current.day);
        }

        if (!isHoliday) {
          days++;
        }
      }
      current = current.add(Duration(days: 1));
    }

    return days;
  }

  // Get leave status color
  int getLeaveStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'معتمد':
        return AppColors.approvedLeaveColor;
      case 'pending':
      case 'في الانتظار':
        return AppColors.pendingLeaveColor;
      case 'rejected':
      case 'مرفوض':
        return AppColors.rejectedLeaveColor;
      case 'cancelled':
      case 'ملغي':
        return AppColors.textSecondaryColor;
      default:
        return AppColors.textSecondaryColor;
    }
  }

  // Get leave type icon
  String getLeaveTypeIcon(String leaveType) {
    switch (leaveType.toLowerCase()) {
      case 'annual':
      case 'سنوية':
        return '🏖️';
      case 'sick':
      case 'مرضية':
        return '🏥';
      case 'emergency':
      case 'طارئة':
        return '🚨';
      case 'maternity':
      case 'أمومة':
        return '👶';
      case 'paternity':
      case 'أبوة':
        return '👨‍👶';
      case 'study':
      case 'دراسية':
        return '📚';
      case 'hajj':
      case 'حج':
        return '🕋';
      default:
        return '📅';
    }
  }

  // Format leave duration
  String formatLeaveDuration(int days) {
    if (days == 1) return 'يوم واحد';
    if (days == 2) return 'يومان';
    if (days <= 10) return '$days أيام';
    return '$days يوماً';
  }

  // Check if leave can be cancelled
  bool canCancelLeave(Leave leave) {
    return leave.isPending && leave.leaveFrom.isAfter(DateTime.now());
  }

  // Get upcoming leaves
  List<Leave> getUpcomingLeaves(List<Leave> leaves) {
    final now = DateTime.now();
    return leaves
        .where((leave) => 
            leave.isApproved && 
            leave.leaveFrom.isAfter(now))
        .toList()
      ..sort((a, b) => a.leaveFrom.compareTo(b.leaveFrom));
  }

  // Get current leaves
  List<Leave> getCurrentLeaves(List<Leave> leaves) {
    final now = DateTime.now();
    return leaves
        .where((leave) => 
            leave.isApproved && 
            leave.leaveFrom.isBefore(now) && 
            leave.leaveTo.isAfter(now))
        .toList();
  }

  // Get pending leaves
  List<Leave> getPendingLeaves(List<Leave> leaves) {
    return leaves.where((leave) => leave.isPending).toList()
      ..sort((a, b) => b.leaveRequestedDate.compareTo(a.leaveRequestedDate));
  }
}
