import '../constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/payroll.dart';
import 'api_service.dart';

class PayrollService {
  static final PayrollService _instance = PayrollService._internal();
  factory PayrollService() => _instance;
  PayrollService._internal();

  final ApiService _apiService = ApiService();

  // Get current salary
  Future<ApiResponse<Salary>> getCurrentSalary() async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.currentSalary,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final salary = Salary.fromJson(response.data!);
        return ApiResponse.success(salary, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب بيانات الراتب: $e');
    }
  }

  // Get payroll history
  Future<ApiResponse<List<Payroll>>> getPayrollHistory({
    int? year,
    int? month,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (year != null) queryParams['year'] = year;
      if (month != null) queryParams['month'] = month;

      final response = await _apiService.get<List<dynamic>>(
        ApiEndpoints.payrollHistory,
        queryParameters: queryParams,
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final payrollList = response.data!
            .map((item) => Payroll.fromJson(item))
            .toList();
        
        return ApiResponse.success(payrollList, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب تاريخ الرواتب: $e');
    }
  }

  // Get payroll details
  Future<ApiResponse<Payroll>> getPayrollDetails(int payrollId) async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '${ApiEndpoints.payrollDetails}/$payrollId',
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final payroll = Payroll.fromJson(response.data!);
        return ApiResponse.success(payroll, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب تفاصيل كشف الراتب: $e');
    }
  }

  // Get advances
  Future<ApiResponse<List<Advance>>> getAdvances({String? status}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (status != null) queryParams['status'] = status;

      final response = await _apiService.get<List<dynamic>>(
        ApiEndpoints.advances,
        queryParameters: queryParams,
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final advancesList = response.data!
            .map((item) => Advance.fromJson(item))
            .toList();
        
        return ApiResponse.success(advancesList, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب بيانات السلف: $e');
    }
  }

  // Request advance
  Future<ApiResponse<Map<String, dynamic>>> requestAdvance(
    AdvanceRequest request,
  ) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.requestAdvance,
        data: request.toJson(),
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تقديم طلب السلفة: $e');
    }
  }

  // Validate advance request
  String? validateAdvanceRequest(AdvanceRequest request) {
    // Check amount
    if (request.amount <= 0) {
      return 'يجب أن يكون مبلغ السلفة أكبر من صفر';
    }

    if (request.amount > 50000) {
      return 'لا يمكن طلب سلفة أكثر من 50,000 ريال';
    }

    // Check reason
    if (request.reason.trim().isEmpty) {
      return 'يجب تقديم سبب للسلفة';
    }

    if (request.reason.trim().length < 10) {
      return 'سبب السلفة قصير جداً';
    }

    // Check installments
    if (request.installments <= 0) {
      return 'يجب أن يكون عدد الأقساط أكبر من صفر';
    }

    if (request.installments > 12) {
      return 'لا يمكن أن يكون عدد الأقساط أكثر من 12';
    }

    // Check installment amount
    if (request.installmentAmount < 100) {
      return 'قيمة القسط قليلة جداً (أقل من 100 ريال)';
    }

    return null; // Valid
  }

  // Get payroll status color
  int getPayrollStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
      case 'مدفوع':
        return AppColors.successColor;
      case 'approved':
      case 'معتمد':
        return AppColors.infoColor;
      case 'draft':
      case 'مسودة':
        return AppColors.warningColor;
      default:
        return AppColors.textSecondaryColor;
    }
  }

  // Get advance status color
  int getAdvanceStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'مكتمل':
        return AppColors.successColor;
      case 'paid':
      case 'مدفوع':
        return AppColors.infoColor;
      case 'approved':
      case 'معتمد':
        return AppColors.secondaryColor;
      case 'pending':
      case 'في الانتظار':
        return AppColors.warningColor;
      case 'rejected':
      case 'مرفوض':
        return AppColors.errorColor;
      default:
        return AppColors.textSecondaryColor;
    }
  }

  // Format currency
  String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} ريال';
  }

  // Calculate net salary percentage
  double calculateNetSalaryPercentage(Payroll payroll) {
    if (payroll.grossSalary == 0) return 0;
    return (payroll.netSalary / payroll.grossSalary) * 100;
  }

  // Calculate deductions percentage
  double calculateDeductionsPercentage(Payroll payroll) {
    if (payroll.grossSalary == 0) return 0;
    return (payroll.totalDeductions / payroll.grossSalary) * 100;
  }

  // Get salary breakdown
  Map<String, double> getSalaryBreakdown(Payroll payroll) {
    return {
      'الراتب الأساسي': payroll.basicSalary,
      'البدلات': payroll.allowances,
      'العمل الإضافي': payroll.overtimeAmount,
      'المكافآت': payroll.bonusAmount,
    };
  }

  // Get deductions breakdown
  Map<String, double> getDeductionsBreakdown(Payroll payroll) {
    return {
      'الخصومات': payroll.deductionAmount,
      'السلف': payroll.advanceAmount,
      'التأمين': payroll.insuranceDeduction,
      'الضرائب': payroll.taxDeduction,
    };
  }

  // Get recent payrolls
  List<Payroll> getRecentPayrolls(List<Payroll> payrolls, {int count = 3}) {
    final sortedPayrolls = List<Payroll>.from(payrolls)
      ..sort((a, b) {
        final aDate = DateTime(a.year, a.month);
        final bDate = DateTime(b.year, b.month);
        return bDate.compareTo(aDate);
      });
    
    return sortedPayrolls.take(count).toList();
  }

  // Get active advances
  List<Advance> getActiveAdvances(List<Advance> advances) {
    return advances
        .where((advance) => advance.isActive && advance.hasRemainingAmount)
        .toList()
      ..sort((a, b) => a.requestDate.compareTo(b.requestDate));
  }

  // Get pending advances
  List<Advance> getPendingAdvances(List<Advance> advances) {
    return advances
        .where((advance) => advance.isPending)
        .toList()
      ..sort((a, b) => b.requestDate.compareTo(a.requestDate));
  }

  // Calculate total advance amount
  double calculateTotalAdvanceAmount(List<Advance> advances) {
    return advances
        .where((advance) => advance.isActive)
        .fold(0.0, (sum, advance) => sum + advance.remainingAmount);
  }

  // Calculate monthly advance deduction
  double calculateMonthlyAdvanceDeduction(List<Advance> advances) {
    return advances
        .where((advance) => advance.isActive)
        .fold(0.0, (sum, advance) => sum + advance.installmentAmount);
  }

  // Get advance payment progress
  double getAdvancePaymentProgress(Advance advance) {
    if (advance.amount == 0) return 0;
    return (advance.paidAmount / advance.amount) * 100;
  }

  // Format advance installment info
  String formatAdvanceInstallmentInfo(Advance advance) {
    return '${advance.installments} أقساط × ${formatCurrency(advance.installmentAmount)}';
  }

  // Check if can request new advance
  bool canRequestNewAdvance(List<Advance> advances) {
    // Check if there's any active advance
    final activeAdvances = getActiveAdvances(advances);
    final pendingAdvances = getPendingAdvances(advances);
    
    return activeAdvances.isEmpty && pendingAdvances.isEmpty;
  }

  // Get advance request limit message
  String getAdvanceRequestLimitMessage(List<Advance> advances) {
    final activeAdvances = getActiveAdvances(advances);
    final pendingAdvances = getPendingAdvances(advances);
    
    if (activeAdvances.isNotEmpty) {
      return 'لديك سلفة نشطة بالفعل';
    }
    
    if (pendingAdvances.isNotEmpty) {
      return 'لديك طلب سلفة في الانتظار';
    }
    
    return '';
  }
}
