import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/user.dart';
import 'api_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();
  User? _currentUser;

  // Get current user
  User? get currentUser => _currentUser;

  // Check if user is logged in
  bool get isLoggedIn => _currentUser != null && _apiService.isAuthenticated;

  // Initialize auth service
  Future<void> initialize() async {
    await _loadUserFromStorage();
    await _apiService.loadAuthToken();
  }

  // Login
  Future<ApiResponse<User>> login(String email, String password) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.login,
        data: {
          'email': email,
          'password': password,
        },
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        
        // Extract token and user data
        final token = data['token'] as String?;
        final userData = data['user'] as Map<String, dynamic>?;

        if (token != null && userData != null) {
          // Save token
          await _apiService.saveAuthToken(token);
          
          // Create user object
          final user = User.fromJson(userData);
          
          // Save user data
          await _saveUserToStorage(user);
          _currentUser = user;

          return ApiResponse.success(user, message: response.message);
        } else {
          return ApiResponse.error('بيانات تسجيل الدخول غير مكتملة');
        }
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تسجيل الدخول: $e');
    }
  }

  // Logout
  Future<ApiResponse<void>> logout() async {
    try {
      // Call logout API
      final response = await _apiService.post<void>(ApiEndpoints.logout);
      
      // Clear local data regardless of API response
      await _clearUserData();
      
      return response.isSuccess 
          ? ApiResponse.success(null, message: 'تم تسجيل الخروج بنجاح')
          : ApiResponse.success(null, message: 'تم تسجيل الخروج محلياً');
    } catch (e) {
      // Clear local data even if API call fails
      await _clearUserData();
      return ApiResponse.success(null, message: 'تم تسجيل الخروج محلياً');
    }
  }

  // Get user profile
  Future<ApiResponse<User>> getProfile() async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.profile,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final user = User.fromJson(response.data!);
        await _saveUserToStorage(user);
        _currentUser = user;
        return ApiResponse.success(user, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب الملف الشخصي: $e');
    }
  }

  // Update profile
  Future<ApiResponse<User>> updateProfile(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.updateProfile,
        data: data,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess) {
        // Refresh user profile
        await getProfile();
        return ApiResponse.success(_currentUser!, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تحديث الملف الشخصي: $e');
    }
  }

  // Change password
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final response = await _apiService.post<void>(
        ApiEndpoints.changePassword,
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
          'new_password_confirmation': confirmPassword,
        },
      );

      return response;
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تغيير كلمة المرور: $e');
    }
  }

  // Refresh token (if needed)
  Future<ApiResponse<void>> refreshToken() async {
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.refreshToken,
      );

      if (response.isSuccess && response.data != null) {
        final token = response.data!['token'] as String?;
        if (token != null) {
          await _apiService.saveAuthToken(token);
          return ApiResponse.success(null, message: 'تم تحديث الرمز المميز');
        }
      }
      
      return ApiResponse.error('فشل في تحديث الرمز المميز');
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تحديث الرمز المميز: $e');
    }
  }

  // Check if session is valid
  Future<bool> isSessionValid() async {
    if (!isLoggedIn) return false;
    
    try {
      final response = await getProfile();
      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }

  // Save user to local storage
  Future<void> _saveUserToStorage(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userKey, jsonEncode(user.toJson()));
    await prefs.setBool(AppConstants.isLoggedInKey, true);
  }

  // Load user from local storage
  Future<void> _loadUserFromStorage() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(AppConstants.userKey);
    final isLoggedIn = prefs.getBool(AppConstants.isLoggedInKey) ?? false;

    if (userJson != null && isLoggedIn) {
      try {
        final userData = jsonDecode(userJson) as Map<String, dynamic>;
        _currentUser = User.fromJson(userData);
      } catch (e) {
        // Clear corrupted data
        await _clearUserData();
      }
    }
  }

  // Clear user data
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userKey);
    await prefs.remove(AppConstants.isLoggedInKey);
    await _apiService.removeAuthToken();
    _currentUser = null;
  }

  // Get user display name
  String get userDisplayName => _currentUser?.displayName ?? 'مستخدم';

  // Get user email
  String get userEmail => _currentUser?.email ?? '';

  // Get user avatar URL
  String get userAvatarUrl => _currentUser?.avatarUrl ?? '';

  // Check if user has avatar
  bool get hasUserAvatar => _currentUser?.hasAvatar ?? false;

  // Get user ID
  int? get userId => _currentUser?.id;

  // Get user company
  String get userCompany => _currentUser?.companyName ?? '';

  // Get user branch
  String get userBranch => _currentUser?.branchName ?? '';

  // Get user department
  String get userDepartment => _currentUser?.departmentName ?? '';

  // Get user position
  String get userPosition => _currentUser?.postName ?? '';
}
