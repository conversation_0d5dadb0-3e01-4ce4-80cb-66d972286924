import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/attendance.dart';
import 'api_service.dart';

class AttendanceService {
  static final AttendanceService _instance = AttendanceService._internal();
  factory AttendanceService() => _instance;
  AttendanceService._internal();

  final ApiService _apiService = ApiService();

  // Get today's attendance status
  Future<ApiResponse<TodayAttendance>> getTodayAttendance() async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.attendanceToday,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final attendance = TodayAttendance.fromJson(response.data!);
        return ApiResponse.success(attendance, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب بيانات الحضور: $e');
    }
  }

  // Get attendance history
  Future<ApiResponse<List<Attendance>>> getAttendanceHistory({
    int? month,
    int? year,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (month != null) queryParams['month'] = month;
      if (year != null) queryParams['year'] = year;

      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.attendanceHistory,
        queryParameters: queryParams,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final attendanceList = (data['attendances'] as List)
            .map((item) => Attendance.fromJson(item))
            .toList();
        
        return ApiResponse.success(attendanceList, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب تاريخ الحضور: $e');
    }
  }

  // Check in
  Future<ApiResponse<Map<String, dynamic>>> checkIn({
    String? note,
    bool includeLocation = true,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (note != null && note.isNotEmpty) {
        data['note'] = note;
      }

      // Get location if requested
      if (includeLocation) {
        final location = await _getCurrentLocation();
        if (location != null) {
          data['latitude'] = location.latitude;
          data['longitude'] = location.longitude;
        }
      }

      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.attendanceCheckIn,
        data: data,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تسجيل الدخول: $e');
    }
  }

  // Check out
  Future<ApiResponse<Map<String, dynamic>>> checkOut({
    String? note,
    bool includeLocation = true,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (note != null && note.isNotEmpty) {
        data['note'] = note;
      }

      // Get location if requested
      if (includeLocation) {
        final location = await _getCurrentLocation();
        if (location != null) {
          data['latitude'] = location.latitude;
          data['longitude'] = location.longitude;
        }
      }

      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.attendanceCheckOut,
        data: data,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء تسجيل الخروج: $e');
    }
  }

  // Get attendance summary
  Future<ApiResponse<AttendanceSummary>> getAttendanceSummary({
    int? month,
    int? year,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (month != null) queryParams['month'] = month;
      if (year != null) queryParams['year'] = year;

      final response = await _apiService.get<Map<String, dynamic>>(
        ApiEndpoints.attendanceSummary,
        queryParameters: queryParams,
        fromJson: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess && response.data != null) {
        final summary = AttendanceSummary.fromJson(response.data!);
        return ApiResponse.success(summary, message: response.message);
      } else {
        return ApiResponse.error(
          response.message,
          errors: response.errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('حدث خطأ أثناء جلب ملخص الحضور: $e');
    }
  }

  // Get current location
  Future<Position?> _getCurrentLocation() async {
    try {
      // Check location permission
      final permission = await _checkLocationPermission();
      if (!permission) {
        return null;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return null;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: AppConstants.locationTimeoutSeconds),
      );

      return position;
    } catch (e) {
      print('Error getting location: $e');
      return null;
    }
  }

  // Check location permission
  Future<bool> _checkLocationPermission() async {
    try {
      final status = await Permission.location.status;
      
      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        final result = await Permission.location.request();
        return result.isGranted;
      } else {
        return false;
      }
    } catch (e) {
      print('Error checking location permission: $e');
      return false;
    }
  }

  // Request location permission
  Future<bool> requestLocationPermission() async {
    try {
      final status = await Permission.location.request();
      return status.isGranted;
    } catch (e) {
      print('Error requesting location permission: $e');
      return false;
    }
  }

  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      print('Error checking location service: $e');
      return false;
    }
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      print('Error opening location settings: $e');
    }
  }

  // Open app settings
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      print('Error opening app settings: $e');
    }
  }

  // Calculate distance between two points
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // Check if user is within allowed location
  bool isWithinAllowedLocation(
    Position currentPosition,
    Position officePosition, {
    double allowedRadius = 100.0, // meters
  }) {
    final distance = calculateDistance(
      currentPosition.latitude,
      currentPosition.longitude,
      officePosition.latitude,
      officePosition.longitude,
    );
    
    return distance <= allowedRadius;
  }

  // Format working hours
  String formatWorkingHours(double hours) {
    final h = hours.floor();
    final m = ((hours - h) * 60).round();
    return '${h}س ${m}د';
  }

  // Format time duration in minutes
  String formatMinutes(int minutes) {
    if (minutes <= 0) return '';
    
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    
    if (hours > 0) {
      return '${hours}س ${mins}د';
    }
    return '${mins}د';
  }

  // Get attendance status color
  int getAttendanceStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'حاضر':
      case 'حضور منتظم':
        return AppColors.presentColor;
      case 'غائب':
        return AppColors.absentColor;
      case 'متأخر':
        return AppColors.lateColor;
      case 'مبكر في المغادرة':
        return AppColors.warningColor;
      case 'متأخر ومبكر في المغادرة':
        return AppColors.errorColor;
      default:
        return AppColors.textSecondaryColor;
    }
  }
}
