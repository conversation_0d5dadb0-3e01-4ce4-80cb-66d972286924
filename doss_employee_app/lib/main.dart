import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'constants/app_constants.dart';
import 'services/api_service.dart';
import 'services/auth_service.dart';
import 'providers/auth_provider.dart';
import 'providers/attendance_provider.dart';
import 'providers/leave_provider.dart';
import 'providers/payroll_provider.dart';
import 'screens/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize date formatting for Arabic
  await initializeDateFormatting('ar', null);

  // Initialize services
  ApiService().initialize();
  await AuthService().initialize();

  runApp(const DossEmployeeApp());
}

class DossEmployeeApp extends StatelessWidget {
  const DossEmployeeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => AttendanceProvider()),
        ChangeNotifierProvider(create: (_) => LeaveProvider()),
        ChangeNotifierProvider(create: (_) => PayrollProvider()),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,
        theme: _buildTheme(),
        home: const SplashScreen(),
        builder: (context, child) {
          return Directionality(
            textDirection: TextDirection.rtl,
            child: child!,
          );
        },
      ),
    );
  }

  ThemeData _buildTheme() {
    return ThemeData(
      primarySwatch: MaterialColor(
        AppColors.primaryColor,
        <int, Color>{
          50: Color(AppColors.primaryLightColor),
          100: Color(AppColors.primaryLightColor),
          200: Color(AppColors.primaryLightColor),
          300: Color(AppColors.primaryColor),
          400: Color(AppColors.primaryColor),
          500: Color(AppColors.primaryColor),
          600: Color(AppColors.primaryDarkColor),
          700: Color(AppColors.primaryDarkColor),
          800: Color(AppColors.primaryDarkColor),
          900: Color(AppColors.primaryDarkColor),
        },
      ),
      primaryColor: Color(AppColors.primaryColor),
      colorScheme: ColorScheme.fromSeed(
        seedColor: Color(AppColors.primaryColor),
        brightness: Brightness.light,
      ),
      scaffoldBackgroundColor: Color(AppColors.backgroundColor),
      appBarTheme: AppBarTheme(
        backgroundColor: Color(AppColors.primaryColor),
        foregroundColor: Colors.white,
        elevation: AppSizes.appBarElevation,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      cardTheme: CardThemeData(
        color: Color(AppColors.cardColor),
        elevation: AppSizes.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(AppColors.primaryColor),
          foregroundColor: Colors.white,
          minimumSize: const Size(double.infinity, AppSizes.buttonHeightL),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      useMaterial3: true,
    );
  }
}
