class AppConstants {
  // API Configuration
  static const String baseUrl = 'http://your-domain.com/api';
  static const String loginEndpoint = '/login';
  static const String logoutEndpoint = '/logout';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';
  
  // App Info
  static const String appName = 'نظام إدارة الموظفين';
  static const String appVersion = '1.0.0';
  static const String companyName = 'صيدلية دوس';
  
  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayTimeFormat = 'hh:mm a';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int maxNameLength = 100;
  static const int maxEmailLength = 255;
  static const int maxPhoneLength = 20;
  static const int maxAddressLength = 500;
  static const int maxNoteLength = 1000;
  
  // Location
  static const double defaultLocationAccuracy = 100.0; // meters
  static const int locationTimeoutSeconds = 30;
  
  // Refresh Intervals
  static const int dashboardRefreshInterval = 300; // 5 minutes
  static const int attendanceRefreshInterval = 60; // 1 minute
  static const int notificationRefreshInterval = 120; // 2 minutes
}

class ApiEndpoints {
  // Authentication
  static const String login = '/login';
  static const String logout = '/logout';
  static const String refreshToken = '/refresh-token';
  
  // Profile
  static const String profile = '/profile';
  static const String updateProfile = '/profile/update';
  static const String changePassword = '/profile/change-password';
  static const String profileDocuments = '/profile/documents';
  static const String uploadDocument = '/profile/upload-document';
  static const String deleteDocument = '/profile/delete-document';
  static const String profileSummary = '/profile/summary';
  
  // Attendance
  static const String attendanceToday = '/attendance/today';
  static const String attendanceHistory = '/attendance/history';
  static const String attendanceCheckIn = '/attendance/check-in';
  static const String attendanceCheckOut = '/attendance/check-out';
  static const String attendanceSummary = '/attendance/summary';
  
  // Leaves
  static const String leaveBalance = '/leaves/balance';
  static const String leaveHistory = '/leaves/history';
  static const String submitLeaveRequest = '/leaves/submit-request';
  static const String cancelLeaveRequest = '/leaves/cancel-request';
  static const String leaveTypes = '/leaves/types';
  static const String holidays = '/leaves/holidays';
  static const String leaveSummary = '/leaves/summary';
  
  // Payroll
  static const String currentSalary = '/payroll/current-salary';
  static const String payrollHistory = '/payroll/history';
  static const String payrollDetails = '/payroll/details';
  static const String advances = '/payroll/advances';
  static const String requestAdvance = '/payroll/request-advance';
  
  // Employee Affairs
  static const String bonuses = '/employee-affairs/bonuses';
  static const String deductions = '/employee-affairs/deductions';
  static const String transfers = '/employee-affairs/transfers';
  static const String promotions = '/employee-affairs/promotions';
  static const String suggestions = '/employee-affairs/suggestions';
  static const String submitSuggestion = '/employee-affairs/submit-suggestion';
  static const String complaints = '/employee-affairs/complaints';
  static const String submitComplaint = '/employee-affairs/submit-complaint';
  static const String warnings = '/employee-affairs/warnings';
  
  // Insurance
  static const String insuranceDetails = '/insurance/details';
  static const String dependents = '/insurance/dependents';
  static const String addDependent = '/insurance/add-dependent';
  static const String updateDependent = '/insurance/update-dependent';
  static const String deleteDependent = '/insurance/delete-dependent';
  static const String insuranceInstitutions = '/insurance/institutions';
  static const String insuranceSummary = '/insurance/summary';
  
  // Dashboard
  static const String dashboard = '/dashboard';
  static const String notifications = '/notifications';
  static const String notices = '/notices';
}

class AppColors {
  // Primary Colors
  static const int primaryColor = 0xFF2196F3; // Blue
  static const int primaryDarkColor = 0xFF1976D2;
  static const int primaryLightColor = 0xFFBBDEFB;
  
  // Secondary Colors
  static const int secondaryColor = 0xFF4CAF50; // Green
  static const int secondaryDarkColor = 0xFF388E3C;
  static const int secondaryLightColor = 0xFFC8E6C9;
  
  // Status Colors
  static const int successColor = 0xFF4CAF50; // Green
  static const int warningColor = 0xFFFF9800; // Orange
  static const int errorColor = 0xFFF44336; // Red
  static const int infoColor = 0xFF2196F3; // Blue
  
  // Text Colors
  static const int textPrimaryColor = 0xFF212121;
  static const int textSecondaryColor = 0xFF757575;
  static const int textDisabledColor = 0xFFBDBDBD;
  
  // Background Colors
  static const int backgroundColor = 0xFFFAFAFA;
  static const int surfaceColor = 0xFFFFFFFF;
  static const int cardColor = 0xFFFFFFFF;
  
  // Border Colors
  static const int borderColor = 0xFFE0E0E0;
  static const int dividerColor = 0xFFBDBDBD;
  
  // Attendance Status Colors
  static const int presentColor = 0xFF4CAF50; // Green
  static const int absentColor = 0xFFF44336; // Red
  static const int lateColor = 0xFFFF9800; // Orange
  static const int overtimeColor = 0xFF9C27B0; // Purple
  
  // Leave Status Colors
  static const int approvedLeaveColor = 0xFF4CAF50; // Green
  static const int pendingLeaveColor = 0xFFFF9800; // Orange
  static const int rejectedLeaveColor = 0xFFF44336; // Red
  
  // Payroll Colors
  static const int salaryColor = 0xFF4CAF50; // Green
  static const int bonusColor = 0xFF2196F3; // Blue
  static const int deductionColor = 0xFFF44336; // Red
  static const int advanceColor = 0xFFFF9800; // Orange
}

class AppSizes {
  // Padding & Margins
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // Border Radius
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  
  // Icon Sizes
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  
  // Button Heights
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;
  
  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 4.0;
  
  // Cards
  static const double cardElevation = 2.0;
  static const double cardRadius = 8.0;
  
  // Bottom Navigation
  static const double bottomNavHeight = 60.0;
  
  // List Items
  static const double listItemHeight = 56.0;
  static const double listItemPadding = 16.0;
}

class AppStrings {
  // Common
  static const String ok = 'موافق';
  static const String cancel = 'إلغاء';
  static const String save = 'حفظ';
  static const String delete = 'حذف';
  static const String edit = 'تعديل';
  static const String add = 'إضافة';
  static const String search = 'بحث';
  static const String filter = 'تصفية';
  static const String refresh = 'تحديث';
  static const String loading = 'جاري التحميل...';
  static const String noData = 'لا توجد بيانات';
  static const String error = 'خطأ';
  static const String success = 'نجح';
  static const String warning = 'تحذير';
  static const String info = 'معلومات';
  
  // Authentication
  static const String login = 'تسجيل الدخول';
  static const String logout = 'تسجيل الخروج';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  static const String rememberMe = 'تذكرني';
  
  // Navigation
  static const String dashboard = 'لوحة التحكم';
  static const String attendance = 'الحضور والانصراف';
  static const String leaves = 'الإجازات';
  static const String payroll = 'الرواتب';
  static const String profile = 'الملف الشخصي';
  static const String affairs = 'شؤون الموظفين';
  
  // Attendance
  static const String checkIn = 'تسجيل الدخول';
  static const String checkOut = 'تسجيل الخروج';
  static const String workingHours = 'ساعات العمل';
  static const String overtime = 'العمل الإضافي';
  static const String lateArrival = 'التأخير';
  static const String earlyDeparture = 'المغادرة المبكرة';
  
  // Leaves
  static const String leaveRequest = 'طلب إجازة';
  static const String leaveBalance = 'رصيد الإجازات';
  static const String leaveHistory = 'تاريخ الإجازات';
  static const String leaveType = 'نوع الإجازة';
  static const String leaveFrom = 'من تاريخ';
  static const String leaveTo = 'إلى تاريخ';
  static const String reason = 'السبب';
  
  // Payroll
  static const String salary = 'الراتب';
  static const String basicSalary = 'الراتب الأساسي';
  static const String allowances = 'البدلات';
  static const String deductions = 'الخصومات';
  static const String netSalary = 'الراتب الصافي';
  static const String advance = 'سلفة';
  static const String requestAdvance = 'طلب سلفة';
  
  // Profile
  static const String personalInfo = 'المعلومات الشخصية';
  static const String employmentInfo = 'معلومات التوظيف';
  static const String documents = 'المستندات';
  static const String changePassword = 'تغيير كلمة المرور';
  
  // Validation Messages
  static const String fieldRequired = 'هذا الحقل مطلوب';
  static const String invalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String passwordsDoNotMatch = 'كلمات المرور غير متطابقة';
  static const String invalidPhoneNumber = 'رقم الهاتف غير صحيح';
  
  // Error Messages
  static const String networkError = 'خطأ في الاتصال بالشبكة';
  static const String serverError = 'خطأ في الخادم';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String sessionExpired = 'انتهت صلاحية الجلسة';
  static const String unauthorized = 'غير مصرح لك بالوصول';
  static const String notFound = 'البيانات غير موجودة';
}
