import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/payroll.dart';

class SalaryOverviewCard extends StatelessWidget {
  final Salary? salary;
  final bool isLoading;

  const SalaryOverviewCard({
    super.key,
    this.salary,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(AppSizes.paddingL),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (salary == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingL),
          child: Column(
            children: [
              Icon(
                Icons.account_balance_wallet_outlined,
                size: 48,
                color: Color(AppColors.textDisabledColor),
              ),
              const SizedBox(height: AppSizes.paddingM),
              Text(
                'لا توجد بيانات راتب متاحة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Color(AppColors.textSecondaryColor),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(AppColors.primaryColor),
              Color(AppColors.primaryDarkColor),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.paddingS),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: AppSizes.iconM,
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'الراتب الحالي',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          salary!.formattedTotalSalary,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // Salary Breakdown
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Column(
                  children: [
                    _buildBreakdownRow(
                      'الراتب الأساسي',
                      salary!.formattedBasicSalary,
                      Icons.account_balance,
                    ),
                    const SizedBox(height: AppSizes.paddingS),
                    _buildBreakdownRow(
                      'البدلات',
                      salary!.formattedAllowances,
                      Icons.add_circle,
                    ),
                    const SizedBox(height: AppSizes.paddingS),
                    _buildBreakdownRow(
                      'الاستقطاعات',
                      salary!.formattedDeductions,
                      Icons.remove_circle,
                    ),
                    const Divider(color: Colors.white30, height: 20),
                    _buildBreakdownRow(
                      'صافي الراتب',
                      salary!.formattedNetSalary,
                      Icons.account_balance_wallet,
                      isTotal: true,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Additional Info
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      'تاريخ الانضمام',
                      salary!.formattedJoinDate,
                      Icons.calendar_today,
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: _buildInfoItem(
                      'المنصب',
                      salary!.position ?? 'غير محدد',
                      Icons.work,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBreakdownRow(
    String label,
    String value,
    IconData icon, {
    bool isTotal = false,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.white70,
          size: AppSizes.iconS,
        ),
        const SizedBox(width: AppSizes.paddingS),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              color: Colors.white70,
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Colors.white70,
                size: AppSizes.iconS,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
