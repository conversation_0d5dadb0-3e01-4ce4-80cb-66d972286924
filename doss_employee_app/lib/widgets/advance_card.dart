import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/payroll.dart';

class AdvanceCard extends StatelessWidget {
  final Advance advance;
  final VoidCallback? onTap;

  const AdvanceCard({
    super.key,
    required this.advance,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final progress = _calculateProgress();
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with amount and status
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.paddingS),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    ),
                    child: Icon(
                      _getStatusIcon(),
                      color: _getStatusColor(),
                      size: AppSizes.iconM,
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          advance.formattedAmount,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Color(AppColors.primaryColor),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor().withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusL),
                          ),
                          child: Text(
                            _getStatusText(),
                            style: TextStyle(
                              color: _getStatusColor(),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${advance.installments} قسط',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        advance.formattedMonthlyInstallment,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Color(AppColors.textSecondaryColor),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Progress Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'تقدم السداد',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${(progress * 100).toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _getProgressColor(progress),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSizes.paddingS),
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Color(AppColors.backgroundColor),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getProgressColor(progress),
                    ),
                    minHeight: 8,
                  ),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Payment Details
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  color: Color(AppColors.backgroundColor),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildPaymentItem(
                        context,
                        'المدفوع',
                        advance.formattedPaidAmount,
                        Icons.check_circle,
                        Color(AppColors.successColor),
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 40,
                      color: Color(AppColors.dividerColor),
                      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
                    ),
                    Expanded(
                      child: _buildPaymentItem(
                        context,
                        'المتبقي',
                        advance.formattedRemainingAmount,
                        Icons.pending,
                        Color(AppColors.warningColor),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Request and Approval Dates
              const SizedBox(height: AppSizes.paddingM),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'طُلبت في: ${advance.formattedRequestDate}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Color(AppColors.textDisabledColor),
                    ),
                  ),
                  if (advance.isApproved && advance.approvedDate != null)
                    Text(
                      'وُوفق عليها: ${advance.formattedApprovedDate}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Color(AppColors.successColor),
                      ),
                    ),
                ],
              ),
              
              // Reason (if available)
              if (advance.reason?.isNotEmpty == true) ...[
                const SizedBox(height: AppSizes.paddingS),
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    color: Color(AppColors.infoColor).withOpacity(0.05),
                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    border: Border.all(
                      color: Color(AppColors.infoColor).withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.note,
                        size: AppSizes.iconS,
                        color: Color(AppColors.infoColor),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Expanded(
                        child: Text(
                          advance.reason!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Color(AppColors.textSecondaryColor),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Next Payment Date (if active)
              if (advance.isActive && advance.nextPaymentDate != null) ...[
                const SizedBox(height: AppSizes.paddingS),
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    color: Color(AppColors.warningColor).withOpacity(0.05),
                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    border: Border.all(
                      color: Color(AppColors.warningColor).withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: AppSizes.iconS,
                        color: Color(AppColors.warningColor),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Text(
                        'القسط القادم: ${advance.formattedNextPaymentDate}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Color(AppColors.warningColor),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: AppSizes.iconM,
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Color(AppColors.textSecondaryColor),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  double _calculateProgress() {
    if (advance.amount <= 0) return 0.0;
    return advance.paidAmount / advance.amount;
  }

  Color _getProgressColor(double progress) {
    if (progress >= 1.0) {
      return Color(AppColors.successColor);
    } else if (progress >= 0.5) {
      return Color(AppColors.warningColor);
    } else {
      return Color(AppColors.infoColor);
    }
  }

  Color _getStatusColor() {
    switch (advance.status?.toLowerCase()) {
      case 'approved':
      case 'active':
      case 'معتمد':
      case 'نشط':
        return Color(AppColors.successColor);
      case 'pending':
      case 'في الانتظار':
        return Color(AppColors.warningColor);
      case 'rejected':
      case 'مرفوض':
        return Color(AppColors.errorColor);
      case 'completed':
      case 'مكتمل':
        return Color(AppColors.infoColor);
      default:
        return Color(AppColors.textSecondaryColor);
    }
  }

  IconData _getStatusIcon() {
    switch (advance.status?.toLowerCase()) {
      case 'approved':
      case 'active':
      case 'معتمد':
      case 'نشط':
        return Icons.trending_down;
      case 'pending':
      case 'في الانتظار':
        return Icons.pending;
      case 'rejected':
      case 'مرفوض':
        return Icons.cancel;
      case 'completed':
      case 'مكتمل':
        return Icons.check_circle;
      default:
        return Icons.help;
    }
  }

  String _getStatusText() {
    switch (advance.status?.toLowerCase()) {
      case 'approved':
      case 'active':
        return 'نشط';
      case 'pending':
        return 'في الانتظار';
      case 'rejected':
        return 'مرفوض';
      case 'completed':
        return 'مكتمل';
      default:
        return advance.status ?? 'غير محدد';
    }
  }
}
