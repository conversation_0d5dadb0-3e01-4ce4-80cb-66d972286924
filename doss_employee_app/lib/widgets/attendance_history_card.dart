import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/attendance.dart';

class AttendanceHistoryCard extends StatelessWidget {
  final Attendance attendance;
  final VoidCallback? onTap;

  const AttendanceHistoryCard({
    super.key,
    required this.attendance,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingS),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Column(
            children: [
              // Date and Status Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: AppSizes.iconS,
                        color: Color(AppColors.primaryColor),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Text(
                        _formatDate(attendance.date),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSizes.paddingS,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusL),
                    ),
                    child: Text(
                      attendance.status ?? 'غير محدد',
                      style: TextStyle(
                        color: _getStatusColor(),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Time Information
              Row(
                children: [
                  Expanded(
                    child: _buildTimeInfo(
                      context,
                      'الدخول',
                      attendance.checkInTime ?? '--:--',
                      Icons.login,
                      attendance.hasCheckedIn,
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingS),
                  Expanded(
                    child: _buildTimeInfo(
                      context,
                      'الخروج',
                      attendance.checkOutTime ?? '--:--',
                      Icons.logout,
                      attendance.hasCheckedOut,
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingS),
                  Expanded(
                    child: _buildTimeInfo(
                      context,
                      'ساعات العمل',
                      '${attendance.workingHours.toStringAsFixed(1)}س',
                      Icons.access_time,
                      attendance.workingHours > 0,
                    ),
                  ),
                ],
              ),
              
              // Additional Info (if any)
              if (attendance.isLate || attendance.overtime > 0 || (attendance.note?.isNotEmpty ?? false))
                ...[
                  const SizedBox(height: AppSizes.paddingM),
                  const Divider(height: 1),
                  const SizedBox(height: AppSizes.paddingS),
                  _buildAdditionalInfo(),
                ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeInfo(
    BuildContext context,
    String label,
    String time,
    IconData icon,
    bool isActive,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      decoration: BoxDecoration(
        color: isActive 
            ? Color(AppColors.primaryColor).withOpacity(0.05)
            : Color(AppColors.textDisabledColor).withOpacity(0.05),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
        border: Border.all(
          color: isActive 
              ? Color(AppColors.primaryColor).withOpacity(0.2)
              : Color(AppColors.textDisabledColor).withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: AppSizes.iconS,
            color: isActive 
                ? Color(AppColors.primaryColor)
                : Color(AppColors.textDisabledColor),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
              fontSize: 10,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            time,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: isActive 
                  ? Color(AppColors.primaryColor)
                  : Color(AppColors.textDisabledColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    final List<Widget> infoChips = [];
    
    if (attendance.isLate) {
      infoChips.add(_buildInfoChip(
        'متأخر',
        Icons.schedule,
        Color(AppColors.warningColor),
      ));
    }
    
    if (attendance.overtime > 0) {
      infoChips.add(_buildInfoChip(
        'عمل إضافي: ${attendance.overtime}د',
        Icons.timer,
        Color(AppColors.infoColor),
      ));
    }
    
    if (attendance.note?.isNotEmpty ?? false) {
      infoChips.add(_buildInfoChip(
        'ملاحظة',
        Icons.note,
        Color(AppColors.secondaryColor),
      ));
    }
    
    return Wrap(
      spacing: AppSizes.paddingS,
      runSpacing: AppSizes.paddingS,
      children: infoChips,
    );
  }

  Widget _buildInfoChip(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingS,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (attendance.status?.toLowerCase()) {
      case 'حاضر':
      case 'حضور منتظم':
        return Color(AppColors.successColor);
      case 'متأخر':
        return Color(AppColors.warningColor);
      case 'غائب':
        return Color(AppColors.errorColor);
      case 'مبكر في المغادرة':
        return Color(AppColors.warningColor);
      default:
        return Color(AppColors.textSecondaryColor);
    }
  }

  String _formatDate(DateTime date) {
    const weekdays = [
      '', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    final weekday = weekdays[date.weekday];
    final month = months[date.month];
    
    return '$weekday ${date.day} $month ${date.year}';
  }
}
