import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/payroll.dart';

class PayrollHistoryCard extends StatelessWidget {
  final Payroll payroll;
  final VoidCallback? onTap;

  const PayrollHistoryCard({
    super.key,
    required this.payroll,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingS),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with period and status
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.paddingS),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    ),
                    child: Icon(
                      _getStatusIcon(),
                      color: _getStatusColor(),
                      size: AppSizes.iconM,
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          payroll.period,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor().withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusL),
                          ),
                          child: Text(
                            _getStatusText(),
                            style: TextStyle(
                              color: _getStatusColor(),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        payroll.formattedNetSalary,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Color(AppColors.primaryColor),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'صافي الراتب',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Color(AppColors.textSecondaryColor),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Salary Breakdown
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  color: Color(AppColors.backgroundColor),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildSalaryItem(
                            context,
                            'الراتب الأساسي',
                            payroll.formattedBasicSalary,
                            Icons.account_balance,
                            Color(AppColors.infoColor),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Expanded(
                          child: _buildSalaryItem(
                            context,
                            'البدلات',
                            payroll.formattedAllowances,
                            Icons.add_circle,
                            Color(AppColors.successColor),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingS),
                    Row(
                      children: [
                        Expanded(
                          child: _buildSalaryItem(
                            context,
                            'العمل الإضافي',
                            payroll.formattedOvertimeAmount,
                            Icons.timer,
                            Color(AppColors.warningColor),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Expanded(
                          child: _buildSalaryItem(
                            context,
                            'الاستقطاعات',
                            payroll.formattedTotalDeductions,
                            Icons.remove_circle,
                            Color(AppColors.errorColor),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Additional Info
              if (payroll.bonusAmount > 0 || payroll.advanceAmount > 0) ...[
                const SizedBox(height: AppSizes.paddingM),
                Wrap(
                  spacing: AppSizes.paddingS,
                  runSpacing: AppSizes.paddingS,
                  children: [
                    if (payroll.bonusAmount > 0)
                      _buildInfoChip(
                        'مكافأة: ${payroll.formattedBonusAmount}',
                        Icons.star,
                        Color(AppColors.successColor),
                      ),
                    if (payroll.advanceAmount > 0)
                      _buildInfoChip(
                        'سلفة: ${payroll.formattedAdvanceAmount}',
                        Icons.trending_down,
                        Color(AppColors.warningColor),
                      ),
                  ],
                ),
              ],
              
              // Payment Date
              const SizedBox(height: AppSizes.paddingS),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تاريخ الدفع: ${payroll.formattedPaymentDate}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Color(AppColors.textDisabledColor),
                    ),
                  ),
                  if (onTap != null)
                    Icon(
                      Icons.arrow_forward_ios,
                      size: AppSizes.iconXS,
                      color: Color(AppColors.textDisabledColor),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSalaryItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: AppSizes.iconS,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingS,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (payroll.status?.toLowerCase()) {
      case 'paid':
      case 'مدفوع':
        return Color(AppColors.successColor);
      case 'pending':
      case 'في الانتظار':
        return Color(AppColors.warningColor);
      case 'processing':
      case 'قيد المعالجة':
        return Color(AppColors.infoColor);
      case 'cancelled':
      case 'ملغي':
        return Color(AppColors.errorColor);
      default:
        return Color(AppColors.textSecondaryColor);
    }
  }

  IconData _getStatusIcon() {
    switch (payroll.status?.toLowerCase()) {
      case 'paid':
      case 'مدفوع':
        return Icons.check_circle;
      case 'pending':
      case 'في الانتظار':
        return Icons.pending;
      case 'processing':
      case 'قيد المعالجة':
        return Icons.hourglass_empty;
      case 'cancelled':
      case 'ملغي':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText() {
    switch (payroll.status?.toLowerCase()) {
      case 'paid':
        return 'مدفوع';
      case 'pending':
        return 'في الانتظار';
      case 'processing':
        return 'قيد المعالجة';
      case 'cancelled':
        return 'ملغي';
      default:
        return payroll.status ?? 'غير محدد';
    }
  }
}
