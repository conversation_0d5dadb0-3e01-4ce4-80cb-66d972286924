import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/leave.dart';

class LeaveHistoryCard extends StatelessWidget {
  final Leave leave;
  final VoidCallback? onTap;
  final VoidCallback? onCancel;

  const LeaveHistoryCard({
    super.key,
    required this.leave,
    this.onTap,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingS),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with leave type and status
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.paddingS),
                    decoration: BoxDecoration(
                      color: _getLeaveTypeColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    ),
                    child: Text(
                      _getLeaveTypeIcon(),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          leave.leaveTypeName ?? 'إجازة',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${leave.numberOfDays} ${_getDaysText(leave.numberOfDays)}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Color(AppColors.textSecondaryColor),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSizes.paddingS,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusL),
                    ),
                    child: Text(
                      _getStatusText(),
                      style: TextStyle(
                        color: _getStatusColor(),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Date Range
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingS),
                decoration: BoxDecoration(
                  color: Color(AppColors.backgroundColor),
                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.date_range,
                      size: AppSizes.iconS,
                      color: Color(AppColors.primaryColor),
                    ),
                    const SizedBox(width: AppSizes.paddingS),
                    Expanded(
                      child: Text(
                        '${_formatDate(leave.leaveFrom)} - ${_formatDate(leave.leaveTo)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Reason (if available)
              if (leave.reason?.isNotEmpty == true) ...[
                const SizedBox(height: AppSizes.paddingS),
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    color: Color(AppColors.infoColor).withOpacity(0.05),
                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    border: Border.all(
                      color: Color(AppColors.infoColor).withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.note,
                        size: AppSizes.iconS,
                        color: Color(AppColors.infoColor),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Expanded(
                        child: Text(
                          leave.reason!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Color(AppColors.textSecondaryColor),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Manager Note (if available)
              if (leave.managerNote?.isNotEmpty == true) ...[
                const SizedBox(height: AppSizes.paddingS),
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withOpacity(0.05),
                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    border: Border.all(
                      color: _getStatusColor().withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.supervisor_account,
                        size: AppSizes.iconS,
                        color: _getStatusColor(),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'ملاحظة المدير:',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: _getStatusColor(),
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              leave.managerNote!,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Color(AppColors.textSecondaryColor),
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Actions (if available)
              if (onCancel != null && leave.isPending) ...[
                const SizedBox(height: AppSizes.paddingM),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: onCancel,
                      icon: const Icon(Icons.cancel, size: 16),
                      label: const Text('إلغاء الطلب'),
                      style: TextButton.styleFrom(
                        foregroundColor: Color(AppColors.errorColor),
                      ),
                    ),
                  ],
                ),
              ],
              
              // Request Date
              const SizedBox(height: AppSizes.paddingS),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تاريخ الطلب: ${_formatDate(leave.leaveRequestedDate)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Color(AppColors.textDisabledColor),
                    ),
                  ),
                  if (leave.isApproved && leave.approvedDate != null)
                    Text(
                      'تاريخ الموافقة: ${_formatDate(leave.approvedDate!)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Color(AppColors.successColor),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getLeaveTypeColor() {
    switch (leave.leaveTypeName?.toLowerCase()) {
      case 'إجازة سنوية':
      case 'annual':
        return Color(AppColors.successColor);
      case 'إجازة مرضية':
      case 'sick':
        return Color(AppColors.errorColor);
      case 'إجازة طارئة':
      case 'emergency':
        return Color(AppColors.warningColor);
      case 'إجازة أمومة':
      case 'maternity':
        return Color(AppColors.secondaryColor);
      default:
        return Color(AppColors.primaryColor);
    }
  }

  String _getLeaveTypeIcon() {
    switch (leave.leaveTypeName?.toLowerCase()) {
      case 'إجازة سنوية':
      case 'annual':
        return '🏖️';
      case 'إجازة مرضية':
      case 'sick':
        return '🏥';
      case 'إجازة طارئة':
      case 'emergency':
        return '🚨';
      case 'إجازة أمومة':
      case 'maternity':
        return '👶';
      case 'إجازة أبوة':
      case 'paternity':
        return '👨‍👶';
      case 'إجازة حج':
      case 'hajj':
        return '🕋';
      default:
        return '📅';
    }
  }

  Color _getStatusColor() {
    switch (leave.status?.toLowerCase()) {
      case 'approved':
      case 'معتمد':
        return Color(AppColors.successColor);
      case 'pending':
      case 'في الانتظار':
        return Color(AppColors.warningColor);
      case 'rejected':
      case 'مرفوض':
        return Color(AppColors.errorColor);
      case 'cancelled':
      case 'ملغي':
        return Color(AppColors.textSecondaryColor);
      default:
        return Color(AppColors.textSecondaryColor);
    }
  }

  String _getStatusText() {
    switch (leave.status?.toLowerCase()) {
      case 'approved':
        return 'معتمد';
      case 'pending':
        return 'في الانتظار';
      case 'rejected':
        return 'مرفوض';
      case 'cancelled':
        return 'ملغي';
      default:
        return leave.status ?? 'غير محدد';
    }
  }

  String _getDaysText(int days) {
    if (days == 1) return 'يوم';
    if (days == 2) return 'يومان';
    if (days <= 10) return 'أيام';
    return 'يوماً';
  }

  String _formatDate(DateTime date) {
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    final month = months[date.month];
    return '${date.day} $month';
  }
}
