import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/leave.dart';

class LeaveBalanceCard extends StatelessWidget {
  final LeaveBalance balance;
  final VoidCallback? onRequestLeave;

  const LeaveBalanceCard({
    super.key,
    required this.balance,
    this.onRequestLeave,
  });

  @override
  Widget build(BuildContext context) {
    final usagePercentage = balance.allocatedDays > 0 
        ? (balance.usedDays / balance.allocatedDays) * 100 
        : 0.0;

    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with leave type and icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    color: _getLeaveTypeColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                  ),
                  child: Text(
                    _getLeaveTypeIcon(),
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
                const SizedBox(width: AppSizes.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        balance.leaveTypeName,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رصيد ${balance.year}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Color(AppColors.textSecondaryColor),
                        ),
                      ),
                    ],
                  ),
                ),
                if (onRequestLeave != null && balance.remainingDays > 0)
                  IconButton(
                    onPressed: onRequestLeave,
                    icon: const Icon(Icons.add_circle),
                    color: Color(AppColors.primaryColor),
                  ),
              ],
            ),
            
            const SizedBox(height: AppSizes.paddingM),
            
            // Balance Information
            Row(
              children: [
                Expanded(
                  child: _buildBalanceItem(
                    context,
                    'المخصص',
                    '${balance.allocatedDays}',
                    Icons.calendar_month,
                    Color(AppColors.infoColor),
                  ),
                ),
                Expanded(
                  child: _buildBalanceItem(
                    context,
                    'المستخدم',
                    '${balance.usedDays}',
                    Icons.check_circle,
                    Color(AppColors.warningColor),
                  ),
                ),
                Expanded(
                  child: _buildBalanceItem(
                    context,
                    'المتبقي',
                    '${balance.remainingDays}',
                    Icons.account_balance_wallet,
                    Color(AppColors.successColor),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSizes.paddingM),
            
            // Progress Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'معدل الاستخدام',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${usagePercentage.toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getUsageColor(usagePercentage),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppSizes.paddingS),
                LinearProgressIndicator(
                  value: usagePercentage / 100,
                  backgroundColor: Color(AppColors.backgroundColor),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getUsageColor(usagePercentage),
                  ),
                  minHeight: 8,
                ),
              ],
            ),
            
            // Additional Info
            if (balance.pendingDays > 0 || balance.note?.isNotEmpty == true) ...[
              const SizedBox(height: AppSizes.paddingM),
              const Divider(height: 1),
              const SizedBox(height: AppSizes.paddingS),
              _buildAdditionalInfo(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: AppSizes.iconM,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo(BuildContext context) {
    final List<Widget> infoItems = [];
    
    if (balance.pendingDays > 0) {
      infoItems.add(_buildInfoChip(
        'في الانتظار: ${balance.pendingDays} أيام',
        Icons.pending_actions,
        Color(AppColors.warningColor),
      ));
    }
    
    if (balance.note?.isNotEmpty == true) {
      infoItems.add(_buildInfoChip(
        'ملاحظة',
        Icons.info,
        Color(AppColors.infoColor),
      ));
    }
    
    return Wrap(
      spacing: AppSizes.paddingS,
      runSpacing: AppSizes.paddingS,
      children: infoItems,
    );
  }

  Widget _buildInfoChip(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingS,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getLeaveTypeColor() {
    switch (balance.leaveTypeName.toLowerCase()) {
      case 'إجازة سنوية':
      case 'annual':
        return Color(AppColors.successColor);
      case 'إجازة مرضية':
      case 'sick':
        return Color(AppColors.errorColor);
      case 'إجازة طارئة':
      case 'emergency':
        return Color(AppColors.warningColor);
      case 'إجازة أمومة':
      case 'maternity':
        return Color(AppColors.secondaryColor);
      default:
        return Color(AppColors.primaryColor);
    }
  }

  String _getLeaveTypeIcon() {
    switch (balance.leaveTypeName.toLowerCase()) {
      case 'إجازة سنوية':
      case 'annual':
        return '🏖️';
      case 'إجازة مرضية':
      case 'sick':
        return '🏥';
      case 'إجازة طارئة':
      case 'emergency':
        return '🚨';
      case 'إجازة أمومة':
      case 'maternity':
        return '👶';
      case 'إجازة أبوة':
      case 'paternity':
        return '👨‍👶';
      case 'إجازة حج':
      case 'hajj':
        return '🕋';
      default:
        return '📅';
    }
  }

  Color _getUsageColor(double percentage) {
    if (percentage <= 50) {
      return Color(AppColors.successColor);
    } else if (percentage <= 80) {
      return Color(AppColors.warningColor);
    } else {
      return Color(AppColors.errorColor);
    }
  }
}
