import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/attendance.dart';

class AttendanceStatusCard extends StatelessWidget {
  final TodayAttendance? todayAttendance;
  final bool isLoading;
  final VoidCallback onCheckIn;
  final VoidCallback onCheckOut;

  const AttendanceStatusCard({
    super.key,
    this.todayAttendance,
    required this.isLoading,
    required this.onCheckIn,
    required this.onCheckOut,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: Color(AppColors.primaryColor),
                  size: AppSizes.iconM,
                ),
                const SizedBox(width: AppSizes.paddingS),
                Text(
                  'حالة الحضور اليوم',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingM),
            
            if (isLoading)
              const Center(
                child: CircularProgressIndicator(),
              )
            else if (todayAttendance != null)
              _buildAttendanceInfo(context)
            else
              _buildNoDataInfo(context),
            
            const SizedBox(height: AppSizes.paddingM),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceInfo(BuildContext context) {
    final attendance = todayAttendance!;
    
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildTimeInfo(
                context,
                'تسجيل الدخول',
                attendance.checkInTime ?? '--:--',
                attendance.hasCheckedIn,
              ),
            ),
            const SizedBox(width: AppSizes.paddingM),
            Expanded(
              child: _buildTimeInfo(
                context,
                'تسجيل الخروج',
                attendance.checkOutTime ?? '--:--',
                attendance.hasCheckedOut,
              ),
            ),
          ],
        ),
        
        if (attendance.hasCheckedIn) ...[
          const SizedBox(height: AppSizes.paddingM),
          Row(
            children: [
              Expanded(
                child: _buildStatusInfo(
                  context,
                  'ساعات العمل',
                  '${attendance.workingHours.toStringAsFixed(1)} ساعة',
                  Color(AppColors.infoColor),
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: _buildStatusInfo(
                  context,
                  'الحالة',
                  attendance.status,
                  _getStatusColor(attendance.status),
                ),
              ),
            ],
          ),
        ],
        
        if (attendance.officeHours != null) ...[
          const SizedBox(height: AppSizes.paddingM),
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingS),
            decoration: BoxDecoration(
              color: Color(AppColors.primaryLightColor).withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppSizes.radiusS),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: AppSizes.iconS,
                  color: Color(AppColors.primaryColor),
                ),
                const SizedBox(width: AppSizes.paddingS),
                Text(
                  'ساعات العمل: ${attendance.officeHours!.openingTime} - ${attendance.officeHours!.closingTime}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Color(AppColors.primaryColor),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeInfo(BuildContext context, String label, String time, bool isActive) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      decoration: BoxDecoration(
        color: isActive 
            ? Color(AppColors.successColor).withOpacity(0.1)
            : Color(AppColors.textDisabledColor).withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            time,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isActive 
                  ? Color(AppColors.successColor)
                  : Color(AppColors.textDisabledColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusInfo(BuildContext context, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      decoration: BoxDecoration(
        color: Color(AppColors.textDisabledColor).withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Color(AppColors.textSecondaryColor),
          ),
          const SizedBox(width: AppSizes.paddingS),
          Expanded(
            child: Text(
              'لم يتم تسجيل أي حضور اليوم',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Color(AppColors.textSecondaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final canCheckIn = todayAttendance?.canCheckIn ?? true;
    final canCheckOut = todayAttendance?.canCheckOut ?? false;

    return Row(
      children: [
        if (canCheckIn)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: isLoading ? null : onCheckIn,
              icon: const Icon(Icons.login),
              label: const Text('تسجيل الدخول'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(AppColors.successColor),
              ),
            ),
          ),
        
        if (canCheckIn && canCheckOut)
          const SizedBox(width: AppSizes.paddingS),
        
        if (canCheckOut)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: isLoading ? null : onCheckOut,
              icon: const Icon(Icons.logout),
              label: const Text('تسجيل الخروج'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(AppColors.warningColor),
              ),
            ),
          ),
        
        if (!canCheckIn && !canCheckOut)
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: Color(AppColors.successColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Color(AppColors.successColor),
                  ),
                  const SizedBox(width: AppSizes.paddingS),
                  Text(
                    'تم إكمال الحضور اليوم',
                    style: TextStyle(
                      color: Color(AppColors.successColor),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'حاضر':
      case 'حضور منتظم':
        return Color(AppColors.presentColor);
      case 'متأخر':
        return Color(AppColors.lateColor);
      case 'مبكر في المغادرة':
        return Color(AppColors.warningColor);
      default:
        return Color(AppColors.textSecondaryColor);
    }
  }
}
