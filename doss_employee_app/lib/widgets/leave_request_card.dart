import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/leave.dart';

class LeaveRequestCard extends StatelessWidget {
  final Leave leave;
  final VoidCallback? onCancel;
  final bool showActions;

  const LeaveRequestCard({
    super.key,
    required this.leave,
    this.onCancel,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                  ),
                  child: Icon(
                    _getStatusIcon(),
                    color: _getStatusColor(),
                    size: AppSizes.iconM,
                  ),
                ),
                const SizedBox(width: AppSizes.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        leave.leaveTypeName ?? 'إجازة',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingS,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(AppSizes.radiusL),
                        ),
                        child: Text(
                          _getStatusText(),
                          style: TextStyle(
                            color: _getStatusColor(),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${leave.numberOfDays} ${_getDaysText(leave.numberOfDays)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Color(AppColors.primaryColor),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSizes.paddingM),
            
            // Date Range
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              decoration: BoxDecoration(
                color: Color(AppColors.backgroundColor),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: _buildDateInfo(
                      context,
                      'من',
                      leave.leaveFrom,
                      Icons.play_arrow,
                    ),
                  ),
                  Container(
                    width: 2,
                    height: 30,
                    color: Color(AppColors.dividerColor),
                    margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
                  ),
                  Expanded(
                    child: _buildDateInfo(
                      context,
                      'إلى',
                      leave.leaveTo,
                      Icons.stop,
                    ),
                  ),
                ],
              ),
            ),
            
            // Reason
            if (leave.reason?.isNotEmpty == true) ...[
              const SizedBox(height: AppSizes.paddingM),
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  color: Color(AppColors.infoColor).withOpacity(0.05),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                  border: Border.all(
                    color: Color(AppColors.infoColor).withOpacity(0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.note,
                          size: AppSizes.iconS,
                          color: Color(AppColors.infoColor),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Text(
                          'سبب الإجازة:',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Color(AppColors.infoColor),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingS),
                    Text(
                      leave.reason!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Color(AppColors.textSecondaryColor),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Manager Note (if rejected or has note)
            if (leave.managerNote?.isNotEmpty == true) ...[
              const SizedBox(height: AppSizes.paddingM),
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  color: _getStatusColor().withOpacity(0.05),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                  border: Border.all(
                    color: _getStatusColor().withOpacity(0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.supervisor_account,
                          size: AppSizes.iconS,
                          color: _getStatusColor(),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Text(
                          'ملاحظة المدير:',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: _getStatusColor(),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingS),
                    Text(
                      leave.managerNote!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Color(AppColors.textSecondaryColor),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Footer with request date and actions
            const SizedBox(height: AppSizes.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'طُلبت في: ${_formatDate(leave.leaveRequestedDate)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Color(AppColors.textDisabledColor),
                  ),
                ),
                if (showActions && onCancel != null && leave.isPending)
                  TextButton.icon(
                    onPressed: onCancel,
                    icon: const Icon(Icons.cancel, size: 16),
                    label: const Text('إلغاء'),
                    style: TextButton.styleFrom(
                      foregroundColor: Color(AppColors.errorColor),
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingS,
                        vertical: 4,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateInfo(
    BuildContext context,
    String label,
    DateTime date,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: AppSizes.iconS,
              color: Color(AppColors.primaryColor),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color(AppColors.textSecondaryColor),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          _formatDate(date),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          _getWeekday(date),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Color(AppColors.textSecondaryColor),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (leave.status?.toLowerCase()) {
      case 'approved':
      case 'معتمد':
        return Color(AppColors.successColor);
      case 'pending':
      case 'في الانتظار':
        return Color(AppColors.warningColor);
      case 'rejected':
      case 'مرفوض':
        return Color(AppColors.errorColor);
      case 'cancelled':
      case 'ملغي':
        return Color(AppColors.textSecondaryColor);
      default:
        return Color(AppColors.textSecondaryColor);
    }
  }

  IconData _getStatusIcon() {
    switch (leave.status?.toLowerCase()) {
      case 'approved':
      case 'معتمد':
        return Icons.check_circle;
      case 'pending':
      case 'في الانتظار':
        return Icons.pending;
      case 'rejected':
      case 'مرفوض':
        return Icons.cancel;
      case 'cancelled':
      case 'ملغي':
        return Icons.block;
      default:
        return Icons.help;
    }
  }

  String _getStatusText() {
    switch (leave.status?.toLowerCase()) {
      case 'approved':
        return 'معتمد';
      case 'pending':
        return 'في الانتظار';
      case 'rejected':
        return 'مرفوض';
      case 'cancelled':
        return 'ملغي';
      default:
        return leave.status ?? 'غير محدد';
    }
  }

  String _getDaysText(int days) {
    if (days == 1) return 'يوم';
    if (days == 2) return 'يومان';
    if (days <= 10) return 'أيام';
    return 'يوماً';
  }

  String _formatDate(DateTime date) {
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    final month = months[date.month];
    return '${date.day} $month ${date.year}';
  }

  String _getWeekday(DateTime date) {
    const weekdays = [
      '', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];
    return weekdays[date.weekday];
  }
}
