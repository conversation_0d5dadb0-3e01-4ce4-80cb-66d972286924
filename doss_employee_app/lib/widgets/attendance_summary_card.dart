import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/attendance.dart';

class AttendanceSummaryCard extends StatelessWidget {
  final AttendanceSummary? summary;
  final bool isLoading;

  const AttendanceSummaryCard({
    super.key,
    this.summary,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(AppSizes.paddingL),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (summary == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingL),
          child: Column(
            children: [
              Icon(
                Icons.analytics_outlined,
                size: 48,
                color: Color(AppColors.textDisabledColor),
              ),
              const SizedBox(height: AppSizes.paddingM),
              Text(
                'لا توجد إحصائيات متاحة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Color(AppColors.textSecondaryColor),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: Color(AppColors.primaryColor),
                  size: AppSizes.iconM,
                ),
                const SizedBox(width: AppSizes.paddingS),
                Text(
                  'ملخص الحضور الشهري',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSizes.paddingL),
            
            // Summary Stats Grid
            _buildSummaryGrid(context),
            
            const SizedBox(height: AppSizes.paddingL),
            
            // Progress Indicators
            _buildProgressSection(context),
            
            const SizedBox(height: AppSizes.paddingL),
            
            // Additional Info
            _buildAdditionalInfo(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryGrid(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildSummaryItem(
                context,
                'أيام الحضور',
                '${summary!.presentDays}',
                Icons.check_circle,
                Color(AppColors.successColor),
              ),
            ),
            const SizedBox(width: AppSizes.paddingS),
            Expanded(
              child: _buildSummaryItem(
                context,
                'أيام الغياب',
                '${summary!.absentDays}',
                Icons.cancel,
                Color(AppColors.errorColor),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSizes.paddingS),
        Row(
          children: [
            Expanded(
              child: _buildSummaryItem(
                context,
                'أيام التأخير',
                '${summary!.lateDays}',
                Icons.schedule,
                Color(AppColors.warningColor),
              ),
            ),
            const SizedBox(width: AppSizes.paddingS),
            Expanded(
              child: _buildSummaryItem(
                context,
                'إجمالي الساعات',
                '${summary!.totalHours.toStringAsFixed(1)}س',
                Icons.access_time,
                Color(AppColors.infoColor),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: AppSizes.iconL,
          ),
          const SizedBox(height: AppSizes.paddingS),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    final attendanceRate = summary!.attendanceRate;
    final punctualityRate = summary!.punctualityRate;
    
    return Column(
      children: [
        _buildProgressIndicator(
          context,
          'معدل الحضور',
          attendanceRate,
          Color(AppColors.successColor),
        ),
        const SizedBox(height: AppSizes.paddingM),
        _buildProgressIndicator(
          context,
          'معدل الانضباط',
          punctualityRate,
          Color(AppColors.infoColor),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(
    BuildContext context,
    String title,
    double percentage,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${percentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSizes.paddingS),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      decoration: BoxDecoration(
        color: Color(AppColors.backgroundColor),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
      ),
      child: Column(
        children: [
          _buildInfoRow(
            context,
            'متوسط ساعات العمل اليومية',
            '${summary!.averageDailyHours.toStringAsFixed(1)} ساعة',
            Icons.schedule,
          ),
          const SizedBox(height: AppSizes.paddingS),
          _buildInfoRow(
            context,
            'إجمالي العمل الإضافي',
            '${summary!.totalOvertimeHours.toStringAsFixed(1)} ساعة',
            Icons.timer,
          ),
          const SizedBox(height: AppSizes.paddingS),
          _buildInfoRow(
            context,
            'أيام العمل المتوقعة',
            '${summary!.expectedWorkDays} يوم',
            Icons.calendar_month,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: AppSizes.iconS,
          color: Color(AppColors.primaryColor),
        ),
        const SizedBox(width: AppSizes.paddingS),
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Color(AppColors.textPrimaryColor),
          ),
        ),
      ],
    );
  }
}
