class Bonus {
  final int id;
  final String bonusType;
  final String bonusTypeLabel;
  final String bonusCategory;
  final String bonusCategoryLabel;
  final double amount;
  final String description;
  final String reason;
  final String approvalStatus;
  final String approvalStatusLabel;
  final DateTime effectiveDate;
  final DateTime? paymentDate;
  final String? notes;
  final String formattedAmount;
  final DateTime? approvedAt;
  final DateTime createdAt;

  Bonus({
    required this.id,
    required this.bonusType,
    required this.bonusTypeLabel,
    required this.bonusCategory,
    required this.bonusCategoryLabel,
    required this.amount,
    required this.description,
    required this.reason,
    required this.approvalStatus,
    required this.approvalStatusLabel,
    required this.effectiveDate,
    this.paymentDate,
    this.notes,
    required this.formattedAmount,
    this.approvedAt,
    required this.createdAt,
  });

  factory Bonus.fromJson(Map<String, dynamic> json) {
    return Bonus(
      id: json['id'],
      bonusType: json['bonus_type'] ?? '',
      bonusTypeLabel: json['bonus_type_label'] ?? '',
      bonusCategory: json['bonus_category'] ?? '',
      bonusCategoryLabel: json['bonus_category_label'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      description: json['description'] ?? '',
      reason: json['reason'] ?? '',
      approvalStatus: json['approval_status'] ?? '',
      approvalStatusLabel: json['approval_status_label'] ?? '',
      effectiveDate: DateTime.parse(json['effective_date']),
      paymentDate: json['payment_date'] != null 
          ? DateTime.parse(json['payment_date']) 
          : null,
      notes: json['notes'],
      formattedAmount: json['formatted_amount'] ?? '',
      approvedAt: json['approved_at'] != null 
          ? DateTime.parse(json['approved_at']) 
          : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isPending => approvalStatus == 'pending';
  bool get isApproved => approvalStatus == 'approved';
  bool get isRejected => approvalStatus == 'rejected';
  bool get isPaid => paymentDate != null;
}

class Deduction {
  final int id;
  final String deductionType;
  final String deductionTypeLabel;
  final double amount;
  final String description;
  final String reason;
  final String approvalStatus;
  final String approvalStatusLabel;
  final DateTime effectiveDate;
  final DateTime? deductionDate;
  final String? notes;
  final String formattedAmount;
  final DateTime? approvedAt;
  final DateTime createdAt;

  Deduction({
    required this.id,
    required this.deductionType,
    required this.deductionTypeLabel,
    required this.amount,
    required this.description,
    required this.reason,
    required this.approvalStatus,
    required this.approvalStatusLabel,
    required this.effectiveDate,
    this.deductionDate,
    this.notes,
    required this.formattedAmount,
    this.approvedAt,
    required this.createdAt,
  });

  factory Deduction.fromJson(Map<String, dynamic> json) {
    return Deduction(
      id: json['id'],
      deductionType: json['deduction_type'] ?? '',
      deductionTypeLabel: json['deduction_type_label'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      description: json['description'] ?? '',
      reason: json['reason'] ?? '',
      approvalStatus: json['approval_status'] ?? '',
      approvalStatusLabel: json['approval_status_label'] ?? '',
      effectiveDate: DateTime.parse(json['effective_date']),
      deductionDate: json['deduction_date'] != null 
          ? DateTime.parse(json['deduction_date']) 
          : null,
      notes: json['notes'],
      formattedAmount: json['formatted_amount'] ?? '',
      approvedAt: json['approved_at'] != null 
          ? DateTime.parse(json['approved_at']) 
          : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isPending => approvalStatus == 'pending';
  bool get isApproved => approvalStatus == 'approved';
  bool get isRejected => approvalStatus == 'rejected';
  bool get isDeducted => deductionDate != null;
}

class Transfer {
  final int id;
  final String transferType;
  final String transferTypeLabel;
  final String? fromBranch;
  final String? toBranch;
  final String? fromDepartment;
  final String? toDepartment;
  final String reason;
  final String approvalStatus;
  final String approvalStatusLabel;
  final DateTime effectiveDate;
  final String? notes;
  final DateTime? approvedAt;
  final DateTime createdAt;

  Transfer({
    required this.id,
    required this.transferType,
    required this.transferTypeLabel,
    this.fromBranch,
    this.toBranch,
    this.fromDepartment,
    this.toDepartment,
    required this.reason,
    required this.approvalStatus,
    required this.approvalStatusLabel,
    required this.effectiveDate,
    this.notes,
    this.approvedAt,
    required this.createdAt,
  });

  factory Transfer.fromJson(Map<String, dynamic> json) {
    return Transfer(
      id: json['id'],
      transferType: json['transfer_type'] ?? '',
      transferTypeLabel: json['transfer_type_label'] ?? '',
      fromBranch: json['from_branch'],
      toBranch: json['to_branch'],
      fromDepartment: json['from_department'],
      toDepartment: json['to_department'],
      reason: json['reason'] ?? '',
      approvalStatus: json['approval_status'] ?? '',
      approvalStatusLabel: json['approval_status_label'] ?? '',
      effectiveDate: DateTime.parse(json['effective_date']),
      notes: json['notes'],
      approvedAt: json['approved_at'] != null 
          ? DateTime.parse(json['approved_at']) 
          : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isPending => approvalStatus == 'pending';
  bool get isApproved => approvalStatus == 'approved';
  bool get isRejected => approvalStatus == 'rejected';

  String get transferDetails {
    if (transferType == 'branch') {
      return 'من $fromBranch إلى $toBranch';
    } else if (transferType == 'department') {
      return 'من $fromDepartment إلى $toDepartment';
    }
    return '';
  }
}

class Promotion {
  final int id;
  final String promotionType;
  final String promotionTypeLabel;
  final String? fromPost;
  final String? toPost;
  final double? oldSalary;
  final double? newSalary;
  final double? salaryIncrease;
  final String reason;
  final String approvalStatus;
  final String approvalStatusLabel;
  final DateTime effectiveDate;
  final String? notes;
  final String? formattedOldSalary;
  final String? formattedNewSalary;
  final String? formattedSalaryIncrease;
  final DateTime? approvedAt;
  final DateTime createdAt;

  Promotion({
    required this.id,
    required this.promotionType,
    required this.promotionTypeLabel,
    this.fromPost,
    this.toPost,
    this.oldSalary,
    this.newSalary,
    this.salaryIncrease,
    required this.reason,
    required this.approvalStatus,
    required this.approvalStatusLabel,
    required this.effectiveDate,
    this.notes,
    this.formattedOldSalary,
    this.formattedNewSalary,
    this.formattedSalaryIncrease,
    this.approvedAt,
    required this.createdAt,
  });

  factory Promotion.fromJson(Map<String, dynamic> json) {
    return Promotion(
      id: json['id'],
      promotionType: json['promotion_type'] ?? '',
      promotionTypeLabel: json['promotion_type_label'] ?? '',
      fromPost: json['from_post'],
      toPost: json['to_post'],
      oldSalary: json['old_salary']?.toDouble(),
      newSalary: json['new_salary']?.toDouble(),
      salaryIncrease: json['salary_increase']?.toDouble(),
      reason: json['reason'] ?? '',
      approvalStatus: json['approval_status'] ?? '',
      approvalStatusLabel: json['approval_status_label'] ?? '',
      effectiveDate: DateTime.parse(json['effective_date']),
      notes: json['notes'],
      formattedOldSalary: json['formatted_old_salary'],
      formattedNewSalary: json['formatted_new_salary'],
      formattedSalaryIncrease: json['formatted_salary_increase'],
      approvedAt: json['approved_at'] != null 
          ? DateTime.parse(json['approved_at']) 
          : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isPending => approvalStatus == 'pending';
  bool get isApproved => approvalStatus == 'approved';
  bool get isRejected => approvalStatus == 'rejected';
  bool get hasSalaryIncrease => salaryIncrease != null && salaryIncrease! > 0;
}

class Suggestion {
  final int id;
  final String title;
  final String description;
  final String category;
  final String categoryLabel;
  final String? expectedBenefit;
  final String status;
  final String statusLabel;
  final DateTime submissionDate;
  final DateTime? reviewDate;
  final DateTime? implementationDate;
  final String? feedback;
  final DateTime createdAt;

  Suggestion({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.categoryLabel,
    this.expectedBenefit,
    required this.status,
    required this.statusLabel,
    required this.submissionDate,
    this.reviewDate,
    this.implementationDate,
    this.feedback,
    required this.createdAt,
  });

  factory Suggestion.fromJson(Map<String, dynamic> json) {
    return Suggestion(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      categoryLabel: json['category_label'] ?? '',
      expectedBenefit: json['expected_benefit'],
      status: json['status'] ?? '',
      statusLabel: json['status_label'] ?? '',
      submissionDate: DateTime.parse(json['submission_date']),
      reviewDate: json['review_date'] != null 
          ? DateTime.parse(json['review_date']) 
          : null,
      implementationDate: json['implementation_date'] != null 
          ? DateTime.parse(json['implementation_date']) 
          : null,
      feedback: json['feedback'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isPending => status == 'pending';
  bool get isUnderReview => status == 'under_review';
  bool get isApproved => status == 'approved';
  bool get isRejected => status == 'rejected';
  bool get isImplemented => status == 'implemented';
}

class Complaint {
  final int id;
  final String title;
  final String description;
  final String category;
  final String categoryLabel;
  final String priority;
  final String priorityLabel;
  final String status;
  final String statusLabel;
  final DateTime submissionDate;
  final DateTime? investigationDate;
  final DateTime? resolutionDate;
  final String? resolution;
  final DateTime createdAt;

  Complaint({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.categoryLabel,
    required this.priority,
    required this.priorityLabel,
    required this.status,
    required this.statusLabel,
    required this.submissionDate,
    this.investigationDate,
    this.resolutionDate,
    this.resolution,
    required this.createdAt,
  });

  factory Complaint.fromJson(Map<String, dynamic> json) {
    return Complaint(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      categoryLabel: json['category_label'] ?? '',
      priority: json['priority'] ?? '',
      priorityLabel: json['priority_label'] ?? '',
      status: json['status'] ?? '',
      statusLabel: json['status_label'] ?? '',
      submissionDate: DateTime.parse(json['submission_date']),
      investigationDate: json['investigation_date'] != null 
          ? DateTime.parse(json['investigation_date']) 
          : null,
      resolutionDate: json['resolution_date'] != null 
          ? DateTime.parse(json['resolution_date']) 
          : null,
      resolution: json['resolution'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isPending => status == 'pending';
  bool get isUnderInvestigation => status == 'under_investigation';
  bool get isResolved => status == 'resolved';
  bool get isClosed => status == 'closed';
  bool get isHighPriority => priority == 'high' || priority == 'urgent';
}

class Warning {
  final int id;
  final String warningType;
  final String warningTypeLabel;
  final String severity;
  final String severityLabel;
  final String title;
  final String description;
  final DateTime incidentDate;
  final DateTime warningDate;
  final DateTime? expiryDate;
  final bool isActive;
  final DateTime createdAt;

  Warning({
    required this.id,
    required this.warningType,
    required this.warningTypeLabel,
    required this.severity,
    required this.severityLabel,
    required this.title,
    required this.description,
    required this.incidentDate,
    required this.warningDate,
    this.expiryDate,
    required this.isActive,
    required this.createdAt,
  });

  factory Warning.fromJson(Map<String, dynamic> json) {
    return Warning(
      id: json['id'],
      warningType: json['warning_type'] ?? '',
      warningTypeLabel: json['warning_type_label'] ?? '',
      severity: json['severity'] ?? '',
      severityLabel: json['severity_label'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      incidentDate: DateTime.parse(json['incident_date']),
      warningDate: DateTime.parse(json['warning_date']),
      expiryDate: json['expiry_date'] != null 
          ? DateTime.parse(json['expiry_date']) 
          : null,
      isActive: json['is_active'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  bool get isHighSeverity => severity == 'high' || severity == 'critical';
}
