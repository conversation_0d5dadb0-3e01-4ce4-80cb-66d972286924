class User {
  final int id;
  final String name;
  final String? fullNameEn;
  final String email;
  final String? username;
  final String? phone;
  final String? emergencyContactNumber;
  final String? address;
  final DateTime? dob;
  final String? gender;
  final String? maritalStatus;
  final String? idNumber;
  final String? avatar;
  final DateTime? joiningDate;
  final String? employmentType;
  final String? userType;
  final String? workspaceType;
  final String? status;
  final bool isActive;
  final String? companyName;
  final String? branchName;
  final String? departmentName;
  final String? postName;
  final String? roleName;
  final String? supervisorName;
  final String? bankAccountNumber;
  final String? bankBranchCode;
  final double? insuranceSalary;
  final String? insuranceType;
  final String? medicalInsuranceCategory;
  final String? insuranceJobTitle;
  final DateTime? insuranceStartDate;
  final String? insuranceInstitutionName;
  final String? fingerprintCode;
  final String? beConnectCode;
  final String? beConnectClientCode;
  final String? deviceType;
  final bool onlineStatus;
  final DateTime? lastLogin;
  final OfficeTime? officeTime;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.isActive,
    required this.onlineStatus,
    this.fullNameEn,
    this.username,
    this.phone,
    this.emergencyContactNumber,
    this.address,
    this.dob,
    this.gender,
    this.maritalStatus,
    this.idNumber,
    this.avatar,
    this.joiningDate,
    this.employmentType,
    this.userType,
    this.workspaceType,
    this.status,
    this.companyName,
    this.branchName,
    this.departmentName,
    this.postName,
    this.roleName,
    this.supervisorName,
    this.bankAccountNumber,
    this.bankBranchCode,
    this.insuranceSalary,
    this.insuranceType,
    this.medicalInsuranceCategory,
    this.insuranceJobTitle,
    this.insuranceStartDate,
    this.insuranceInstitutionName,
    this.fingerprintCode,
    this.beConnectCode,
    this.beConnectClientCode,
    this.deviceType,
    this.lastLogin,
    this.officeTime,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['personal_info']['id'],
      name: json['personal_info']['name'],
      fullNameEn: json['personal_info']['full_name_en'],
      email: json['personal_info']['email'],
      username: json['personal_info']['username'],
      phone: json['personal_info']['phone'],
      emergencyContactNumber: json['personal_info']['emergency_contact_number'],
      address: json['personal_info']['address'],
      dob: json['personal_info']['dob'] != null 
          ? DateTime.parse(json['personal_info']['dob']) 
          : null,
      gender: json['personal_info']['gender'],
      maritalStatus: json['personal_info']['marital_status'],
      idNumber: json['personal_info']['id_number'],
      avatar: json['personal_info']['avatar'],
      joiningDate: json['employment_info']['joining_date'] != null 
          ? DateTime.parse(json['employment_info']['joining_date']) 
          : null,
      employmentType: json['employment_info']['employment_type'],
      userType: json['employment_info']['user_type'],
      workspaceType: json['employment_info']['workspace_type'],
      status: json['employment_info']['status'],
      isActive: json['employment_info']['is_active'] ?? true,
      companyName: json['employment_info']['company'],
      branchName: json['employment_info']['branch'],
      departmentName: json['employment_info']['department'],
      postName: json['employment_info']['post'],
      roleName: json['employment_info']['role'],
      supervisorName: json['employment_info']['supervisor'],
      bankAccountNumber: json['financial_info']['bank_account_number'],
      bankBranchCode: json['financial_info']['bank_branch_code'],
      insuranceSalary: json['financial_info']['insurance_salary']?.toDouble(),
      insuranceType: json['insurance_info']['insurance_type'],
      medicalInsuranceCategory: json['insurance_info']['medical_insurance_category'],
      insuranceJobTitle: json['insurance_info']['insurance_job_title'],
      insuranceStartDate: json['insurance_info']['insurance_start_date'] != null 
          ? DateTime.parse(json['insurance_info']['insurance_start_date']) 
          : null,
      insuranceInstitutionName: json['insurance_info']['insurance_institution'],
      fingerprintCode: json['system_info']['fingerprint_code'],
      beConnectCode: json['system_info']['be_connect_code'],
      beConnectClientCode: json['system_info']['be_connect_client_code'],
      deviceType: json['system_info']['device_type'],
      onlineStatus: json['system_info']['online_status'] ?? false,
      lastLogin: json['system_info']['last_login'] != null 
          ? DateTime.parse(json['system_info']['last_login']) 
          : null,
      officeTime: json['employment_info']['office_time'] != null 
          ? OfficeTime.fromJson(json['employment_info']['office_time']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'is_active': isActive,
      // Add other fields as needed
    };
  }

  int? get age {
    if (dob == null) return null;
    return DateTime.now().difference(dob!).inDays ~/ 365;
  }

  String get displayName => name;
  
  String get avatarUrl => avatar ?? '';
  
  bool get hasAvatar => avatar != null && avatar!.isNotEmpty;
}

class OfficeTime {
  final String shift;
  final String openingTime;
  final String closingTime;

  OfficeTime({
    required this.shift,
    required this.openingTime,
    required this.closingTime,
  });

  factory OfficeTime.fromJson(Map<String, dynamic> json) {
    return OfficeTime(
      shift: json['shift'] ?? '',
      openingTime: json['opening_time'] ?? '',
      closingTime: json['closing_time'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'shift': shift,
      'opening_time': openingTime,
      'closing_time': closingTime,
    };
  }
}
