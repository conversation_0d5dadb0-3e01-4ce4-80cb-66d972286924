class Leave {
  final int id;
  final String leaveType;
  final DateTime leaveFrom;
  final DateTime leaveTo;
  final int noOfDays;
  final String reason;
  final String status;
  final String statusLabel;
  final DateTime leaveRequestedDate;
  final DateTime? approvedDate;
  final DateTime? rejectedDate;
  final String? adminRemark;
  final bool earlyExit;
  final DateTime createdAt;

  Leave({
    required this.id,
    required this.leaveType,
    required this.leaveFrom,
    required this.leaveTo,
    required this.noOfDays,
    required this.reason,
    required this.status,
    required this.statusLabel,
    required this.leaveRequestedDate,
    this.approvedDate,
    this.rejectedDate,
    this.adminRemark,
    required this.earlyExit,
    required this.createdAt,
  });

  factory Leave.fromJson(Map<String, dynamic> json) {
    return Leave(
      id: json['id'],
      leaveType: json['leave_type'] ?? '',
      leaveFrom: DateTime.parse(json['leave_from']),
      leaveTo: DateTime.parse(json['leave_to']),
      noOfDays: json['no_of_days'] ?? 0,
      reason: json['reason'] ?? '',
      status: json['status'] ?? '',
      statusLabel: json['status_label'] ?? '',
      leaveRequestedDate: DateTime.parse(json['leave_requested_date']),
      approvedDate: json['approved_date'] != null 
          ? DateTime.parse(json['approved_date']) 
          : null,
      rejectedDate: json['rejected_date'] != null 
          ? DateTime.parse(json['rejected_date']) 
          : null,
      adminRemark: json['admin_remark'],
      earlyExit: json['early_exit'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'leave_type': leaveType,
      'leave_from': leaveFrom.toIso8601String().split('T')[0],
      'leave_to': leaveTo.toIso8601String().split('T')[0],
      'no_of_days': noOfDays,
      'reason': reason,
      'status': status,
      'status_label': statusLabel,
      'early_exit': earlyExit,
    };
  }

  bool get isPending => status == 'pending';
  bool get isApproved => status == 'approved';
  bool get isRejected => status == 'rejected';
  bool get isCancelled => status == 'cancelled';
  bool get canBeCancelled => isPending;

  String get formattedDuration {
    if (noOfDays == 1) return 'يوم واحد';
    if (noOfDays == 2) return 'يومان';
    if (noOfDays <= 10) return '$noOfDays أيام';
    return '$noOfDays يوماً';
  }

  String get dateRange {
    if (leaveFrom.isAtSameMomentAs(leaveTo)) {
      return '${leaveFrom.day}/${leaveFrom.month}/${leaveFrom.year}';
    }
    return '${leaveFrom.day}/${leaveFrom.month} - ${leaveTo.day}/${leaveTo.month}/${leaveTo.year}';
  }
}

class LeaveBalance {
  final int leaveTypeId;
  final String leaveTypeName;
  final int allocatedDays;
  final int usedDays;
  final int remainingDays;
  final double usagePercentage;

  LeaveBalance({
    required this.leaveTypeId,
    required this.leaveTypeName,
    required this.allocatedDays,
    required this.usedDays,
    required this.remainingDays,
    required this.usagePercentage,
  });

  factory LeaveBalance.fromJson(Map<String, dynamic> json) {
    return LeaveBalance(
      leaveTypeId: json['leave_type_id'],
      leaveTypeName: json['leave_type_name'] ?? '',
      allocatedDays: json['allocated_days'] ?? 0,
      usedDays: json['used_days'] ?? 0,
      remainingDays: json['remaining_days'] ?? 0,
      usagePercentage: (json['usage_percentage'] ?? 0.0).toDouble(),
    );
  }

  bool get hasRemainingDays => remainingDays > 0;
  bool get isFullyUsed => remainingDays <= 0;
  bool get isNearlyExhausted => remainingDays <= 2 && remainingDays > 0;
}

class LeaveType {
  final int id;
  final String name;
  final int? leaveAllocated;
  final bool earlyExit;

  LeaveType({
    required this.id,
    required this.name,
    this.leaveAllocated,
    required this.earlyExit,
  });

  factory LeaveType.fromJson(Map<String, dynamic> json) {
    return LeaveType(
      id: json['id'],
      name: json['name'] ?? '',
      leaveAllocated: json['leave_allocated'],
      earlyExit: json['early_exit'] ?? false,
    );
  }
}

class Holiday {
  final int id;
  final String event;
  final DateTime eventDate;
  final String dayName;
  final String? note;

  Holiday({
    required this.id,
    required this.event,
    required this.eventDate,
    required this.dayName,
    this.note,
  });

  factory Holiday.fromJson(Map<String, dynamic> json) {
    return Holiday(
      id: json['id'],
      event: json['event'] ?? '',
      eventDate: DateTime.parse(json['event_date']),
      dayName: json['day_name'] ?? '',
      note: json['note'],
    );
  }

  String get formattedDate {
    return '${eventDate.day}/${eventDate.month}/${eventDate.year}';
  }

  bool get isToday {
    final now = DateTime.now();
    return eventDate.year == now.year &&
           eventDate.month == now.month &&
           eventDate.day == now.day;
  }

  bool get isUpcoming {
    return eventDate.isAfter(DateTime.now());
  }
}

class LeaveSummary {
  final int year;
  final int totalRequests;
  final int approvedRequests;
  final int pendingRequests;
  final int rejectedRequests;
  final int totalLeaveDays;
  final double approvalRate;

  LeaveSummary({
    required this.year,
    required this.totalRequests,
    required this.approvedRequests,
    required this.pendingRequests,
    required this.rejectedRequests,
    required this.totalLeaveDays,
    required this.approvalRate,
  });

  factory LeaveSummary.fromJson(Map<String, dynamic> json) {
    return LeaveSummary(
      year: json['year'] ?? 0,
      totalRequests: json['total_requests'] ?? 0,
      approvedRequests: json['approved_requests'] ?? 0,
      pendingRequests: json['pending_requests'] ?? 0,
      rejectedRequests: json['rejected_requests'] ?? 0,
      totalLeaveDays: json['total_leave_days'] ?? 0,
      approvalRate: (json['approval_rate'] ?? 0.0).toDouble(),
    );
  }
}

class LeaveRequest {
  final int leaveTypeId;
  final DateTime leaveFrom;
  final DateTime leaveTo;
  final String reason;
  final bool earlyExit;

  LeaveRequest({
    required this.leaveTypeId,
    required this.leaveFrom,
    required this.leaveTo,
    required this.reason,
    this.earlyExit = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'leave_type_id': leaveTypeId,
      'leave_from': leaveFrom.toIso8601String().split('T')[0],
      'leave_to': leaveTo.toIso8601String().split('T')[0],
      'reason': reason,
      'early_exit': earlyExit,
    };
  }

  int get numberOfDays {
    return leaveTo.difference(leaveFrom).inDays + 1;
  }
}
