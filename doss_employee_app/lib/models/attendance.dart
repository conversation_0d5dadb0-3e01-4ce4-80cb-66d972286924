class Attendance {
  final int? id;
  final DateTime date;
  final String? dayName;
  final String? checkInTime;
  final String? checkOutTime;
  final double workingHours;
  final String status;
  final int lateArrival;
  final int earlyDeparture;
  final int overtime;
  final String? note;
  final AttendanceLocation? location;

  Attendance({
    this.id,
    required this.date,
    this.dayName,
    this.checkInTime,
    this.checkOutTime,
    required this.workingHours,
    required this.status,
    required this.lateArrival,
    required this.earlyDeparture,
    required this.overtime,
    this.note,
    this.location,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) {
    return Attendance(
      id: json['id'],
      date: DateTime.parse(json['date'] ?? json['attendance_date']),
      dayName: json['day_name'],
      checkInTime: json['check_in_time'],
      checkOutTime: json['check_out_time'],
      workingHours: (json['working_hours'] ?? 0.0).toDouble(),
      status: json['status'] ?? '',
      lateArrival: json['late_arrival'] ?? 0,
      earlyDeparture: json['early_departure'] ?? 0,
      overtime: json['overtime'] ?? 0,
      note: json['note'],
      location: json['location'] != null 
          ? AttendanceLocation.fromJson(json['location']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String().split('T')[0],
      'day_name': dayName,
      'check_in_time': checkInTime,
      'check_out_time': checkOutTime,
      'working_hours': workingHours,
      'status': status,
      'late_arrival': lateArrival,
      'early_departure': earlyDeparture,
      'overtime': overtime,
      'note': note,
      'location': location?.toJson(),
    };
  }

  bool get hasCheckedIn => checkInTime != null;
  bool get hasCheckedOut => checkOutTime != null;
  bool get isComplete => hasCheckedIn && hasCheckedOut;
  bool get isLate => lateArrival > 0;
  bool get hasOvertime => overtime > 0;
  bool get leftEarly => earlyDeparture > 0;

  String get formattedWorkingHours {
    final hours = workingHours.floor();
    final minutes = ((workingHours - hours) * 60).round();
    return '${hours}س ${minutes}د';
  }

  String get formattedLateArrival {
    if (lateArrival <= 0) return '';
    final hours = lateArrival ~/ 60;
    final minutes = lateArrival % 60;
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    }
    return '${minutes}د';
  }

  String get formattedOvertime {
    if (overtime <= 0) return '';
    final hours = overtime ~/ 60;
    final minutes = overtime % 60;
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    }
    return '${minutes}د';
  }
}

class AttendanceLocation {
  final double? checkInLat;
  final double? checkInLng;
  final double? checkOutLat;
  final double? checkOutLng;

  AttendanceLocation({
    this.checkInLat,
    this.checkInLng,
    this.checkOutLat,
    this.checkOutLng,
  });

  factory AttendanceLocation.fromJson(Map<String, dynamic> json) {
    return AttendanceLocation(
      checkInLat: json['check_in_lat']?.toDouble(),
      checkInLng: json['check_in_lng']?.toDouble(),
      checkOutLat: json['check_out_lat']?.toDouble(),
      checkOutLng: json['check_out_lng']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'check_in_lat': checkInLat,
      'check_in_lng': checkInLng,
      'check_out_lat': checkOutLat,
      'check_out_lng': checkOutLng,
    };
  }
}

class TodayAttendance {
  final bool hasCheckedIn;
  final bool hasCheckedOut;
  final String? checkInTime;
  final String? checkOutTime;
  final String attendanceDate;
  final double workingHours;
  final String status;
  final OfficeHours? officeHours;
  final int lateArrival;
  final int earlyDeparture;
  final int overtime;

  TodayAttendance({
    required this.hasCheckedIn,
    required this.hasCheckedOut,
    this.checkInTime,
    this.checkOutTime,
    required this.attendanceDate,
    required this.workingHours,
    required this.status,
    this.officeHours,
    required this.lateArrival,
    required this.earlyDeparture,
    required this.overtime,
  });

  factory TodayAttendance.fromJson(Map<String, dynamic> json) {
    return TodayAttendance(
      hasCheckedIn: json['has_checked_in'] ?? false,
      hasCheckedOut: json['has_checked_out'] ?? false,
      checkInTime: json['check_in_time'],
      checkOutTime: json['check_out_time'],
      attendanceDate: json['attendance_date'] ?? '',
      workingHours: (json['working_hours'] ?? 0.0).toDouble(),
      status: json['status'] ?? '',
      officeHours: json['office_hours'] != null 
          ? OfficeHours.fromJson(json['office_hours']) 
          : null,
      lateArrival: json['late_arrival'] ?? 0,
      earlyDeparture: json['early_departure'] ?? 0,
      overtime: json['overtime'] ?? 0,
    );
  }

  bool get canCheckIn => !hasCheckedIn;
  bool get canCheckOut => hasCheckedIn && !hasCheckedOut;
  bool get isComplete => hasCheckedIn && hasCheckedOut;
}

class OfficeHours {
  final String openingTime;
  final String closingTime;
  final String shiftName;

  OfficeHours({
    required this.openingTime,
    required this.closingTime,
    required this.shiftName,
  });

  factory OfficeHours.fromJson(Map<String, dynamic> json) {
    return OfficeHours(
      openingTime: json['opening_time'] ?? '',
      closingTime: json['closing_time'] ?? '',
      shiftName: json['shift_name'] ?? '',
    );
  }
}

class AttendanceSummary {
  final int month;
  final int year;
  final int totalDays;
  final int presentDays;
  final int absentDays;
  final int lateDays;
  final double attendanceRate;
  final double totalWorkingHours;
  final double totalOvertimeHours;
  final double averageDailyHours;
  final int totalLateMinutes;
  final double averageLateMinutes;

  AttendanceSummary({
    required this.month,
    required this.year,
    required this.totalDays,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.attendanceRate,
    required this.totalWorkingHours,
    required this.totalOvertimeHours,
    required this.averageDailyHours,
    required this.totalLateMinutes,
    required this.averageLateMinutes,
  });

  factory AttendanceSummary.fromJson(Map<String, dynamic> json) {
    return AttendanceSummary(
      month: json['month'] ?? 0,
      year: json['year'] ?? 0,
      totalDays: json['total_days'] ?? 0,
      presentDays: json['present_days'] ?? 0,
      absentDays: json['absent_days'] ?? 0,
      lateDays: json['late_days'] ?? 0,
      attendanceRate: (json['attendance_rate'] ?? 0.0).toDouble(),
      totalWorkingHours: (json['total_working_hours'] ?? 0.0).toDouble(),
      totalOvertimeHours: (json['total_overtime_hours'] ?? 0.0).toDouble(),
      averageDailyHours: (json['average_daily_hours'] ?? 0.0).toDouble(),
      totalLateMinutes: json['total_late_minutes'] ?? 0,
      averageLateMinutes: (json['average_late_minutes'] ?? 0.0).toDouble(),
    );
  }
}
