class Salary {
  final int id;
  final double basicSalary;
  final double housingAllowance;
  final double transportAllowance;
  final double foodAllowance;
  final double otherAllowances;
  final double totalAllowances;
  final double totalSalary;
  final DateTime effectiveDate;
  final String formattedTotalSalary;
  final String formattedBasicSalary;

  Salary({
    required this.id,
    required this.basicSalary,
    required this.housingAllowance,
    required this.transportAllowance,
    required this.foodAllowance,
    required this.otherAllowances,
    required this.totalAllowances,
    required this.totalSalary,
    required this.effectiveDate,
    required this.formattedTotalSalary,
    required this.formattedBasicSalary,
  });

  factory Salary.fromJson(Map<String, dynamic> json) {
    return Salary(
      id: json['id'],
      basicSalary: (json['basic_salary'] ?? 0.0).toDouble(),
      housingAllowance: (json['housing_allowance'] ?? 0.0).toDouble(),
      transportAllowance: (json['transport_allowance'] ?? 0.0).toDouble(),
      foodAllowance: (json['food_allowance'] ?? 0.0).toDouble(),
      otherAllowances: (json['other_allowances'] ?? 0.0).toDouble(),
      totalAllowances: (json['total_allowances'] ?? 0.0).toDouble(),
      totalSalary: (json['total_salary'] ?? 0.0).toDouble(),
      effectiveDate: DateTime.parse(json['effective_date']),
      formattedTotalSalary: json['formatted_total_salary'] ?? '',
      formattedBasicSalary: json['formatted_basic_salary'] ?? '',
    );
  }

  double get allowancesPercentage {
    if (totalSalary == 0) return 0;
    return (totalAllowances / totalSalary) * 100;
  }
}

class Payroll {
  final int id;
  final int year;
  final int month;
  final String monthName;
  final String period;
  final double basicSalary;
  final double allowances;
  final double overtimeAmount;
  final double bonusAmount;
  final double deductionAmount;
  final double advanceAmount;
  final double insuranceDeduction;
  final double taxDeduction;
  final double grossSalary;
  final double netSalary;
  final String status;
  final String statusLabel;
  final DateTime? paymentDate;
  final String? notes;
  final String formattedNetSalary;
  final String formattedGrossSalary;
  final DateTime createdAt;

  Payroll({
    required this.id,
    required this.year,
    required this.month,
    required this.monthName,
    required this.period,
    required this.basicSalary,
    required this.allowances,
    required this.overtimeAmount,
    required this.bonusAmount,
    required this.deductionAmount,
    required this.advanceAmount,
    required this.insuranceDeduction,
    required this.taxDeduction,
    required this.grossSalary,
    required this.netSalary,
    required this.status,
    required this.statusLabel,
    this.paymentDate,
    this.notes,
    required this.formattedNetSalary,
    required this.formattedGrossSalary,
    required this.createdAt,
  });

  factory Payroll.fromJson(Map<String, dynamic> json) {
    return Payroll(
      id: json['id'],
      year: json['year'],
      month: json['month'],
      monthName: json['month_name'] ?? '',
      period: json['period'] ?? '',
      basicSalary: (json['basic_salary'] ?? 0.0).toDouble(),
      allowances: (json['allowances'] ?? 0.0).toDouble(),
      overtimeAmount: (json['overtime_amount'] ?? 0.0).toDouble(),
      bonusAmount: (json['bonus_amount'] ?? 0.0).toDouble(),
      deductionAmount: (json['deduction_amount'] ?? 0.0).toDouble(),
      advanceAmount: (json['advance_amount'] ?? 0.0).toDouble(),
      insuranceDeduction: (json['insurance_deduction'] ?? 0.0).toDouble(),
      taxDeduction: (json['tax_deduction'] ?? 0.0).toDouble(),
      grossSalary: (json['gross_salary'] ?? 0.0).toDouble(),
      netSalary: (json['net_salary'] ?? 0.0).toDouble(),
      status: json['status'] ?? '',
      statusLabel: json['status_label'] ?? '',
      paymentDate: json['payment_date'] != null 
          ? DateTime.parse(json['payment_date']) 
          : null,
      notes: json['notes'],
      formattedNetSalary: json['formatted_net_salary'] ?? '',
      formattedGrossSalary: json['formatted_gross_salary'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get isPaid => status == 'paid';
  bool get isApproved => status == 'approved';
  bool get isDraft => status == 'draft';

  double get totalDeductions {
    return deductionAmount + advanceAmount + insuranceDeduction + taxDeduction;
  }

  double get totalEarnings {
    return basicSalary + allowances + overtimeAmount + bonusAmount;
  }
}

class Advance {
  final int id;
  final double amount;
  final String reason;
  final String status;
  final String statusLabel;
  final DateTime requestDate;
  final DateTime? approvedDate;
  final DateTime? paymentDate;
  final int installments;
  final double installmentAmount;
  final double paidAmount;
  final double remainingAmount;
  final double paymentProgress;
  final String formattedAmount;
  final String formattedRemainingAmount;
  final String formattedPaidAmount;
  final String? approvalNotes;
  final String? rejectionReason;

  Advance({
    required this.id,
    required this.amount,
    required this.reason,
    required this.status,
    required this.statusLabel,
    required this.requestDate,
    this.approvedDate,
    this.paymentDate,
    required this.installments,
    required this.installmentAmount,
    required this.paidAmount,
    required this.remainingAmount,
    required this.paymentProgress,
    required this.formattedAmount,
    required this.formattedRemainingAmount,
    required this.formattedPaidAmount,
    this.approvalNotes,
    this.rejectionReason,
  });

  factory Advance.fromJson(Map<String, dynamic> json) {
    return Advance(
      id: json['id'],
      amount: (json['amount'] ?? 0.0).toDouble(),
      reason: json['reason'] ?? '',
      status: json['status'] ?? '',
      statusLabel: json['status_label'] ?? '',
      requestDate: DateTime.parse(json['request_date']),
      approvedDate: json['approved_date'] != null 
          ? DateTime.parse(json['approved_date']) 
          : null,
      paymentDate: json['payment_date'] != null 
          ? DateTime.parse(json['payment_date']) 
          : null,
      installments: json['installments'] ?? 0,
      installmentAmount: (json['installment_amount'] ?? 0.0).toDouble(),
      paidAmount: (json['paid_amount'] ?? 0.0).toDouble(),
      remainingAmount: (json['remaining_amount'] ?? 0.0).toDouble(),
      paymentProgress: (json['payment_progress'] ?? 0.0).toDouble(),
      formattedAmount: json['formatted_amount'] ?? '',
      formattedRemainingAmount: json['formatted_remaining_amount'] ?? '',
      formattedPaidAmount: json['formatted_paid_amount'] ?? '',
      approvalNotes: json['approval_notes'],
      rejectionReason: json['rejection_reason'],
    );
  }

  bool get isPending => status == 'pending';
  bool get isApproved => status == 'approved';
  bool get isRejected => status == 'rejected';
  bool get isPaid => status == 'paid';
  bool get isCompleted => status == 'completed';

  bool get isActive => isApproved || isPaid;
  bool get hasRemainingAmount => remainingAmount > 0;
}

class AdvanceRequest {
  final double amount;
  final String reason;
  final int installments;

  AdvanceRequest({
    required this.amount,
    required this.reason,
    required this.installments,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'reason': reason,
      'installments': installments,
    };
  }

  double get installmentAmount => amount / installments;

  bool get isValid {
    return amount > 0 && 
           reason.isNotEmpty && 
           installments > 0 && 
           installments <= 12;
  }
}
