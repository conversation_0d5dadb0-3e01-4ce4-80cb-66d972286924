import 'user.dart';

class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final Map<String, dynamic>? errors;
  final int? statusCode;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
    this.statusCode,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json, 
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : json['data'],
      errors: json['errors'],
      statusCode: json['status_code'],
    );
  }

  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse<T>(
      success: true,
      message: message ?? 'تم بنجاح',
      data: data,
    );
  }

  factory ApiResponse.error(String message, {Map<String, dynamic>? errors, int? statusCode}) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errors: errors,
      statusCode: statusCode,
    );
  }

  bool get isSuccess => success;
  bool get isError => !success;
  bool get hasData => data != null;
  bool get hasErrors => errors != null && errors!.isNotEmpty;
}

class ApiListResponse<T> {
  final bool success;
  final String message;
  final List<T>? data;
  final Map<String, dynamic>? errors;
  final int? statusCode;
  final PaginationMeta? meta;

  ApiListResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
    this.statusCode,
    this.meta,
  });

  factory ApiListResponse.fromJson(
    Map<String, dynamic> json, 
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    List<T>? dataList;
    if (json['data'] != null) {
      if (json['data'] is List) {
        dataList = (json['data'] as List)
            .map((item) => fromJsonT(item as Map<String, dynamic>))
            .toList();
      }
    }

    return ApiListResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: dataList,
      errors: json['errors'],
      statusCode: json['status_code'],
      meta: json['meta'] != null ? PaginationMeta.fromJson(json['meta']) : null,
    );
  }

  bool get isSuccess => success;
  bool get isError => !success;
  bool get hasData => data != null && data!.isNotEmpty;
  bool get isEmpty => data == null || data!.isEmpty;
  int get count => data?.length ?? 0;
}

class PaginationMeta {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;
  final int from;
  final int to;

  PaginationMeta({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    required this.from,
    required this.to,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) {
    return PaginationMeta(
      currentPage: json['current_page'] ?? 1,
      lastPage: json['last_page'] ?? 1,
      perPage: json['per_page'] ?? 15,
      total: json['total'] ?? 0,
      from: json['from'] ?? 0,
      to: json['to'] ?? 0,
    );
  }

  bool get hasNextPage => currentPage < lastPage;
  bool get hasPreviousPage => currentPage > 1;
  bool get isFirstPage => currentPage == 1;
  bool get isLastPage => currentPage == lastPage;
}

class LoginResponse {
  final bool success;
  final String message;
  final String? token;
  final User? user;
  final Map<String, dynamic>? errors;

  LoginResponse({
    required this.success,
    required this.message,
    this.token,
    this.user,
    this.errors,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      token: json['data']?['token'],
      user: json['data']?['user'] != null 
          ? User.fromJson(json['data']['user']) 
          : null,
      errors: json['errors'],
    );
  }

  bool get isSuccess => success && token != null;
  bool get hasUser => user != null;
}

class ErrorResponse {
  final String message;
  final Map<String, List<String>>? validationErrors;
  final int? statusCode;

  ErrorResponse({
    required this.message,
    this.validationErrors,
    this.statusCode,
  });

  factory ErrorResponse.fromJson(Map<String, dynamic> json) {
    Map<String, List<String>>? validationErrors;
    
    if (json['errors'] != null) {
      validationErrors = {};
      (json['errors'] as Map<String, dynamic>).forEach((key, value) {
        if (value is List) {
          validationErrors![key] = value.cast<String>();
        } else if (value is String) {
          validationErrors![key] = [value];
        }
      });
    }

    return ErrorResponse(
      message: json['message'] ?? 'حدث خطأ غير متوقع',
      validationErrors: validationErrors,
      statusCode: json['status_code'],
    );
  }

  bool get hasValidationErrors => 
      validationErrors != null && validationErrors!.isNotEmpty;

  String get firstValidationError {
    if (!hasValidationErrors) return message;
    return validationErrors!.values.first.first;
  }

  List<String> getFieldErrors(String field) {
    return validationErrors?[field] ?? [];
  }
}
