import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../auth/login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.profile),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _showLogoutDialog,
          ),
        ],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              children: [
                _buildProfileHeader(authProvider),
                const SizedBox(height: AppSizes.paddingL),
                _buildProfileOptions(),
                const SizedBox(height: AppSizes.paddingL),
                _buildAppInfo(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(AuthProvider authProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          children: [
            CircleAvatar(
              radius: 50,
              backgroundColor: Color(AppColors.primaryLightColor),
              backgroundImage: authProvider.hasUserAvatar
                  ? NetworkImage(authProvider.userAvatarUrl)
                  : null,
              child: !authProvider.hasUserAvatar
                  ? Icon(
                      Icons.person,
                      size: 50,
                      color: Color(AppColors.primaryColor),
                    )
                  : null,
            ),
            const SizedBox(height: AppSizes.paddingM),
            Text(
              authProvider.userDisplayName,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              authProvider.userEmail,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Color(AppColors.textSecondaryColor),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.paddingM,
                vertical: AppSizes.paddingS,
              ),
              decoration: BoxDecoration(
                color: Color(AppColors.primaryColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusL),
              ),
              child: Text(
                authProvider.userPosition,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Color(AppColors.primaryColor),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: AppSizes.paddingS),
            Text(
              '${authProvider.userDepartment} - ${authProvider.userBranch}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color(AppColors.textSecondaryColor),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileOptions() {
    return Column(
      children: [
        _buildOptionCard(
          icon: Icons.person_outline,
          title: 'المعلومات الشخصية',
          subtitle: 'عرض وتحديث البيانات الشخصية',
          onTap: () {
            // TODO: Navigate to personal info
            _showComingSoon();
          },
        ),
        const SizedBox(height: AppSizes.paddingS),
        _buildOptionCard(
          icon: Icons.lock_outline,
          title: 'تغيير كلمة المرور',
          subtitle: 'تحديث كلمة المرور الخاصة بك',
          onTap: () {
            // TODO: Navigate to change password
            _showComingSoon();
          },
        ),
        const SizedBox(height: AppSizes.paddingS),
        _buildOptionCard(
          icon: Icons.folder_outlined,
          title: 'المستندات',
          subtitle: 'عرض وإدارة المستندات الشخصية',
          onTap: () {
            // TODO: Navigate to documents
            _showComingSoon();
          },
        ),
        const SizedBox(height: AppSizes.paddingS),
        _buildOptionCard(
          icon: Icons.notifications_outlined,
          title: 'الإشعارات',
          subtitle: 'إعدادات الإشعارات والتنبيهات',
          onTap: () {
            // TODO: Navigate to notifications settings
            _showComingSoon();
          },
        ),
        const SizedBox(height: AppSizes.paddingS),
        _buildOptionCard(
          icon: Icons.help_outline,
          title: 'المساعدة والدعم',
          subtitle: 'الحصول على المساعدة والدعم الفني',
          onTap: () {
            // TODO: Navigate to help
            _showComingSoon();
          },
        ),
      ],
    );
  }

  Widget _buildOptionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Color(AppColors.primaryColor).withOpacity(0.1),
          child: Icon(
            icon,
            color: Color(AppColors.primaryColor),
          ),
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Widget _buildAppInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            Text(
              'معلومات التطبيق',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            _buildInfoRow('اسم التطبيق', AppConstants.appName),
            _buildInfoRow('الإصدار', AppConstants.appVersion),
            _buildInfoRow('الشركة', AppConstants.companyName),
            const SizedBox(height: AppSizes.paddingM),
            Text(
              'جميع الحقوق محفوظة © ${DateTime.now().year}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color(AppColors.textDisabledColor),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _showComingSoon() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('هذه الميزة قيد التطوير'),
        backgroundColor: Color(AppColors.infoColor),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(AppColors.errorColor),
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  Future<void> _logout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    final success = await authProvider.logout();
    
    if (success && mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false,
      );
    }
  }
}
