import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/leave_provider.dart';
import '../../widgets/leave_balance_card.dart';
import '../../widgets/leave_history_card.dart';
import '../../widgets/leave_request_card.dart';
import 'leave_request_screen.dart';

class LeavesScreen extends StatefulWidget {
  const LeavesScreen({super.key});

  @override
  State<LeavesScreen> createState() => _LeavesScreenState();
}

class _LeavesScreenState extends State<LeavesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final leaveProvider = Provider.of<LeaveProvider>(context, listen: false);
    await leaveProvider.refreshAll();
  }

  Future<void> _refreshData() async {
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.leaves),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.account_balance_wallet),
              text: 'الرصيد',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: 'التاريخ',
            ),
            Tab(
              icon: Icon(Icons.pending_actions),
              text: 'الطلبات',
            ),
          ],
        ),
      ),
      body: Consumer<LeaveProvider>(
        builder: (context, leaveProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildBalanceTab(leaveProvider),
              _buildHistoryTab(leaveProvider),
              _buildRequestsTab(leaveProvider),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToLeaveRequest(),
        icon: const Icon(Icons.add),
        label: const Text('طلب إجازة'),
        backgroundColor: Color(AppColors.primaryColor),
      ),
    );
  }

  Widget _buildBalanceTab(LeaveProvider leaveProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            // Leave Balance Summary
            _buildBalanceSummary(leaveProvider),

            const SizedBox(height: AppSizes.paddingM),

            // Leave Balance Cards
            if (leaveProvider.isLoading)
              const Center(child: CircularProgressIndicator())
            else if (leaveProvider.leaveBalances.isEmpty)
              _buildEmptyState('لا توجد أرصدة إجازات', Icons.account_balance_wallet)
            else
              ...leaveProvider.leaveBalances.map(
                (balance) => LeaveBalanceCard(
                  balance: balance,
                  onRequestLeave: () => _navigateToLeaveRequest(
                    leaveTypeId: balance.leaveTypeId,
                  ),
                ),
              ),

            const SizedBox(height: AppSizes.paddingM),

            // Upcoming Holidays
            _buildUpcomingHolidays(leaveProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab(LeaveProvider leaveProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Column(
        children: [
          // Year Selector
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Row(
              children: [
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingM,
                        vertical: AppSizes.paddingS,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'السنة: ${leaveProvider.selectedYear}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          IconButton(
                            icon: const Icon(Icons.calendar_month),
                            onPressed: () => _selectYear(leaveProvider),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Leave History List
          Expanded(
            child: leaveProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : leaveProvider.leaveHistory.isEmpty
                    ? _buildEmptyState('لا توجد إجازات', Icons.history)
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
                        itemCount: leaveProvider.leaveHistory.length,
                        itemBuilder: (context, index) {
                          final leave = leaveProvider.leaveHistory[index];
                          return LeaveHistoryCard(
                            leave: leave,
                            onTap: () => _showLeaveDetails(leave),
                            onCancel: leaveProvider.canCancelLeave(leave)
                                ? () => _cancelLeave(leaveProvider, leave.id)
                                : null,
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestsTab(LeaveProvider leaveProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            // Pending Requests
            if (leaveProvider.pendingLeaves.isNotEmpty) ...[
              _buildSectionHeader('الطلبات المعلقة', Icons.pending_actions),
              ...leaveProvider.pendingLeaves.map(
                (leave) => LeaveRequestCard(
                  leave: leave,
                  onCancel: () => _cancelLeave(leaveProvider, leave.id),
                ),
              ),
              const SizedBox(height: AppSizes.paddingL),
            ],

            // Upcoming Leaves
            if (leaveProvider.upcomingLeaves.isNotEmpty) ...[
              _buildSectionHeader('الإجازات القادمة', Icons.upcoming),
              ...leaveProvider.upcomingLeaves.map(
                (leave) => LeaveRequestCard(
                  leave: leave,
                  showActions: false,
                ),
              ),
              const SizedBox(height: AppSizes.paddingL),
            ],

            // Current Leaves
            if (leaveProvider.currentLeaves.isNotEmpty) ...[
              _buildSectionHeader('الإجازات الحالية', Icons.beach_access),
              ...leaveProvider.currentLeaves.map(
                (leave) => LeaveRequestCard(
                  leave: leave,
                  showActions: false,
                ),
              ),
            ],

            // Empty State
            if (leaveProvider.pendingLeaves.isEmpty &&
                leaveProvider.upcomingLeaves.isEmpty &&
                leaveProvider.currentLeaves.isEmpty)
              _buildEmptyState('لا توجد طلبات إجازات', Icons.pending_actions),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceSummary(LeaveProvider leaveProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص أرصدة الإجازات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الأيام',
                    '${leaveProvider.totalAllocatedDays}',
                    Icons.calendar_month,
                    Color(AppColors.infoColor),
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المستخدم',
                    '${leaveProvider.totalUsedDays}',
                    Icons.check_circle,
                    Color(AppColors.warningColor),
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المتبقي',
                    '${leaveProvider.totalRemainingDays}',
                    Icons.account_balance_wallet,
                    Color(AppColors.successColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppSizes.iconM),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingHolidays(LeaveProvider leaveProvider) {
    if (leaveProvider.upcomingHolidays.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإجازات الرسمية القادمة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            ...leaveProvider.upcomingHolidays.take(3).map(
              (holiday) => ListTile(
                leading: CircleAvatar(
                  backgroundColor: Color(AppColors.successColor).withOpacity(0.1),
                  child: Icon(
                    Icons.celebration,
                    color: Color(AppColors.successColor),
                  ),
                ),
                title: Text(holiday.event),
                subtitle: Text(_formatDate(holiday.eventDate)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        children: [
          Icon(
            icon,
            color: Color(AppColors.primaryColor),
            size: AppSizes.iconM,
          ),
          const SizedBox(width: AppSizes.paddingS),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Color(AppColors.textDisabledColor),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Color(AppColors.textSecondaryColor),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    final month = months[date.month];
    return '${date.day} $month ${date.year}';
  }

  Future<void> _selectYear(LeaveProvider leaveProvider) async {
    final currentYear = DateTime.now().year;
    final years = List.generate(5, (index) => currentYear - index);

    final selectedYear = await showDialog<int>(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('اختر السنة'),
        children: years.map((year) => SimpleDialogOption(
          onPressed: () => Navigator.pop(context, year),
          child: Text('$year'),
        )).toList(),
      ),
    );

    if (selectedYear != null) {
      leaveProvider.setSelectedYear(selectedYear);
    }
  }

  void _navigateToLeaveRequest({int? leaveTypeId}) {
    // TODO: Navigate to leave request screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شاشة طلب الإجازة قيد التطوير'),
        backgroundColor: Color(AppColors.infoColor),
      ),
    );
  }

  void _showLeaveDetails(dynamic leave) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الإجازة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('النوع', leave.leaveTypeName ?? 'غير محدد'),
            _buildDetailRow('من', _formatDate(leave.leaveFrom)),
            _buildDetailRow('إلى', _formatDate(leave.leaveTo)),
            _buildDetailRow('عدد الأيام', '${leave.numberOfDays}'),
            _buildDetailRow('الحالة', leave.status ?? 'غير محدد'),
            _buildDetailRow('السبب', leave.reason ?? 'غير محدد'),
            if (leave.managerNote?.isNotEmpty ?? false)
              _buildDetailRow('ملاحظة المدير', leave.managerNote!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Future<void> _cancelLeave(LeaveProvider leaveProvider, int leaveId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء طلب الإجازة'),
        content: const Text('هل أنت متأكد من رغبتك في إلغاء طلب الإجازة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('لا'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(AppColors.errorColor),
            ),
            child: const Text('نعم، إلغاء'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await leaveProvider.cancelLeaveRequest(leaveId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'تم إلغاء طلب الإجازة بنجاح' : 'فشل في إلغاء طلب الإجازة'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }
}
