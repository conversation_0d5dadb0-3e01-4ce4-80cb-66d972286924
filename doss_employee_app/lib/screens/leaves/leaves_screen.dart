import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class LeavesScreen extends StatefulWidget {
  const LeavesScreen({super.key});

  @override
  State<LeavesScreen> createState() => _LeavesScreenState();
}

class _LeavesScreenState extends State<LeavesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.leaves),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.beach_access,
              size: 64,
              color: Color(AppColors.textDisabledColor),
            ),
            SizedB<PERSON>(height: 16),
            Text(
              'شاشة الإجازات',
              style: TextStyle(
                fontSize: 18,
                color: Color(AppColors.textSecondaryColor),
              ),
            ),
            Sized<PERSON><PERSON>(height: 8),
            Text(
              'قيد التطوير...',
              style: TextStyle(
                fontSize: 14,
                color: Color(AppColors.textDisabledColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
