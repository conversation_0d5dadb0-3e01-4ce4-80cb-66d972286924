import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class PayrollScreen extends StatefulWidget {
  const PayrollScreen({super.key});

  @override
  State<PayrollScreen> createState() => _PayrollScreenState();
}

class _PayrollScreenState extends State<PayrollScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.payroll),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 64,
              color: Color(AppColors.textDisabledColor),
            ),
            SizedBox(height: 16),
            Text(
              'شاشة الرواتب',
              style: TextStyle(
                fontSize: 18,
                color: Color(AppColors.textSecondaryColor),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'قيد التطوير...',
              style: TextStyle(
                fontSize: 14,
                color: Color(AppColors.textDisabledColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
