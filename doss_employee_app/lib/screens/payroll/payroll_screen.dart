import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/payroll_provider.dart';
import '../../widgets/salary_overview_card.dart';
import '../../widgets/payroll_history_card.dart';
import '../../widgets/advance_card.dart';

class PayrollScreen extends StatefulWidget {
  const PayrollScreen({super.key});

  @override
  State<PayrollScreen> createState() => _PayrollScreenState();
}

class _PayrollScreenState extends State<PayrollScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final payrollProvider = Provider.of<PayrollProvider>(context, listen: false);
    await payrollProvider.refreshAll();
  }

  Future<void> _refreshData() async {
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.payroll),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.account_balance_wallet),
              text: 'الراتب',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: 'التاريخ',
            ),
            Tab(
              icon: Icon(Icons.money),
              text: 'السلف',
            ),
          ],
        ),
      ),
      body: Consumer<PayrollProvider>(
        builder: (context, payrollProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildSalaryTab(payrollProvider),
              _buildHistoryTab(payrollProvider),
              _buildAdvancesTab(payrollProvider),
            ],
          );
        },
      ),
      floatingActionButton: Consumer<PayrollProvider>(
        builder: (context, payrollProvider, child) {
          if (_tabController.index == 2 && payrollProvider.canRequestNewAdvance) {
            return FloatingActionButton.extended(
              onPressed: () => _requestAdvance(payrollProvider),
              icon: const Icon(Icons.add),
              label: const Text('طلب سلفة'),
              backgroundColor: Color(AppColors.primaryColor),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSalaryTab(PayrollProvider payrollProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            // Current Salary Overview
            SalaryOverviewCard(
              salary: payrollProvider.currentSalary,
              isLoading: payrollProvider.isLoading,
            ),

            const SizedBox(height: AppSizes.paddingM),

            // Recent Payrolls
            if (payrollProvider.recentPayrolls.isNotEmpty) ...[
              _buildSectionHeader('كشوف الرواتب الأخيرة', Icons.receipt),
              ...payrollProvider.recentPayrolls.map(
                (payroll) => PayrollHistoryCard(
                  payroll: payroll,
                  onTap: () => _showPayrollDetails(payroll),
                ),
              ),
            ],

            // Salary Statistics
            const SizedBox(height: AppSizes.paddingM),
            _buildSalaryStatistics(payrollProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab(PayrollProvider payrollProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Column(
        children: [
          // Year Selector
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Row(
              children: [
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingM,
                        vertical: AppSizes.paddingS,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'السنة: ${payrollProvider.selectedYear}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          IconButton(
                            icon: const Icon(Icons.calendar_month),
                            onPressed: () => _selectYear(payrollProvider),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Payroll History List
          Expanded(
            child: payrollProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : payrollProvider.payrollHistory.isEmpty
                    ? _buildEmptyState('لا توجد كشوف رواتب', Icons.receipt_long)
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
                        itemCount: payrollProvider.payrollHistory.length,
                        itemBuilder: (context, index) {
                          final payroll = payrollProvider.payrollHistory[index];
                          return PayrollHistoryCard(
                            payroll: payroll,
                            onTap: () => _showPayrollDetails(payroll),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancesTab(PayrollProvider payrollProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            // Advance Summary
            _buildAdvanceSummary(payrollProvider),

            const SizedBox(height: AppSizes.paddingM),

            // Active Advances
            if (payrollProvider.activeAdvances.isNotEmpty) ...[
              _buildSectionHeader('السلف النشطة', Icons.trending_down),
              ...payrollProvider.activeAdvances.map(
                (advance) => AdvanceCard(
                  advance: advance,
                  onTap: () => _showAdvanceDetails(advance),
                ),
              ),
              const SizedBox(height: AppSizes.paddingL),
            ],

            // Pending Advances
            if (payrollProvider.pendingAdvances.isNotEmpty) ...[
              _buildSectionHeader('السلف في الانتظار', Icons.pending_actions),
              ...payrollProvider.pendingAdvances.map(
                (advance) => AdvanceCard(
                  advance: advance,
                  onTap: () => _showAdvanceDetails(advance),
                ),
              ),
            ],

            // Empty State or Request Limit Message
            if (payrollProvider.activeAdvances.isEmpty &&
                payrollProvider.pendingAdvances.isEmpty) ...[
              if (payrollProvider.canRequestNewAdvance)
                _buildEmptyState('لا توجد سلف', Icons.money)
              else
                _buildRequestLimitMessage(payrollProvider.advanceRequestLimitMessage),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        children: [
          Icon(
            icon,
            color: Color(AppColors.primaryColor),
            size: AppSizes.iconM,
          ),
          const SizedBox(width: AppSizes.paddingS),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalaryStatistics(PayrollProvider payrollProvider) {
    final stats = payrollProvider.getYearlySalaryStats();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات الراتب السنوية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الراتب',
                    payrollProvider.formatCurrency(stats['total_gross'] ?? 0),
                    Icons.account_balance_wallet,
                    Color(AppColors.infoColor),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'صافي الراتب',
                    payrollProvider.formatCurrency(stats['total_net'] ?? 0),
                    Icons.money,
                    Color(AppColors.successColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvanceSummary(PayrollProvider payrollProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص السلف',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي السلف',
                    payrollProvider.formatCurrency(payrollProvider.totalAdvanceAmount),
                    Icons.trending_down,
                    Color(AppColors.warningColor),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الاستقطاع الشهري',
                    payrollProvider.formatCurrency(payrollProvider.monthlyAdvanceDeduction),
                    Icons.remove_circle,
                    Color(AppColors.errorColor),
                  ),
                ),
              ],
            ),
            if (!payrollProvider.canRequestNewAdvance) ...[
              const SizedBox(height: AppSizes.paddingM),
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingS),
                decoration: BoxDecoration(
                  color: Color(AppColors.warningColor).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: Color(AppColors.warningColor),
                      size: AppSizes.iconS,
                    ),
                    const SizedBox(width: AppSizes.paddingS),
                    Expanded(
                      child: Text(
                        payrollProvider.advanceRequestLimitMessage,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Color(AppColors.warningColor),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppSizes.iconM),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Color(AppColors.textDisabledColor),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Color(AppColors.textSecondaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestLimitMessage(String message) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Color(AppColors.warningColor),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Text(
              'تنبيه',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Color(AppColors.warningColor),
              ),
            ),
            const SizedBox(height: AppSizes.paddingS),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Color(AppColors.textSecondaryColor),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectYear(PayrollProvider payrollProvider) async {
    final currentYear = DateTime.now().year;
    final years = List.generate(5, (index) => currentYear - index);

    final selectedYear = await showDialog<int>(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('اختر السنة'),
        children: years.map((year) => SimpleDialogOption(
          onPressed: () => Navigator.pop(context, year),
          child: Text('$year'),
        )).toList(),
      ),
    );

    if (selectedYear != null) {
      payrollProvider.setSelectedYear(selectedYear);
    }
  }

  void _requestAdvance(PayrollProvider payrollProvider) {
    // TODO: Navigate to advance request screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شاشة طلب السلفة قيد التطوير'),
        backgroundColor: Color(AppColors.infoColor),
      ),
    );
  }

  void _showPayrollDetails(dynamic payroll) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('كشف راتب ${payroll.period}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('الراتب الأساسي', '${payroll.basicSalary} ريال'),
              _buildDetailRow('البدلات', '${payroll.allowances} ريال'),
              _buildDetailRow('العمل الإضافي', '${payroll.overtimeAmount} ريال'),
              _buildDetailRow('المكافآت', '${payroll.bonusAmount} ريال'),
              const Divider(),
              _buildDetailRow('إجمالي الراتب', '${payroll.grossSalary} ريال'),
              _buildDetailRow('الاستقطاعات', '${payroll.totalDeductions} ريال'),
              _buildDetailRow('صافي الراتب', '${payroll.netSalary} ريال', isTotal: true),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showAdvanceDetails(dynamic advance) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل السلفة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('المبلغ', '${advance.amount} ريال'),
            _buildDetailRow('عدد الأقساط', '${advance.installments}'),
            _buildDetailRow('القسط الشهري', '${advance.monthlyInstallment} ريال'),
            _buildDetailRow('المدفوع', '${advance.paidAmount} ريال'),
            _buildDetailRow('المتبقي', '${advance.remainingAmount} ريال'),
            _buildDetailRow('الحالة', advance.status ?? 'غير محدد'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Color(AppColors.primaryColor) : null,
            ),
          ),
        ],
      ),
    );
  }
}
