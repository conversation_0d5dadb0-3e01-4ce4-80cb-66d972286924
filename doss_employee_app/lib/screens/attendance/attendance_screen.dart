import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.attendance),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.access_time,
              size: 64,
              color: Color(AppColors.textDisabledColor),
            ),
            SizedBox(height: 16),
            Text(
              'شاشة الحضور والانصراف',
              style: TextStyle(
                fontSize: 18,
                color: Color(AppColors.textSecondaryColor),
              ),
            ),
            SizedB<PERSON>(height: 8),
            Text(
              'قيد التطوير...',
              style: TextStyle(
                fontSize: 14,
                color: Color(AppColors.textDisabledColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
