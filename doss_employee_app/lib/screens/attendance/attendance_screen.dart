import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/attendance_provider.dart';
import '../../widgets/attendance_status_card.dart';
import '../../widgets/attendance_history_card.dart';
import '../../widgets/attendance_summary_card.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final attendanceProvider = Provider.of<AttendanceProvider>(context, listen: false);
    await attendanceProvider.refreshAll();
  }

  Future<void> _refreshData() async {
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.attendance),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.today),
              text: 'اليوم',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: 'التاريخ',
            ),
            Tab(
              icon: Icon(Icons.analytics),
              text: 'الإحصائيات',
            ),
          ],
        ),
      ),
      body: Consumer<AttendanceProvider>(
        builder: (context, attendanceProvider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildTodayTab(attendanceProvider),
              _buildHistoryTab(attendanceProvider),
              _buildSummaryTab(attendanceProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTodayTab(AttendanceProvider attendanceProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            // Today's Attendance Status
            AttendanceStatusCard(
              todayAttendance: attendanceProvider.todayAttendance,
              isLoading: attendanceProvider.isLoading,
              onCheckIn: () async {
                final success = await attendanceProvider.checkIn();
                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تسجيل الدخول بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              onCheckOut: () async {
                final success = await attendanceProvider.checkOut();
                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تسجيل الخروج بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
            ),

            const SizedBox(height: AppSizes.paddingM),

            // Quick Actions
            _buildQuickActions(attendanceProvider),

            const SizedBox(height: AppSizes.paddingM),

            // Today's Summary
            if (attendanceProvider.todayAttendance != null)
              _buildTodaySummary(attendanceProvider.todayAttendance!),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab(AttendanceProvider attendanceProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Column(
        children: [
          // Month Selector
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Row(
              children: [
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingM,
                        vertical: AppSizes.paddingS,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'الشهر: ${_getMonthName(attendanceProvider.selectedMonth.month)} ${attendanceProvider.selectedMonth.year}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          IconButton(
                            icon: const Icon(Icons.calendar_month),
                            onPressed: () => _selectMonth(attendanceProvider),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Attendance History List
          Expanded(
            child: attendanceProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : attendanceProvider.attendanceHistory.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.event_busy,
                              size: 64,
                              color: Color(AppColors.textDisabledColor),
                            ),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد سجلات حضور',
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(AppColors.textSecondaryColor),
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
                        itemCount: attendanceProvider.attendanceHistory.length,
                        itemBuilder: (context, index) {
                          final attendance = attendanceProvider.attendanceHistory[index];
                          return AttendanceHistoryCard(
                            attendance: attendance,
                            onTap: () => _showAttendanceDetails(attendance),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryTab(AttendanceProvider attendanceProvider) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            // Summary Cards
            AttendanceSummaryCard(
              summary: attendanceProvider.attendanceSummary,
              isLoading: attendanceProvider.isLoading,
            ),

            const SizedBox(height: AppSizes.paddingM),

            // Statistics
            _buildStatisticsCards(attendanceProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(AttendanceProvider attendanceProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _requestLocationPermission(attendanceProvider),
                    icon: const Icon(Icons.location_on),
                    label: const Text('تفعيل الموقع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(AppColors.infoColor),
                    ),
                  ),
                ),
                const SizedBox(width: AppSizes.paddingS),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showAttendanceSettings(),
                    icon: const Icon(Icons.settings),
                    label: const Text('الإعدادات'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodaySummary(dynamic todayAttendance) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص اليوم',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'ساعات العمل',
                    '${todayAttendance.workingHours.toStringAsFixed(1)} ساعة',
                    Icons.access_time,
                    Color(AppColors.infoColor),
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'الحالة',
                    todayAttendance.status ?? 'غير محدد',
                    Icons.info,
                    _getStatusColor(todayAttendance.status ?? ''),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusS),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppSizes.iconM),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Color(AppColors.textSecondaryColor),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards(AttendanceProvider attendanceProvider) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'أيام الحضور',
                '${attendanceProvider.presentDaysCount}',
                Icons.check_circle,
                Color(AppColors.successColor),
              ),
            ),
            const SizedBox(width: AppSizes.paddingS),
            Expanded(
              child: _buildStatCard(
                'أيام الغياب',
                '${attendanceProvider.absentDaysCount}',
                Icons.cancel,
                Color(AppColors.errorColor),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSizes.paddingS),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'أيام التأخير',
                '${attendanceProvider.lateDaysCount}',
                Icons.schedule,
                Color(AppColors.warningColor),
              ),
            ),
            const SizedBox(width: AppSizes.paddingS),
            Expanded(
              child: _buildStatCard(
                'معدل الحضور',
                '${attendanceProvider.attendanceRate.toStringAsFixed(1)}%',
                Icons.trending_up,
                Color(AppColors.infoColor),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          children: [
            Icon(icon, color: color, size: AppSizes.iconL),
            const SizedBox(height: AppSizes.paddingS),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color(AppColors.textSecondaryColor),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month];
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'حاضر':
      case 'حضور منتظم':
        return Color(AppColors.successColor);
      case 'متأخر':
        return Color(AppColors.warningColor);
      case 'غائب':
        return Color(AppColors.errorColor);
      default:
        return Color(AppColors.textSecondaryColor);
    }
  }

  Future<void> _selectMonth(AttendanceProvider attendanceProvider) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: attendanceProvider.selectedMonth,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (picked != null) {
      attendanceProvider.setSelectedMonth(picked);
    }
  }

  Future<void> _requestLocationPermission(AttendanceProvider attendanceProvider) async {
    final hasPermission = await attendanceProvider.checkLocationPermission();
    if (!hasPermission) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('يرجى السماح بالوصول للموقع من إعدادات التطبيق'),
            action: SnackBarAction(
              label: 'الإعدادات',
              onPressed: () => attendanceProvider.openAppSettings(),
            ),
          ),
        );
      }
    }
  }

  void _showAttendanceSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إعدادات الحضور',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.paddingL),
            ListTile(
              leading: const Icon(Icons.location_on),
              title: const Text('تفعيل تتبع الموقع'),
              subtitle: const Text('لتسجيل الحضور من الموقع المحدد'),
              trailing: Switch(
                value: true,
                onChanged: (value) {},
              ),
            ),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('تذكير الحضور'),
              subtitle: const Text('إشعار يومي لتذكيرك بتسجيل الحضور'),
              trailing: Switch(
                value: true,
                onChanged: (value) {},
              ),
            ),
            const SizedBox(height: AppSizes.paddingL),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAttendanceDetails(dynamic attendance) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الحضور - ${attendance.date.toString().split(' ')[0]}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('تسجيل الدخول', attendance.checkInTime ?? 'لم يسجل'),
            _buildDetailRow('تسجيل الخروج', attendance.checkOutTime ?? 'لم يسجل'),
            _buildDetailRow('ساعات العمل', '${attendance.workingHours.toStringAsFixed(1)} ساعة'),
            _buildDetailRow('الحالة', attendance.status ?? 'غير محدد'),
            if (attendance.note != null && attendance.note!.isNotEmpty)
              _buildDetailRow('ملاحظة', attendance.note!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
