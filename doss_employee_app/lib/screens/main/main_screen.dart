import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import 'dashboard_screen.dart';
import '../attendance/attendance_screen.dart';
import '../leaves/leaves_screen.dart';
import '../payroll/payroll_screen.dart';
import '../profile/profile_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    const DashboardScreen(),
    const AttendanceScreen(),
    const LeavesScreen(),
    const PayrollScreen(),
    const ProfileScreen(),
  ];

  final List<BottomNavigationBarItem> _bottomNavItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.dashboard),
      label: AppStrings.dashboard,
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.access_time),
      label: AppStrings.attendance,
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.beach_access),
      label: AppStrings.leaves,
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.account_balance_wallet),
      label: AppStrings.payroll,
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.person),
      label: AppStrings.profile,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: _bottomNavItems,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Color(AppColors.primaryColor),
        unselectedItemColor: Color(AppColors.textSecondaryColor),
        backgroundColor: Colors.white,
        elevation: 8,
        selectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }
}
