import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/attendance_provider.dart';
import '../../providers/leave_provider.dart';
import '../../providers/payroll_provider.dart';
import '../../widgets/dashboard_card.dart';
import '../../widgets/quick_action_card.dart';
import '../../widgets/attendance_status_card.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    final attendanceProvider = Provider.of<AttendanceProvider>(context, listen: false);
    final leaveProvider = Provider.of<LeaveProvider>(context, listen: false);
    final payrollProvider = Provider.of<PayrollProvider>(context, listen: false);

    await Future.wait([
      attendanceProvider.getTodayAttendance(),
      leaveProvider.getLeaveBalance(),
      payrollProvider.getCurrentSalary(),
    ]);
  }

  Future<void> _refreshDashboard() async {
    await _loadDashboardData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(AppColors.backgroundColor),
      body: RefreshIndicator(
        onRefresh: _refreshDashboard,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            SliverPadding(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildWelcomeCard(),
                  const SizedBox(height: AppSizes.paddingM),
                  _buildAttendanceStatusCard(),
                  const SizedBox(height: AppSizes.paddingM),
                  _buildQuickActions(),
                  const SizedBox(height: AppSizes.paddingM),
                  _buildSummaryCards(),
                  const SizedBox(height: AppSizes.paddingM),
                  _buildRecentActivity(),
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return SliverAppBar(
          expandedHeight: 120,
          floating: false,
          pinned: true,
          backgroundColor: Color(AppColors.primaryColor),
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              'مرحباً، ${authProvider.userDisplayName}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(AppColors.primaryColor),
                    Color(AppColors.primaryDarkColor),
                  ],
                ),
              ),
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.white),
              onPressed: () {
                // TODO: Navigate to notifications
              },
            ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _refreshDashboard,
            ),
          ],
        );
      },
    );
  }

  Widget _buildWelcomeCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Color(AppColors.primaryLightColor),
                  backgroundImage: authProvider.hasUserAvatar
                      ? NetworkImage(authProvider.userAvatarUrl)
                      : null,
                  child: !authProvider.hasUserAvatar
                      ? Icon(
                          Icons.person,
                          size: 30,
                          color: Color(AppColors.primaryColor),
                        )
                      : null,
                ),
                const SizedBox(width: AppSizes.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        authProvider.userDisplayName,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        authProvider.userPosition,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Color(AppColors.textSecondaryColor),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        authProvider.userDepartment,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Color(AppColors.textSecondaryColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAttendanceStatusCard() {
    return Consumer<AttendanceProvider>(
      builder: (context, attendanceProvider, child) {
        return AttendanceStatusCard(
          todayAttendance: attendanceProvider.todayAttendance,
          isLoading: attendanceProvider.isLoading,
          onCheckIn: () async {
            final success = await attendanceProvider.checkIn();
            if (success && mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسجيل الدخول بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          },
          onCheckOut: () async {
            final success = await attendanceProvider.checkOut();
            if (success && mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسجيل الخروج بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          },
        );
      },
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: AppSizes.paddingM),
        Row(
          children: [
            Expanded(
              child: QuickActionCard(
                icon: Icons.beach_access,
                title: 'طلب إجازة',
                color: Color(AppColors.successColor),
                onTap: () {
                  // TODO: Navigate to leave request
                },
              ),
            ),
            const SizedBox(width: AppSizes.paddingS),
            Expanded(
              child: QuickActionCard(
                icon: Icons.account_balance_wallet,
                title: 'طلب سلفة',
                color: Color(AppColors.warningColor),
                onTap: () {
                  // TODO: Navigate to advance request
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSizes.paddingS),
        Row(
          children: [
            Expanded(
              child: QuickActionCard(
                icon: Icons.lightbulb,
                title: 'تقديم اقتراح',
                color: Color(AppColors.infoColor),
                onTap: () {
                  // TODO: Navigate to suggestion
                },
              ),
            ),
            const SizedBox(width: AppSizes.paddingS),
            Expanded(
              child: QuickActionCard(
                icon: Icons.report_problem,
                title: 'تقديم شكوى',
                color: Color(AppColors.errorColor),
                onTap: () {
                  // TODO: Navigate to complaint
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الملخص',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: AppSizes.paddingM),
        Consumer3<AttendanceProvider, LeaveProvider, PayrollProvider>(
          builder: (context, attendanceProvider, leaveProvider, payrollProvider, child) {
            return Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DashboardCard(
                        title: 'معدل الحضور',
                        value: '${attendanceProvider.attendanceRate.toStringAsFixed(1)}%',
                        icon: Icons.access_time,
                        color: Color(AppColors.presentColor),
                      ),
                    ),
                    const SizedBox(width: AppSizes.paddingS),
                    Expanded(
                      child: DashboardCard(
                        title: 'رصيد الإجازات',
                        value: '${leaveProvider.totalRemainingDays} يوم',
                        icon: Icons.beach_access,
                        color: Color(AppColors.successColor),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppSizes.paddingS),
                Row(
                  children: [
                    Expanded(
                      child: DashboardCard(
                        title: 'الراتب الحالي',
                        value: payrollProvider.currentSalary?.formattedTotalSalary ?? 'غير متاح',
                        icon: Icons.account_balance_wallet,
                        color: Color(AppColors.salaryColor),
                      ),
                    ),
                    const SizedBox(width: AppSizes.paddingS),
                    Expanded(
                      child: DashboardCard(
                        title: 'السلف النشطة',
                        value: '${payrollProvider.activeAdvances.length}',
                        icon: Icons.money,
                        color: Color(AppColors.advanceColor),
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاط الأخير',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: AppSizes.paddingM),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              children: [
                _buildActivityItem(
                  icon: Icons.access_time,
                  title: 'تسجيل الحضور',
                  subtitle: 'اليوم - 08:30 ص',
                  color: Color(AppColors.presentColor),
                ),
                const Divider(),
                _buildActivityItem(
                  icon: Icons.beach_access,
                  title: 'طلب إجازة سنوية',
                  subtitle: 'أمس - معتمد',
                  color: Color(AppColors.successColor),
                ),
                const Divider(),
                _buildActivityItem(
                  icon: Icons.account_balance_wallet,
                  title: 'كشف راتب ديسمبر',
                  subtitle: 'منذ 3 أيام - مدفوع',
                  color: Color(AppColors.salaryColor),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      contentPadding: EdgeInsets.zero,
    );
  }
}
