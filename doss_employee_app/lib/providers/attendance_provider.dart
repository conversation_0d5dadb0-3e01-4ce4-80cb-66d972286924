import 'package:flutter/foundation.dart';
import '../models/attendance.dart';
import '../services/attendance_service.dart';

class AttendanceProvider extends ChangeNotifier {
  final AttendanceService _attendanceService = AttendanceService();
  
  bool _isLoading = false;
  String? _errorMessage;
  TodayAttendance? _todayAttendance;
  List<Attendance> _attendanceHistory = [];
  AttendanceSummary? _attendanceSummary;
  DateTime _selectedMonth = DateTime.now();

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  TodayAttendance? get todayAttendance => _todayAttendance;
  List<Attendance> get attendanceHistory => _attendanceHistory;
  AttendanceSummary? get attendanceSummary => _attendanceSummary;
  DateTime get selectedMonth => _selectedMonth;

  // Computed getters
  bool get canCheckIn => _todayAttendance?.canCheckIn ?? true;
  bool get canCheckOut => _todayAttendance?.canCheckOut ?? false;
  bool get hasCheckedInToday => _todayAttendance?.hasCheckedIn ?? false;
  bool get hasCheckedOutToday => _todayAttendance?.hasCheckedOut ?? false;
  bool get isCompleteToday => _todayAttendance?.isComplete ?? false;

  // Get today's attendance
  Future<void> getTodayAttendance() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _attendanceService.getTodayAttendance();
      
      if (response.isSuccess) {
        _todayAttendance = response.data;
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب بيانات الحضور: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get attendance history
  Future<void> getAttendanceHistory({int? month, int? year}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _attendanceService.getAttendanceHistory(
        month: month ?? _selectedMonth.month,
        year: year ?? _selectedMonth.year,
      );
      
      if (response.isSuccess) {
        _attendanceHistory = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب تاريخ الحضور: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Check in
  Future<bool> checkIn({String? note, bool includeLocation = true}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _attendanceService.checkIn(
        note: note,
        includeLocation: includeLocation,
      );
      
      if (response.isSuccess) {
        // Refresh today's attendance
        await getTodayAttendance();
        _clearError();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الدخول: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check out
  Future<bool> checkOut({String? note, bool includeLocation = true}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _attendanceService.checkOut(
        note: note,
        includeLocation: includeLocation,
      );
      
      if (response.isSuccess) {
        // Refresh today's attendance
        await getTodayAttendance();
        _clearError();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الخروج: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get attendance summary
  Future<void> getAttendanceSummary({int? month, int? year}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _attendanceService.getAttendanceSummary(
        month: month ?? _selectedMonth.month,
        year: year ?? _selectedMonth.year,
      );
      
      if (response.isSuccess) {
        _attendanceSummary = response.data;
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب ملخص الحضور: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Set selected month
  void setSelectedMonth(DateTime month) {
    _selectedMonth = month;
    notifyListeners();
    
    // Refresh data for new month
    getAttendanceHistory();
    getAttendanceSummary();
  }

  // Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      getTodayAttendance(),
      getAttendanceHistory(),
      getAttendanceSummary(),
    ]);
  }

  // Get attendance for specific date
  Attendance? getAttendanceForDate(DateTime date) {
    try {
      return _attendanceHistory.firstWhere(
        (attendance) => 
            attendance.date.year == date.year &&
            attendance.date.month == date.month &&
            attendance.date.day == date.day,
      );
    } catch (e) {
      return null;
    }
  }

  // Get present days count
  int get presentDaysCount {
    return _attendanceHistory.where((a) => a.hasCheckedIn).length;
  }

  // Get absent days count
  int get absentDaysCount {
    return _attendanceHistory.where((a) => !a.hasCheckedIn).length;
  }

  // Get late days count
  int get lateDaysCount {
    return _attendanceHistory.where((a) => a.isLate).length;
  }

  // Get total working hours
  double get totalWorkingHours {
    return _attendanceHistory.fold(0.0, (sum, a) => sum + a.workingHours);
  }

  // Get total overtime hours
  double get totalOvertimeHours {
    return _attendanceHistory.fold(0.0, (sum, a) => sum + (a.overtime / 60));
  }

  // Get attendance rate
  double get attendanceRate {
    if (_attendanceHistory.isEmpty) return 0.0;
    return (presentDaysCount / _attendanceHistory.length) * 100;
  }

  // Check location permission
  Future<bool> checkLocationPermission() async {
    return await _attendanceService.requestLocationPermission();
  }

  // Check location service
  Future<bool> checkLocationService() async {
    return await _attendanceService.isLocationServiceEnabled();
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    await _attendanceService.openLocationSettings();
  }

  // Open app settings
  Future<void> openAppSettings() async {
    await _attendanceService.openAppSettings();
  }

  // Format working hours
  String formatWorkingHours(double hours) {
    return _attendanceService.formatWorkingHours(hours);
  }

  // Format minutes
  String formatMinutes(int minutes) {
    return _attendanceService.formatMinutes(minutes);
  }

  // Get status color
  int getStatusColor(String status) {
    return _attendanceService.getAttendanceStatusColor(status);
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _todayAttendance = null;
    _attendanceHistory.clear();
    _attendanceSummary = null;
    _errorMessage = null;
    _isLoading = false;
    _selectedMonth = DateTime.now();
    notifyListeners();
  }
}
