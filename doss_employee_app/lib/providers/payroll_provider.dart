import 'package:flutter/foundation.dart';
import '../models/payroll.dart';
import '../services/payroll_service.dart';

class PayrollProvider extends ChangeNotifier {
  final PayrollService _payrollService = PayrollService();
  
  bool _isLoading = false;
  String? _errorMessage;
  Salary? _currentSalary;
  List<Payroll> _payrollHistory = [];
  List<Advance> _advances = [];
  int _selectedYear = DateTime.now().year;

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Salary? get currentSalary => _currentSalary;
  List<Payroll> get payrollHistory => _payrollHistory;
  List<Advance> get advances => _advances;
  int get selectedYear => _selectedYear;

  // Computed getters
  List<Payroll> get recentPayrolls => _payrollService.getRecentPayrolls(_payrollHistory);
  List<Advance> get activeAdvances => _payrollService.getActiveAdvances(_advances);
  List<Advance> get pendingAdvances => _payrollService.getPendingAdvances(_advances);
  
  double get totalAdvanceAmount => _payrollService.calculateTotalAdvanceAmount(_advances);
  double get monthlyAdvanceDeduction => _payrollService.calculateMonthlyAdvanceDeduction(_advances);
  bool get canRequestNewAdvance => _payrollService.canRequestNewAdvance(_advances);
  String get advanceRequestLimitMessage => _payrollService.getAdvanceRequestLimitMessage(_advances);

  // Get current salary
  Future<void> getCurrentSalary() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _payrollService.getCurrentSalary();
      
      if (response.isSuccess) {
        _currentSalary = response.data;
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب بيانات الراتب: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get payroll history
  Future<void> getPayrollHistory({int? year, int? month}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _payrollService.getPayrollHistory(
        year: year ?? _selectedYear,
        month: month,
      );
      
      if (response.isSuccess) {
        _payrollHistory = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب تاريخ الرواتب: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get payroll details
  Future<Payroll?> getPayrollDetails(int payrollId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _payrollService.getPayrollDetails(payrollId);
      
      if (response.isSuccess) {
        _clearError();
        return response.data;
      } else {
        _setError(response.message);
        return null;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب تفاصيل كشف الراتب: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Get advances
  Future<void> getAdvances({String? status}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _payrollService.getAdvances(status: status);
      
      if (response.isSuccess) {
        _advances = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب بيانات السلف: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Request advance
  Future<bool> requestAdvance(AdvanceRequest request) async {
    _setLoading(true);
    _clearError();

    // Validate request
    final validationError = _payrollService.validateAdvanceRequest(request);
    if (validationError != null) {
      _setError(validationError);
      _setLoading(false);
      return false;
    }

    try {
      final response = await _payrollService.requestAdvance(request);
      
      if (response.isSuccess) {
        // Refresh advances
        await getAdvances();
        _clearError();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تقديم طلب السلفة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Set selected year
  void setSelectedYear(int year) {
    _selectedYear = year;
    notifyListeners();
    
    // Refresh data for new year
    getPayrollHistory();
  }

  // Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      getCurrentSalary(),
      getPayrollHistory(),
      getAdvances(),
    ]);
  }

  // Get payroll for specific month
  Payroll? getPayrollForMonth(int year, int month) {
    try {
      return _payrollHistory.firstWhere(
        (payroll) => payroll.year == year && payroll.month == month,
      );
    } catch (e) {
      return null;
    }
  }

  // Get advance by id
  Advance? getAdvanceById(int id) {
    try {
      return _advances.firstWhere((advance) => advance.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get payroll status color
  int getPayrollStatusColor(String status) {
    return _payrollService.getPayrollStatusColor(status);
  }

  // Get advance status color
  int getAdvanceStatusColor(String status) {
    return _payrollService.getAdvanceStatusColor(status);
  }

  // Format currency
  String formatCurrency(double amount) {
    return _payrollService.formatCurrency(amount);
  }

  // Calculate net salary percentage
  double calculateNetSalaryPercentage(Payroll payroll) {
    return _payrollService.calculateNetSalaryPercentage(payroll);
  }

  // Calculate deductions percentage
  double calculateDeductionsPercentage(Payroll payroll) {
    return _payrollService.calculateDeductionsPercentage(payroll);
  }

  // Get salary breakdown
  Map<String, double> getSalaryBreakdown(Payroll payroll) {
    return _payrollService.getSalaryBreakdown(payroll);
  }

  // Get deductions breakdown
  Map<String, double> getDeductionsBreakdown(Payroll payroll) {
    return _payrollService.getDeductionsBreakdown(payroll);
  }

  // Get advance payment progress
  double getAdvancePaymentProgress(Advance advance) {
    return _payrollService.getAdvancePaymentProgress(advance);
  }

  // Format advance installment info
  String formatAdvanceInstallmentInfo(Advance advance) {
    return _payrollService.formatAdvanceInstallmentInfo(advance);
  }

  // Get yearly salary statistics
  Map<String, double> getYearlySalaryStats() {
    if (_payrollHistory.isEmpty) {
      return {
        'total_gross': 0.0,
        'total_net': 0.0,
        'total_deductions': 0.0,
        'average_gross': 0.0,
        'average_net': 0.0,
      };
    }

    final totalGross = _payrollHistory.fold(0.0, (sum, p) => sum + p.grossSalary);
    final totalNet = _payrollHistory.fold(0.0, (sum, p) => sum + p.netSalary);
    final totalDeductions = _payrollHistory.fold(0.0, (sum, p) => sum + p.totalDeductions);
    final count = _payrollHistory.length;

    return {
      'total_gross': totalGross,
      'total_net': totalNet,
      'total_deductions': totalDeductions,
      'average_gross': totalGross / count,
      'average_net': totalNet / count,
    };
  }

  // Get monthly salary trend
  List<Map<String, dynamic>> getMonthlySalaryTrend() {
    final sortedPayrolls = List<Payroll>.from(_payrollHistory)
      ..sort((a, b) {
        final aDate = DateTime(a.year, a.month);
        final bDate = DateTime(b.year, b.month);
        return aDate.compareTo(bDate);
      });

    return sortedPayrolls.map((payroll) => {
      'month': payroll.month,
      'year': payroll.year,
      'period': payroll.period,
      'gross_salary': payroll.grossSalary,
      'net_salary': payroll.netSalary,
      'deductions': payroll.totalDeductions,
    }).toList();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _currentSalary = null;
    _payrollHistory.clear();
    _advances.clear();
    _errorMessage = null;
    _isLoading = false;
    _selectedYear = DateTime.now().year;
    notifyListeners();
  }
}
