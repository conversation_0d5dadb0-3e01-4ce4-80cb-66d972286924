import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _errorMessage;
  User? _currentUser;

  // Getters
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _authService.isLoggedIn;
  String? get errorMessage => _errorMessage;
  User? get currentUser => _currentUser ?? _authService.currentUser;
  String get userDisplayName => _authService.userDisplayName;
  String get userEmail => _authService.userEmail;
  String get userAvatarUrl => _authService.userAvatarUrl;
  bool get hasUserAvatar => _authService.hasUserAvatar;
  int? get userId => _authService.userId;
  String get userCompany => _authService.userCompany;
  String get userBranch => _authService.userBranch;
  String get userDepartment => _authService.userDepartment;
  String get userPosition => _authService.userPosition;

  // Initialize
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _setLoading(true);
    try {
      await _authService.initialize();
      _currentUser = _authService.currentUser;
      _isInitialized = true;
      _clearError();
    } catch (e) {
      _setError('حدث خطأ أثناء تهيئة التطبيق: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.login(email, password);
      
      if (response.isSuccess) {
        _currentUser = response.data;
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الدخول: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<bool> logout() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.logout();
      _currentUser = null;
      _clearError();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الخروج: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get profile
  Future<bool> getProfile() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.getProfile();
      
      if (response.isSuccess) {
        _currentUser = response.data;
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب الملف الشخصي: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.updateProfile(data);
      
      if (response.isSuccess) {
        _currentUser = response.data;
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث الملف الشخصي: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );
      
      if (response.isSuccess) {
        _clearError();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تغيير كلمة المرور: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check session validity
  Future<bool> checkSession() async {
    try {
      return await _authService.isSessionValid();
    } catch (e) {
      return false;
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    try {
      final response = await _authService.refreshToken();
      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }

  // Validate email
  String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(email)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    
    return null;
  }

  // Validate password
  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    
    if (password.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    return null;
  }

  // Validate confirm password
  String? validateConfirmPassword(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    
    if (password != confirmPassword) {
      return 'كلمات المرور غير متطابقة';
    }
    
    return null;
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _currentUser = null;
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }
}
