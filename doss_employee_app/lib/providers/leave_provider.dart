import 'package:flutter/foundation.dart';
import '../models/leave.dart';
import '../services/leave_service.dart';

class LeaveProvider extends ChangeNotifier {
  final LeaveService _leaveService = LeaveService();
  
  bool _isLoading = false;
  String? _errorMessage;
  List<LeaveBalance> _leaveBalances = [];
  List<Leave> _leaveHistory = [];
  List<LeaveType> _leaveTypes = [];
  List<Holiday> _holidays = [];
  LeaveSummary? _leaveSummary;
  int _selectedYear = DateTime.now().year;

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<LeaveBalance> get leaveBalances => _leaveBalances;
  List<Leave> get leaveHistory => _leaveHistory;
  List<LeaveType> get leaveTypes => _leaveTypes;
  List<Holiday> get holidays => _holidays;
  LeaveSummary? get leaveSummary => _leaveSummary;
  int get selectedYear => _selectedYear;

  // Computed getters
  List<Leave> get pendingLeaves => _leaveService.getPendingLeaves(_leaveHistory);
  List<Leave> get upcomingLeaves => _leaveService.getUpcomingLeaves(_leaveHistory);
  List<Leave> get currentLeaves => _leaveService.getCurrentLeaves(_leaveHistory);
  
  int get totalRemainingDays => _leaveBalances.fold(0, (sum, balance) => sum + balance.remainingDays);
  int get totalUsedDays => _leaveBalances.fold(0, (sum, balance) => sum + balance.usedDays);
  int get totalAllocatedDays => _leaveBalances.fold(0, (sum, balance) => sum + balance.allocatedDays);

  // Get leave balance
  Future<void> getLeaveBalance() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _leaveService.getLeaveBalance();
      
      if (response.isSuccess) {
        _leaveBalances = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب رصيد الإجازات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get leave history
  Future<void> getLeaveHistory({String? status, int? year}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _leaveService.getLeaveHistory(
        status: status,
        year: year ?? _selectedYear,
      );
      
      if (response.isSuccess) {
        _leaveHistory = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب تاريخ الإجازات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Submit leave request
  Future<bool> submitLeaveRequest(LeaveRequest request) async {
    _setLoading(true);
    _clearError();

    // Validate request
    final validationError = _leaveService.validateLeaveRequest(request);
    if (validationError != null) {
      _setError(validationError);
      _setLoading(false);
      return false;
    }

    try {
      final response = await _leaveService.submitLeaveRequest(request);
      
      if (response.isSuccess) {
        // Refresh leave history and balance
        await Future.wait([
          getLeaveHistory(),
          getLeaveBalance(),
        ]);
        _clearError();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تقديم طلب الإجازة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cancel leave request
  Future<bool> cancelLeaveRequest(int leaveId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _leaveService.cancelLeaveRequest(leaveId);
      
      if (response.isSuccess) {
        // Refresh leave history and balance
        await Future.wait([
          getLeaveHistory(),
          getLeaveBalance(),
        ]);
        _clearError();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء إلغاء طلب الإجازة: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get leave types
  Future<void> getLeaveTypes() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _leaveService.getLeaveTypes();
      
      if (response.isSuccess) {
        _leaveTypes = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب أنواع الإجازات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get holidays
  Future<void> getHolidays({int? year}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _leaveService.getHolidays(
        year: year ?? _selectedYear,
      );
      
      if (response.isSuccess) {
        _holidays = response.data ?? [];
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب الإجازات الرسمية: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get leave summary
  Future<void> getLeaveSummary({int? year}) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _leaveService.getLeaveSummary(
        year: year ?? _selectedYear,
      );
      
      if (response.isSuccess) {
        _leaveSummary = response.data;
        _clearError();
      } else {
        _setError(response.message);
      }
    } catch (e) {
      _setError('حدث خطأ أثناء جلب ملخص الإجازات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Set selected year
  void setSelectedYear(int year) {
    _selectedYear = year;
    notifyListeners();
    
    // Refresh data for new year
    getLeaveHistory();
    getHolidays();
    getLeaveSummary();
  }

  // Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      getLeaveBalance(),
      getLeaveHistory(),
      getLeaveTypes(),
      getHolidays(),
      getLeaveSummary(),
    ]);
  }

  // Get leave balance for specific type
  LeaveBalance? getLeaveBalanceForType(int leaveTypeId) {
    try {
      return _leaveBalances.firstWhere(
        (balance) => balance.leaveTypeId == leaveTypeId,
      );
    } catch (e) {
      return null;
    }
  }

  // Get leave type by id
  LeaveType? getLeaveTypeById(int id) {
    try {
      return _leaveTypes.firstWhere((type) => type.id == id);
    } catch (e) {
      return null;
    }
  }

  // Calculate leave days
  int calculateLeaveDays(DateTime from, DateTime to) {
    return _leaveService.calculateLeaveDays(from, to, holidays: _holidays);
  }

  // Check if can cancel leave
  bool canCancelLeave(Leave leave) {
    return _leaveService.canCancelLeave(leave);
  }

  // Get status color
  int getStatusColor(String status) {
    return _leaveService.getLeaveStatusColor(status);
  }

  // Get leave type icon
  String getLeaveTypeIcon(String leaveType) {
    return _leaveService.getLeaveTypeIcon(leaveType);
  }

  // Format leave duration
  String formatLeaveDuration(int days) {
    return _leaveService.formatLeaveDuration(days);
  }

  // Get upcoming holidays
  List<Holiday> get upcomingHolidays {
    final now = DateTime.now();
    return _holidays.where((holiday) => holiday.eventDate.isAfter(now)).toList()
      ..sort((a, b) => a.eventDate.compareTo(b.eventDate));
  }

  // Get today's holidays
  List<Holiday> get todayHolidays {
    final now = DateTime.now();
    return _holidays.where((holiday) => holiday.isToday).toList();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _leaveBalances.clear();
    _leaveHistory.clear();
    _leaveTypes.clear();
    _holidays.clear();
    _leaveSummary = null;
    _errorMessage = null;
    _isLoading = false;
    _selectedYear = DateTime.now().year;
    notifyListeners();
  }
}
