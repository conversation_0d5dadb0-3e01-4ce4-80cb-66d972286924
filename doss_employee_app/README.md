# نظام إدارة الموظفين - صيدلية دوس

تطبيق محمول لإدارة شؤون الموظفين في صيدلية دوس، مطور باستخدام Flutter ومتصل بـ Laravel Backend.

## المميزات

### 🔐 المصادقة والأمان
- تسجيل الدخول الآمن
- إدارة الجلسات
- تشفير البيانات

### ⏰ إدارة الحضور والانصراف
- تسجيل الحضور والانصراف
- تتبع الموقع الجغرافي
- عرض تاريخ الحضور
- إحصائيات الحضور الشهرية

### 🏖️ إدارة الإجازات
- طلب الإجازات المختلفة
- عرض رصيد الإجازات
- تتبع حالة الطلبات
- عرض الإجازات الرسمية

### 💰 إدارة الرواتب والسلف
- عرض كشوف الرواتب
- طلب السلف
- تتبع الاستقطاعات
- إحصائيات الرواتب

### 👤 الملف الشخصي
- عرض البيانات الشخصية
- تحديث المعلومات
- إدارة المستندات
- تغيير كلمة المرور

## التقنيات المستخدمة

### Frontend (Flutter)
- **Flutter**: إطار العمل الرئيسي
- **Provider**: إدارة الحالة
- **Dio**: التواصل مع APIs
- **Geolocator**: تحديد الموقع
- **SharedPreferences**: التخزين المحلي
- **Intl**: الدعم متعدد اللغات

### Backend (Laravel)
- **Laravel 10**: إطار العمل الرئيسي
- **MySQL**: قاعدة البيانات
- **JWT**: المصادقة
- **Laravel Sanctum**: إدارة الرموز المميزة

## متطلبات التشغيل

### Flutter
- Flutter SDK 3.8.1 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- Android Studio / VS Code
- Android SDK (للأندرويد)
- Xcode (لـ iOS)

### Laravel Backend
- PHP 8.1 أو أحدث
- Composer
- MySQL 8.0 أو أحدث
- Laravel 10

## التثبيت والتشغيل

### 1. إعداد Backend (Laravel)
```bash
# استنساخ المشروع
git clone [repository-url]
cd employee-management-backend

# تثبيت المكتبات
composer install

# إعداد البيئة
cp .env.example .env
php artisan key:generate

# إعداد قاعدة البيانات
php artisan migrate --seed

# تشغيل الخادم
php artisan serve
```

### 2. إعداد التطبيق (Flutter)
```bash
# الانتقال لمجلد التطبيق
cd doss_employee_app

# تثبيت المكتبات
flutter pub get

# تشغيل التطبيق
flutter run
```

## الإعدادات

### إعداد الـ API
قم بتحديث رابط الـ API في ملف `lib/constants/app_constants.dart`:

```dart
static const String baseUrl = 'http://your-domain.com/api';
```

## بنية المشروع

```
lib/
├── constants/          # الثوابت والإعدادات
├── models/            # نماذج البيانات
├── services/          # خدمات API
├── providers/         # إدارة الحالة
├── screens/           # الشاشات
│   ├── auth/         # شاشات المصادقة
│   ├── main/         # الشاشات الرئيسية
│   ├── attendance/   # شاشات الحضور
│   ├── leaves/       # شاشات الإجازات
│   ├── payroll/      # شاشات الرواتب
│   └── profile/      # شاشات الملف الشخصي
├── widgets/           # المكونات المشتركة
└── main.dart         # نقطة البداية
```

## الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/
```

### بناء التطبيق للإنتاج

#### Android
```bash
flutter build apk --release
# أو
flutter build appbundle --release
```

#### iOS
```bash
flutter build ios --release
```

## الإصدارات

### v1.0.0 (الحالي)
- الإصدار الأولي
- المصادقة وإدارة الجلسات
- إدارة الحضور والانصراف
- إدارة الإجازات
- إدارة الرواتب والسلف
- الملف الشخصي الأساسي

---

© 2024 صيدلية دوس. جميع الحقوق محفوظة.
