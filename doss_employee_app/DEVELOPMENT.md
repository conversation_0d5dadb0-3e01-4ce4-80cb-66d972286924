# دليل التطوير - نظام إدارة الموظفين

## الحالة الحالية للمشروع

### ✅ المكتمل
- **البنية الأساسية**: تم إعداد مشروع Flutter مع جميع المكتبات المطلوبة
- **نماذج البيانات**: تم إنشاء جميع النماذج (User, Attendance, Leave, Payroll, etc.)
- **خدمات API**: تم إنشاء خدمات التواصل مع Backend
- **إدارة الحالة**: تم إعداد Providers لجميع الوحدات
- **شاشة البداية**: Splash Screen مع الرسوم المتحركة
- **المصادقة**: شاشة تسجيل الدخول مع التحقق
- **الشاشة الرئيسية**: Dashboard مع الإحصائيات والاختصارات
- **التنقل**: Bottom Navigation مع 5 أقسام رئيسية

### 🚧 قيد التطوير
- شاشات الحضور والانصراف التفصيلية
- شاشات الإجازات التفصيلية  
- شاشات الرواتب والسلف التفصيلية
- شاشات الملف الشخصي التفصيلية
- شاشات شؤون الموظفين

## بنية المشروع

```
lib/
├── constants/
│   └── app_constants.dart          # الثوابت والألوان والنصوص
├── models/
│   ├── api_response.dart           # نماذج الاستجابة من API
│   ├── user.dart                   # نموذج المستخدم
│   ├── attendance.dart             # نماذج الحضور
│   ├── leave.dart                  # نماذج الإجازات
│   └── payroll.dart               # نماذج الرواتب
├── services/
│   ├── api_service.dart           # خدمة API الأساسية
│   ├── auth_service.dart          # خدمة المصادقة
│   ├── attendance_service.dart    # خدمة الحضور
│   ├── leave_service.dart         # خدمة الإجازات
│   └── payroll_service.dart       # خدمة الرواتب
├── providers/
│   ├── auth_provider.dart         # إدارة حالة المصادقة
│   ├── attendance_provider.dart   # إدارة حالة الحضور
│   ├── leave_provider.dart        # إدارة حالة الإجازات
│   └── payroll_provider.dart      # إدارة حالة الرواتب
├── screens/
│   ├── splash_screen.dart         # شاشة البداية
│   ├── auth/
│   │   └── login_screen.dart      # شاشة تسجيل الدخول
│   ├── main/
│   │   ├── main_screen.dart       # الشاشة الرئيسية مع التنقل
│   │   └── dashboard_screen.dart  # لوحة التحكم
│   ├── attendance/
│   │   └── attendance_screen.dart # شاشات الحضور (أساسية)
│   ├── leaves/
│   │   └── leaves_screen.dart     # شاشات الإجازات (أساسية)
│   ├── payroll/
│   │   └── payroll_screen.dart    # شاشات الرواتب (أساسية)
│   └── profile/
│       └── profile_screen.dart    # شاشة الملف الشخصي
├── widgets/
│   ├── dashboard_card.dart        # بطاقة الإحصائيات
│   ├── quick_action_card.dart     # بطاقة الإجراءات السريعة
│   └── attendance_status_card.dart # بطاقة حالة الحضور
└── main.dart                      # نقطة البداية
```

## المكتبات المستخدمة

### الأساسية
- `flutter`: إطار العمل
- `provider`: إدارة الحالة
- `dio`: HTTP client
- `shared_preferences`: التخزين المحلي
- `intl`: التدويل والتواريخ

### الوظائف
- `geolocator`: تحديد الموقع
- `permission_handler`: إدارة الأذونات
- `file_picker`: اختيار الملفات
- `image_picker`: اختيار الصور

### واجهة المستخدم
- `material_design_icons_flutter`: أيقونات إضافية
- `flutter_spinkit`: مؤشرات التحميل
- `shimmer`: تأثيرات التحميل
- `cached_network_image`: تخزين الصور مؤقتاً
- `fl_chart`: الرسوم البيانية

## إعدادات التطوير

### VS Code
تم إعداد ملفات VS Code في `.vscode/`:
- `launch.json`: إعدادات التشغيل والتصحيح
- `settings.json`: إعدادات المحرر والتنسيق

### Scripts
- `run_app.sh`: سكريبت تشغيل التطبيق مع خيارات متعددة

## الخطوات التالية

### 1. إكمال شاشات الحضور والانصراف
- شاشة تسجيل الحضور مع الموقع
- شاشة عرض تاريخ الحضور
- شاشة الإحصائيات الشهرية
- شاشة طلبات التصحيح

### 2. إكمال شاشات الإجازات
- شاشة طلب إجازة جديدة
- شاشة عرض رصيد الإجازات
- شاشة تاريخ الإجازات
- شاشة الإجازات الرسمية

### 3. إكمال شاشات الرواتب
- شاشة عرض كشف الراتب
- شاشة طلب سلفة
- شاشة تاريخ الرواتب
- شاشة الإحصائيات المالية

### 4. إكمال الملف الشخصي
- شاشة تحديث البيانات الشخصية
- شاشة تغيير كلمة المرور
- شاشة إدارة المستندات
- شاشة الإعدادات

### 5. شؤون الموظفين
- شاشة المكافآت والخصومات
- شاشة النقل والترقيات
- شاشة الاقتراحات والشكاوى
- شاشة التحذيرات

## ملاحظات مهمة

### الأمان
- جميع الطلبات محمية بـ JWT tokens
- البيانات الحساسة مشفرة محلياً
- التحقق من الأذونات على مستوى API

### الأداء
- استخدام Provider لإدارة الحالة بكفاءة
- تخزين البيانات مؤقتاً لتقليل طلبات API
- تحسين الصور والموارد

### التجربة
- دعم اللغة العربية بالكامل
- تصميم متجاوب لجميع الأحجام
- رسوم متحركة سلسة
- معالجة الأخطاء بوضوح

## التشغيل والاختبار

### تشغيل التطبيق
```bash
# الطريقة السريعة
./run_app.sh

# أو يدوياً
flutter clean
flutter pub get
flutter run
```

### الاختبار
```bash
# اختبارات الوحدة
flutter test

# تحليل الكود
flutter analyze

# بناء للإنتاج
flutter build apk --release
```

## الدعم والمساعدة

للحصول على المساعدة في التطوير:
1. راجع التوثيق في `README.md`
2. تحقق من الأخطاء باستخدام `flutter doctor`
3. استخدم `flutter analyze` للتحقق من جودة الكود
4. راجع logs التطبيق للأخطاء التشغيلية

---

آخر تحديث: ديسمبر 2024
