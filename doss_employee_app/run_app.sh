#!/bin/bash

# نظام إدارة الموظفين - صيدلية دوس
# سكريبت تشغيل التطبيق

echo "🚀 بدء تشغيل تطبيق إدارة الموظفين..."

# التحقق من وجود Flutter
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter غير مثبت. يرجى تثبيت Flutter أولاً."
    exit 1
fi

# التحقق من إصدار Flutter
echo "📱 التحقق من إصدار Flutter..."
flutter --version

# تنظيف المشروع
echo "🧹 تنظيف المشروع..."
flutter clean

# تحميل المكتبات
echo "📦 تحميل المكتبات..."
flutter pub get

# تشغيل التطبيق
echo "🎯 تشغيل التطبيق..."
echo "اختر المنصة:"
echo "1) Android"
echo "2) iOS"
echo "3) Web"
echo "4) تشغيل تلقائي"

read -p "أدخل اختيارك (1-4): " choice

case $choice in
    1)
        echo "🤖 تشغيل على Android..."
        flutter run -d android
        ;;
    2)
        echo "🍎 تشغيل على iOS..."
        flutter run -d ios
        ;;
    3)
        echo "🌐 تشغيل على Web..."
        flutter run -d web-server --web-port 8080
        ;;
    4)
        echo "⚡ تشغيل تلقائي..."
        flutter run
        ;;
    *)
        echo "❌ اختيار غير صحيح. تشغيل تلقائي..."
        flutter run
        ;;
esac

echo "✅ انتهى تشغيل التطبيق."
