{"dart.flutterSdkPath": "", "dart.lineLength": 100, "dart.insertArgumentPlaceholders": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.hotReloadOnSave": "always", "dart.openDevTools": "flutter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "files.associations": {"*.dart": "dart"}, "emmet.includeLanguages": {"dart": "html"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.flutter-plugins": true, "**/.flutter-plugins-dependencies": true, "**/.packages": true, "**/pubspec.lock": true}, "search.exclude": {"**/build": true, "**/.flutter-plugins": true, "**/.flutter-plugins-dependencies": true, "**/.packages": true, "**/pubspec.lock": true}}