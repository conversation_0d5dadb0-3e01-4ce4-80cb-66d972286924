# ⚙️ دليل إعدادات العميل - نظام إدارة الموظفين

## 🎨 تخصيص العلامة التجارية

### 1. تحديث معلومات الشركة

#### في لوحة التحكم Laravel:
**ملف:** `DossBackend/.env`
```env
APP_NAME="صيدلية دوس - نظام الموظفين"
COMPANY_NAME="صيدلية دوس"
COMPANY_EMAIL="<EMAIL>"
COMPANY_PHONE="+966123456789"
COMPANY_ADDRESS="الرياض، المملكة العربية السعودية"
```

#### في التطبيق Flutter:
**ملف:** `doss_employee_app/lib/constants/app_constants.dart`
```dart
class AppStrings {
  static const String appName = 'صيدلية دوس';
  static const String companyName = 'صيدلية دوس';
  static const String companyEmail = '<EMAIL>';
  static const String companyPhone = '+966123456789';
}
```

### 2. تخصيص الألوان

**ملف:** `doss_employee_app/lib/constants/app_constants.dart`
```dart
class AppColors {
  // الألوان الأساسية - يمكن تعديلها حسب هوية الشركة
  static const int primaryColor = 0xFF2E7D32;      // أخضر داكن
  static const int primaryLightColor = 0xFF4CAF50;  // أخضر فاتح
  static const int primaryDarkColor = 0xFF1B5E20;   // أخضر غامق
  static const int secondaryColor = 0xFF00BCD4;     // سماوي
  
  // يمكن تغيير هذه الألوان لتتناسب مع هوية صيدلية دوس
}
```

### 3. تحديث الشعار

#### شعار التطبيق:
- **المسار:** `doss_employee_app/assets/images/logo.png`
- **الحجم المطلوب:** 512x512 بكسل
- **التنسيق:** PNG مع خلفية شفافة

#### أيقونة التطبيق:
- **المسار:** `doss_employee_app/android/app/src/main/res/`
- **الأحجام المطلوبة:** 
  - mipmap-hdpi: 72x72
  - mipmap-mdpi: 48x48
  - mipmap-xhdpi: 96x96
  - mipmap-xxhdpi: 144x144
  - mipmap-xxxhdpi: 192x192

## 🏢 إعداد بيانات الشركة

### 1. الأقسام والفروع

**في لوحة التحكم:**
1. اذهب إلى "إدارة الشركة" > "الأقسام"
2. أضف الأقسام المطلوبة:
   - الصيدلة
   - المبيعات
   - المحاسبة
   - الموارد البشرية
   - التسويق
   - خدمة العملاء

3. اذهب إلى "إدارة الشركة" > "الفروع"
4. أضف الفروع المطلوبة:
   - الفرع الرئيسي
   - فرع الملز
   - فرع العليا
   - (أضف حسب الحاجة)

### 2. أنواع الإجازات

**في قاعدة البيانات أو لوحة التحكم:**
```sql
INSERT INTO leave_types (name, days, company_id, is_active) VALUES
('إجازة سنوية', 30, 1, 1),
('إجازة مرضية', 15, 1, 1),
('إجازة طارئة', 5, 1, 1),
('إجازة أمومة', 70, 1, 1),
('إجازة أبوة', 3, 1, 1),
('إجازة حج', 15, 1, 1),
('إجازة عمرة', 7, 1, 1);
```

### 3. سياسات العمل

**ملف:** `DossBackend/.env`
```env
# أوقات العمل
WORK_START_TIME=08:00
WORK_END_TIME=17:00
BREAK_DURATION=60
LATE_THRESHOLD=15

# سياسات الراتب
BASIC_SALARY_PERCENTAGE=70
ALLOWANCES_PERCENTAGE=20
INSURANCE_PERCENTAGE=9
TAX_PERCENTAGE=0

# سياسات السلف
MAX_ADVANCE_PERCENTAGE=50
MAX_ADVANCE_INSTALLMENTS=12
```

## 🔐 إعدادات الأمان

### 1. كلمات المرور

**في لوحة التحكم:**
- الحد الأدنى لطول كلمة المرور: 8 أحرف
- يجب أن تحتوي على أحرف وأرقام
- تغيير كلمة المرور كل 90 يوم (اختياري)

### 2. جلسات المستخدمين

**ملف:** `DossBackend/config/session.php`
```php
'lifetime' => 480, // 8 ساعات
'expire_on_close' => true,
```

### 3. النسخ الاحتياطية

**إعداد نسخ احتياطية تلقائية:**
```bash
# إضافة إلى crontab
0 2 * * * mysqldump -u root -p doss_pharmacy > /backup/doss_$(date +\%Y\%m\%d).sql
```

## 📱 إعدادات التطبيق المحمول

### 1. رابط الخادم

**ملف:** `doss_employee_app/lib/constants/api_constants.dart`
```dart
class ApiConstants {
  // للتطوير المحلي
  static const String baseUrl = 'http://10.0.2.2:8000/api';
  
  // للخادم الحقيقي - غير هذا الرابط
  // static const String baseUrl = 'https://your-domain.com/api';
}
```

### 2. إعدادات الحضور

**ملف:** `doss_employee_app/lib/constants/app_constants.dart`
```dart
class AttendanceSettings {
  static const bool requireLocation = true;        // تتطلب الموقع
  static const double allowedRadius = 100.0;       // نطاق مسموح بالمتر
  static const bool allowHomeWork = false;         // السماح بالعمل من المنزل
  static const int lateThresholdMinutes = 15;      // حد التأخير بالدقائق
}
```

### 3. إعدادات الإشعارات

```dart
class NotificationSettings {
  static const bool enablePushNotifications = true;
  static const bool enableAttendanceReminder = true;
  static const String reminderTime = '08:00';      // وقت التذكير
}
```

## 🌐 إعدادات الخادم

### 1. متطلبات الخادم

**الحد الأدنى:**
- PHP 8.1+
- MySQL 8.0+
- 2GB RAM
- 20GB مساحة تخزين
- SSL Certificate

**المُوصى به:**
- PHP 8.2+
- MySQL 8.0+
- 4GB RAM
- 50GB مساحة تخزين
- CDN للملفات الثابتة

### 2. إعدادات Apache/Nginx

**Apache (.htaccess):**
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^ index.php [L]
```

**Nginx:**
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

### 3. إعدادات PHP

**php.ini:**
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

## 📧 إعدادات البريد الإلكتروني

**ملف:** `DossBackend/.env`
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="صيدلية دوس"
```

## 🔄 إعدادات النسخ الاحتياطية

### 1. قاعدة البيانات

**نسخ احتياطية يومية:**
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p doss_pharmacy > /backup/doss_$DATE.sql
find /backup -name "doss_*.sql" -mtime +7 -delete
```

### 2. الملفات

**نسخ احتياطية أسبوعية:**
```bash
#!/bin/bash
DATE=$(date +%Y%m%d)
tar -czf /backup/files_$DATE.tar.gz /var/www/html/doss-system
find /backup -name "files_*.tar.gz" -mtime +30 -delete
```

## 📊 إعدادات التقارير

### 1. تقارير تلقائية

**تقرير حضور شهري:**
- يُرسل في أول كل شهر
- يحتوي على إحصائيات الشهر السابق
- يُرسل للإدارة والموارد البشرية

**تقرير رواتب:**
- يُرسل مع كل كشف راتب
- يحتوي على تفاصيل الراتب
- يُرسل للموظف والمحاسبة

### 2. تصدير البيانات

**تنسيقات مدعومة:**
- PDF للتقارير الرسمية
- Excel للبيانات التفصيلية
- CSV للتحليل الخارجي

## 🎯 قائمة مراجعة الإعدادات

### قبل النشر:
- [ ] تحديث معلومات الشركة
- [ ] تخصيص الألوان والشعار
- [ ] إضافة الأقسام والفروع
- [ ] تحديد أنواع الإجازات
- [ ] إعداد سياسات العمل
- [ ] تكوين إعدادات الأمان
- [ ] اختبار البريد الإلكتروني
- [ ] إعداد النسخ الاحتياطية
- [ ] تحديث رابط الخادم في التطبيق
- [ ] اختبار شامل للنظام

### بعد النشر:
- [ ] مراقبة الأداء
- [ ] مراجعة السجلات
- [ ] تحديث النسخ الاحتياطية
- [ ] تدريب المستخدمين
- [ ] جمع الملاحظات
- [ ] تحديثات دورية

---

**هذا الدليل يساعدك في تخصيص النظام ليناسب احتياجات صيدلية دوس بالضبط.**
