# API Documentation - نظام إدارة الموظفين

## نظرة عامة
هذا المستند يحتوي على توثيق شامل لجميع APIs المتاحة في نظام إدارة الموظفين.

## المصادقة
جميع APIs تتطلب مصادقة باستخدام Bearer Token.

```
Authorization: Bearer {token}
```

## Base URL
```
{domain}/api/
```

---

## 1. APIs المصادقة

### تسجيل الدخول
```
POST /login
```

**Parameters:**
- `email` (string, required)
- `password` (string, required)

---

## 2. APIs الرواتب والسلف

### الحصول على الراتب الحالي
```
GET /payroll/current-salary
```

### تاريخ الرواتب
```
GET /payroll/history?year=2024&month=12
```

### تفاصيل كشف راتب محدد
```
GET /payroll/details/{id}
```

### قائمة السلف
```
GET /payroll/advances?status=pending
```

### طلب سلفة جديدة
```
POST /payroll/request-advance
```

**Parameters:**
- `amount` (numeric, required, min:1, max:50000)
- `reason` (string, required, max:500)
- `installments` (integer, required, min:1, max:12)

---

## 3. APIs شؤون الموظفين

### المكافآت
```
GET /employee-affairs/bonuses?status=approved&type=performance
```

### الخصومات
```
GET /employee-affairs/deductions?status=pending
```

### النقل
```
GET /employee-affairs/transfers
```

### الترقيات
```
GET /employee-affairs/promotions
```

### تقديم اقتراح
```
POST /employee-affairs/submit-suggestion
```

**Parameters:**
- `title` (string, required, max:255)
- `description` (string, required, max:1000)
- `category` (enum, required): process_improvement, cost_reduction, quality_improvement, safety, technology, other
- `expected_benefit` (string, optional, max:500)

### قائمة الاقتراحات
```
GET /employee-affairs/suggestions
```

### تقديم شكوى
```
POST /employee-affairs/submit-complaint
```

**Parameters:**
- `title` (string, required, max:255)
- `description` (string, required, max:1000)
- `category` (enum, required): workplace_harassment, discrimination, safety_violation, policy_violation, management_issue, other
- `priority` (enum, required): low, medium, high, urgent

### قائمة الشكاوى
```
GET /employee-affairs/complaints
```

### الإنذارات
```
GET /employee-affairs/warnings
```

---

## 4. APIs التأمينات

### بيانات التأمين
```
GET /insurance/details
```

### المعالين
```
GET /insurance/dependents
```

### إضافة معال
```
POST /insurance/add-dependent
```

**Parameters:**
- `name` (string, required, max:255)
- `relationship` (enum, required): spouse, child, parent, sibling, other
- `date_of_birth` (date, required, before:today)
- `id_number` (string, optional, max:20)
- `is_insured` (boolean)
- `insurance_start_date` (date, optional)
- `insurance_end_date` (date, optional, after:insurance_start_date)
- `notes` (string, optional, max:500)

### تحديث معال
```
PUT /insurance/update-dependent/{id}
```

### حذف معال
```
DELETE /insurance/delete-dependent/{id}
```

### مؤسسات التأمين
```
GET /insurance/institutions
```

### ملخص التأمين
```
GET /insurance/summary
```

---

## 5. APIs الحضور والانصراف المحسنة

### حضور اليوم
```
GET /attendance/today
```

### تاريخ الحضور
```
GET /attendance/history?month=12&year=2024
```

### تسجيل الدخول
```
POST /attendance/check-in
```

**Parameters:**
- `latitude` (numeric, optional)
- `longitude` (numeric, optional)
- `note` (string, optional, max:255)

### تسجيل الخروج
```
POST /attendance/check-out
```

**Parameters:**
- `latitude` (numeric, optional)
- `longitude` (numeric, optional)
- `note` (string, optional, max:255)

### ملخص الحضور
```
GET /attendance/summary?month=12&year=2024
```

---

## 6. APIs الإجازات المحسنة

### رصيد الإجازات
```
GET /leaves/balance
```

### تاريخ الإجازات
```
GET /leaves/history?status=approved&year=2024
```

### تقديم طلب إجازة
```
POST /leaves/submit-request
```

**Parameters:**
- `leave_type_id` (integer, required, exists:leave_types,id)
- `leave_from` (date, required, after_or_equal:today)
- `leave_to` (date, required, after_or_equal:leave_from)
- `reason` (string, required, max:500)
- `early_exit` (boolean)

### إلغاء طلب إجازة
```
PUT /leaves/cancel-request/{id}
```

### أنواع الإجازات
```
GET /leaves/types
```

### الإجازات الرسمية
```
GET /leaves/holidays?year=2024
```

### ملخص الإجازات
```
GET /leaves/summary?year=2024
```

---

## 7. APIs الملف الشخصي المحسن

### الملف الشخصي
```
GET /profile
```

### تحديث الملف الشخصي
```
POST /profile/update
```

**Parameters:**
- `name` (string, optional, max:255)
- `phone` (string, optional, max:20)
- `emergency_contact_number` (string, optional, max:20)
- `address` (string, optional, max:500)
- `marital_status` (enum, optional): single, married
- `avatar` (file, optional): image, mimes:jpeg,png,jpg, max:2048KB

### تغيير كلمة المرور
```
POST /profile/change-password
```

**Parameters:**
- `current_password` (string, required)
- `new_password` (string, required, min:8, confirmed)
- `new_password_confirmation` (string, required)

### المستندات
```
GET /profile/documents
```

### رفع مستند
```
POST /profile/upload-document
```

**Parameters:**
- `document_type` (enum, required): id_copy, passport, contract, certificate, medical_report, other
- `document_name` (string, required, max:255)
- `file` (file, required): mimes:pdf,doc,docx,jpg,jpeg,png, max:5120KB
- `expiry_date` (date, optional, after:today)
- `notes` (string, optional, max:500)

### حذف مستند
```
DELETE /profile/delete-document/{id}
```

### ملخص الملف الشخصي
```
GET /profile/summary
```

---

## 8. APIs أخرى موجودة

### لوحة التحكم
```
GET /dashboard
```

### الإشعارات
```
GET /notifications
```

### الإعلانات
```
GET /notices
```

### الإجازات الرسمية
```
GET /holidays
```

### المحتوى الثابت
```
GET /static-page-content/{contentType}
GET /company-rules
```

---

## استجابات APIs

### استجابة ناجحة
```json
{
    "success": true,
    "message": "تم جلب البيانات بنجاح",
    "data": {...}
}
```

### استجابة خطأ
```json
{
    "success": false,
    "message": "رسالة الخطأ",
    "errors": {...}
}
```

---

## رموز الحالة

- `200` - نجح الطلب
- `201` - تم إنشاء المورد بنجاح
- `400` - خطأ في الطلب
- `401` - غير مصرح
- `403` - ممنوع
- `404` - غير موجود
- `422` - بيانات غير صحيحة
- `500` - خطأ في الخادم

---

## ملاحظات مهمة

1. جميع التواريخ بصيغة `Y-m-d` (2024-12-07)
2. جميع الأوقات بصيغة `H:i:s` (14:30:00)
3. المبالغ المالية بصيغة decimal مع خانتين عشريتين
4. الملفات المرفوعة يتم حفظها في مجلد `storage/uploads/`
5. الصور الشخصية يتم حفظها في `storage/uploads/user/avatar/`
6. مستندات الموظفين يتم حفظها في `storage/uploads/employee/documents/{user_id}/`
