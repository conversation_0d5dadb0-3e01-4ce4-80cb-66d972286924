# تصميم نظام شؤون العاملين - Employee Affairs System Design

## نظرة عامة - Overview

نظام شامل لإدارة شؤون العاملين في صيدلية دوس يشمل 8 وحدات رئيسية مع نظام تقارير متقدم.

## الوحدات الرئيسية - Main Modules

### 1. المكافآت - Bonuses
**الجدول**: `employee_bonuses`
**الوصف**: إدارة المكافآت المالية والعينية للموظفين

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `bonus_type` - نوع المكافأة (performance, annual, special, achievement)
- `bonus_category` - فئة المكافأة (financial, in_kind, promotion)
- `amount` - المبلغ (للمكافآت المالية)
- `description` - وصف المكافأة
- `reason` - سبب المنح
- `approval_status` - حالة الموافقة (pending, approved, rejected)
- `approved_by` - معرف المعتمد (FK)
- `approved_at` - تاريخ الاعتماد
- `effective_date` - تاريخ السريان
- `payment_date` - تاريخ الصرف
- `notes` - ملاحظات
- `created_by` - منشئ السجل (FK)
- `updated_by` - محدث السجل (FK)
- `created_at`, `updated_at`

### 2. الخصومات - Deductions
**الجدول**: `employee_deductions`
**الوصف**: إدارة الخصومات من راتب الموظف

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `deduction_type` - نوع الخصم (disciplinary, loan, advance, insurance, tax)
- `amount` - مبلغ الخصم
- `description` - وصف الخصم
- `reason` - سبب الخصم
- `approval_status` - حالة الموافقة
- `approved_by` - معرف المعتمد (FK)
- `approved_at` - تاريخ الاعتماد
- `effective_date` - تاريخ السريان
- `installments` - عدد الأقساط (للقروض)
- `installment_amount` - مبلغ القسط
- `remaining_amount` - المبلغ المتبقي
- `is_recurring` - خصم متكرر (boolean)
- `recurrence_period` - فترة التكرار (monthly, quarterly, yearly)
- `notes` - ملاحظات
- `created_by`, `updated_by`
- `created_at`, `updated_at`

### 3. النقل - Transfers
**الجدول**: `employee_transfers`
**الوصف**: إدارة نقل الموظفين بين الفروع والإدارات

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `transfer_type` - نوع النقل (branch, department, position, temporary, permanent)
- `from_branch_id` - الفرع السابق (FK)
- `to_branch_id` - الفرع الجديد (FK)
- `from_department_id` - الإدارة السابقة (FK)
- `to_department_id` - الإدارة الجديدة (FK)
- `from_post_id` - الوظيفة السابقة (FK)
- `to_post_id` - الوظيفة الجديدة (FK)
- `reason` - سبب النقل
- `request_date` - تاريخ الطلب
- `effective_date` - تاريخ السريان
- `end_date` - تاريخ الانتهاء (للنقل المؤقت)
- `approval_status` - حالة الموافقة
- `approved_by` - معرف المعتمد (FK)
- `approved_at` - تاريخ الاعتماد
- `notes` - ملاحظات
- `created_by`, `updated_by`
- `created_at`, `updated_at`

### 4. الترقيات - Promotions
**الجدول**: `employee_promotions`
**الوصف**: إدارة ترقيات الموظفين

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `promotion_type` - نوع الترقية (position, salary, grade)
- `from_post_id` - الوظيفة السابقة (FK)
- `to_post_id` - الوظيفة الجديدة (FK)
- `old_salary` - الراتب السابق
- `new_salary` - الراتب الجديد
- `salary_increase` - مقدار الزيادة
- `old_grade` - الدرجة السابقة
- `new_grade` - الدرجة الجديدة
- `reason` - سبب الترقية
- `effective_date` - تاريخ السريان
- `approval_status` - حالة الموافقة
- `approved_by` - معرف المعتمد (FK)
- `approved_at` - تاريخ الاعتماد
- `performance_rating` - تقييم الأداء
- `notes` - ملاحظات
- `created_by`, `updated_by`
- `created_at`, `updated_at`

### 5. الاقتراحات - Suggestions
**الجدول**: `employee_suggestions`
**الوصف**: إدارة اقتراحات الموظفين

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `title` - عنوان الاقتراح
- `description` - وصف الاقتراح
- `category` - فئة الاقتراح (process_improvement, cost_reduction, innovation, safety)
- `priority` - الأولوية (low, medium, high, urgent)
- `status` - الحالة (submitted, under_review, approved, rejected, implemented)
- `expected_benefit` - الفائدة المتوقعة
- `implementation_cost` - تكلفة التنفيذ
- `reviewed_by` - معرف المراجع (FK)
- `reviewed_at` - تاريخ المراجعة
- `review_notes` - ملاحظات المراجعة
- `implemented_by` - معرف المنفذ (FK)
- `implemented_at` - تاريخ التنفيذ
- `implementation_notes` - ملاحظات التنفيذ
- `reward_amount` - مبلغ المكافأة
- `created_by`, `updated_by`
- `created_at`, `updated_at`

### 6. الشكاوى - Complaints
**الجدول**: `employee_complaints`
**الوصف**: إدارة شكاوى الموظفين

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `complaint_against_id` - الشكوى ضد (FK - user_id)
- `title` - عنوان الشكوى
- `description` - وصف الشكوى
- `category` - فئة الشكوى (harassment, discrimination, safety, management, colleague)
- `severity` - درجة الخطورة (low, medium, high, critical)
- `status` - الحالة (submitted, investigating, resolved, closed, escalated)
- `is_anonymous` - شكوى مجهولة (boolean)
- `incident_date` - تاريخ الحادثة
- `location` - مكان الحادثة
- `witnesses` - الشهود (JSON)
- `assigned_to` - معرف المحقق (FK)
- `assigned_at` - تاريخ التكليف
- `investigation_notes` - ملاحظات التحقيق
- `resolution` - القرار
- `resolved_by` - معرف من حل الشكوى (FK)
- `resolved_at` - تاريخ الحل
- `follow_up_required` - يتطلب متابعة (boolean)
- `follow_up_date` - تاريخ المتابعة
- `created_by`, `updated_by`
- `created_at`, `updated_at`

### 7. الاستقالات - Resignations
**الجدول**: `employee_resignations`
**الوصف**: إدارة استقالات الموظفين

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `resignation_type` - نوع الاستقالة (voluntary, involuntary, retirement, termination)
- `reason` - سبب الاستقالة
- `notice_period` - فترة الإشعار (بالأيام)
- `last_working_date` - آخر يوم عمل
- `resignation_date` - تاريخ الاستقالة
- `approval_status` - حالة الموافقة
- `approved_by` - معرف المعتمد (FK)
- `approved_at` - تاريخ الاعتماد
- `exit_interview_completed` - مقابلة الخروج مكتملة (boolean)
- `exit_interview_date` - تاريخ مقابلة الخروج
- `exit_interview_notes` - ملاحظات مقابلة الخروج
- `handover_completed` - تسليم المهام مكتمل (boolean)
- `handover_to` - تسليم المهام إلى (FK)
- `final_settlement` - التسوية النهائية
- `rehire_eligible` - مؤهل للإعادة التوظيف (boolean)
- `notes` - ملاحظات
- `created_by`, `updated_by`
- `created_at`, `updated_at`

### 8. الإنذارات - Warnings
**الجدول**: `employee_warnings`
**الوصف**: إدارة الإنذارات التأديبية

**الحقول**:
- `id` - المعرف الفريد
- `user_id` - معرف الموظف (FK)
- `warning_type` - نوع الإنذار (verbal, written, final, suspension)
- `category` - فئة المخالفة (attendance, performance, conduct, policy_violation)
- `title` - عنوان الإنذار
- `description` - وصف المخالفة
- `severity` - درجة الخطورة (minor, moderate, major, severe)
- `incident_date` - تاريخ الحادثة
- `warning_date` - تاريخ الإنذار
- `issued_by` - معرف مصدر الإنذار (FK)
- `acknowledged_by_employee` - إقرار الموظف (boolean)
- `acknowledgment_date` - تاريخ الإقرار
- `employee_response` - رد الموظف
- `improvement_plan` - خطة التحسين
- `review_date` - تاريخ المراجعة
- `status` - الحالة (active, resolved, expired)
- `escalation_level` - مستوى التصعيد (1-5)
- `next_action` - الإجراء التالي
- `expiry_date` - تاريخ انتهاء الصلاحية
- `notes` - ملاحظات
- `created_by`, `updated_by`
- `created_at`, `updated_at`

## العلاقات - Relationships

### العلاقات الأساسية:
- جميع الجداول ترتبط بـ `users` table عبر `user_id`
- الموافقات ترتبط بـ `users` table عبر `approved_by`
- المنشئ والمحدث يرتبطان بـ `users` table

### العلاقات الخاصة:
- `employee_transfers`: ترتبط بـ `branches`, `departments`, `posts`
- `employee_promotions`: ترتبط بـ `posts` (from/to)
- `employee_complaints`: ترتبط بـ `users` (complaint_against_id)
- `employee_resignations`: ترتبط بـ `users` (handover_to)

## الفهارس - Indexes

### فهارس الأداء:
```sql
-- فهارس أساسية لجميع الجداول
INDEX idx_user_id (user_id)
INDEX idx_created_at (created_at)
INDEX idx_status (status/approval_status)

-- فهارس خاصة
INDEX idx_effective_date (effective_date)
INDEX idx_approved_by (approved_by)
INDEX idx_category (category)
```

## الصلاحيات - Permissions

### صلاحيات عامة:
- `view_employee_affairs` - عرض شؤون العاملين
- `manage_employee_affairs` - إدارة شؤون العاملين
- `approve_employee_affairs` - اعتماد شؤون العاملين

### صلاحيات خاصة لكل وحدة:
- `manage_bonuses`, `approve_bonuses`
- `manage_deductions`, `approve_deductions`
- `manage_transfers`, `approve_transfers`
- `manage_promotions`, `approve_promotions`
- `view_suggestions`, `manage_suggestions`
- `view_complaints`, `investigate_complaints`
- `manage_resignations`, `approve_resignations`
- `issue_warnings`, `manage_warnings`

## نظام التقارير - Reporting System

### تقارير شهرية:
- إحصائيات المكافآت والخصومات
- تقرير النقل والترقيات
- تحليل الاقتراحات والشكاوى
- معدلات الاستقالة والإنذارات

### تقارير سنوية:
- تقرير شامل لشؤون العاملين
- تحليل الاتجاهات والأنماط
- تقييم فعالية السياسات

### لوحة المعلومات:
- إحصائيات فورية لجميع الوحدات
- تنبيهات للمهام المعلقة
- مؤشرات الأداء الرئيسية
