# تحليل شامل لنظام إدارة الموظفين - Employee Management System Analysis

## الحالة الحالية - Current Status

### ✅ الموجود حالياً - Currently Available

#### 1. جدول الموظفين (users table):
- **الحقول الأساسية**: name, email, username, password, address, phone, avatar
- **الحقول التنظيمية**: company_id, branch_id, department_id, post_id, supervisor_id
- **الحقول الإضافية**: 
  - id_number (رقم الهوية)
  - emergency_contact_number (رقم الطوارئ)
  - full_name_en (الاسم بالإنجليزية)
  - bank_branch_code, bank_account_number (البيانات المصرفية)
  - fingerprint_code, be_connect_code (أكواد الأنظمة الخارجية)
  - marital_status (الحالة الاجتماعية)
  - insurance_type, medical_insurance_category, insurance_institution_id
  - insurance_job_title, insurance_start_date, insurance_salary
- **حقول التتبع**: created_by, updated_by, deleted_by, created_at, updated_at, deleted_at

#### 2. العلاقات المتاحة:
```php
// في نموذج User
public function company() // الشركة
public function branch() // الفرع
public function department() // الإدارة
public function post() // الوظيفة
public function role() // الدور
public function supervisor() // المشرف
public function accountDetail() // تفاصيل الحساب
public function dependents() // المعالين
```

#### 3. المتحكم (UserController):
- **العمليات المتاحة**:
  - `index()` - عرض قائمة الموظفين مع الفلترة
  - `create()` - نموذج إضافة موظف
  - `store()` - حفظ موظف جديد
  - `edit()` - نموذج تعديل موظف
  - `update()` - تحديث بيانات موظف
  - `delete()` - حذف موظف
  - `toggleStatus()` - تغيير حالة النشاط

#### 4. نظام رفع الملفات الحالي:
- **ImageService Trait**: لرفع الصور والملفات
- **مسار رفع الصور**: `User::AVATAR_UPLOAD_PATH`
- **أنواع الملفات المدعومة**: jpeg, png, jpg, svg (للصور)
- **نظام المرفقات العام**: Attachment model مع polymorphic relationships

#### 5. نظام التحقق من صحة البيانات:
- **UserCreateRequest**: للتحقق من بيانات إنشاء موظف
- **UserUpdateRequest**: للتحقق من بيانات تحديث موظف
- **UserAccountRequest**: للتحقق من بيانات الحساب

#### 6. الصلاحيات المتاحة:
- `list_employee` - عرض قائمة الموظفين
- `create_employee` - إضافة موظف جديد
- `edit_employee` - تعديل الموظف
- `delete_employee` - حذف الموظف

### ❌ المفقود - Missing Features

#### 1. نظام رفع مصوغات التعيين:
- لا يوجد نظام مخصص لرفع وثائق الموظفين
- لا يوجد جدول لحفظ مصوغات التعيين
- لا يوجد تصنيف للوثائق (CV, عقد عمل, شهادات، إلخ)

#### 2. APIs مخصصة لإدارة الموظفين:
- معظم العمليات تتم عبر الواجهة الويب فقط
- لا توجد APIs منفصلة لإدارة بيانات الموظفين

#### 3. نظام إدارة الملفات المتقدم:
- لا يوجد تصنيف للملفات
- لا يوجد نظام أذونات للملفات
- لا يوجد تتبع لتاريخ الملفات

## المطلوب تنفيذه - Required Implementation

### 1. إنشاء جدول مصوغات التعيين:
```sql
CREATE TABLE employee_documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    document_type ENUM('cv', 'contract', 'certificate', 'id_copy', 'photo', 'other'),
    document_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_by BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);
```

### 2. إنشاء نموذج EmployeeDocument:
```php
class EmployeeDocument extends Model
{
    protected $fillable = [
        'user_id', 'document_type', 'document_name', 
        'file_path', 'file_size', 'mime_type', 'uploaded_by'
    ];
    
    public function user() {
        return $this->belongsTo(User::class);
    }
    
    public function uploadedBy() {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
}
```

### 3. إنشاء EmployeeDocumentController:
- `index($userId)` - عرض مصوغات موظف
- `create($userId)` - نموذج رفع مصوغة
- `store(Request $request)` - حفظ مصوغة جديدة
- `download($id)` - تحميل مصوغة
- `delete($id)` - حذف مصوغة

### 4. تطوير APIs لإدارة الموظفين:
```php
// في routes/api.php
Route::group(['prefix' => 'employees'], function () {
    Route::get('/', [EmployeeApiController::class, 'index']);
    Route::post('/', [EmployeeApiController::class, 'store']);
    Route::get('/{id}', [EmployeeApiController::class, 'show']);
    Route::put('/{id}', [EmployeeApiController::class, 'update']);
    Route::delete('/{id}', [EmployeeApiController::class, 'destroy']);
    
    // APIs للمصوغات
    Route::get('/{id}/documents', [EmployeeApiController::class, 'getDocuments']);
    Route::post('/{id}/documents', [EmployeeApiController::class, 'uploadDocument']);
    Route::delete('/documents/{docId}', [EmployeeApiController::class, 'deleteDocument']);
});
```

### 5. تحسين نظام رفع الملفات:
- دعم أنواع ملفات متعددة (PDF, DOC, DOCX, JPG, PNG)
- تشفير أسماء الملفات
- فحص أمان الملفات
- ضغط الصور تلقائياً

## الخطة التنفيذية - Implementation Plan

### المرحلة 1: إنشاء البنية الأساسية (يوم واحد)
1. إنشاء migration لجدول employee_documents
2. إنشاء نموذج EmployeeDocument
3. إنشاء EmployeeDocumentController
4. إنشاء EmployeeDocumentService

### المرحلة 2: تطوير الواجهات (يومان)
1. إنشاء صفحات إدارة المصوغات
2. تطوير نموذج رفع الملفات
3. إنشاء صفحة عرض المصوغات
4. تطوير واجهة تحميل الملفات

### المرحلة 3: تطوير APIs (يومان)
1. إنشاء EmployeeApiController
2. تطوير APIs لإدارة بيانات الموظفين
3. تطوير APIs لإدارة المصوغات
4. إضافة التوثيق للـ APIs

### المرحلة 4: الاختبار والتحسين (يوم واحد)
1. اختبار جميع الوظائف
2. تحسين الأداء
3. إضافة التحقق من الأمان
4. كتابة الوثائق

## الملفات المطلوب إنشاؤها:

### 1. Database:
- `create_employee_documents_table.php` (Migration)

### 2. Models:
- `app/Models/EmployeeDocument.php`

### 3. Controllers:
- `app/Http/Controllers/Web/EmployeeDocumentController.php`
- `app/Http/Controllers/Api/EmployeeApiController.php`

### 4. Services:
- `app/Services/Employee/EmployeeDocumentService.php`

### 5. Requests:
- `app/Http/Requests/Employee/DocumentUploadRequest.php`

### 6. Views:
- `resources/views/admin/employee/documents/index.blade.php`
- `resources/views/admin/employee/documents/upload.blade.php`

### 7. Routes:
- إضافة مسارات في `routes/web.php`
- إضافة مسارات في `routes/api.php`
