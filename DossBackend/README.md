# Employee Management System - نظام إدارة الموظفين

نظام شامل لإدارة الموظفين في صيدلية دوس، مطور باستخدام Laravel للـ Backend و Flutter للتطبيق المحمول.

## المميزات الرئيسية

### 🏢 إدارة الهيكل التنظيمي
- إدارة الشركات والفروع
- إدارة الإدارات والأقسام
- إدارة الوظائف والمناصب
- نظام الصلاحيات المتقدم

### 👥 إدارة الموظفين
- ملفات شخصية شاملة للموظفين
- إدارة المستندات والملفات
- تتبع تاريخ التوظيف والترقيات
- إدارة المعالين والتأمينات

### ⏰ نظام الحضور والانصراف
- تسجيل الحضور والانصراف بالموقع الجغرافي
- تتبع ساعات العمل والعمل الإضافي
- حساب التأخير والانصراف المبكر
- تقارير الحضور الشهرية والسنوية

### 🏖️ إدارة الإجازات
- طلب الإجازات إلكترونياً
- تتبع رصيد الإجازات
- إدارة أنواع الإجازات المختلفة
- نظام الموافقات المتدرج

### 💰 نظام الرواتب والسلف
- إدارة الرواتب الأساسية والبدلات
- كشوف الرواتب الشهرية
- نظام السلف والأقساط
- تقارير الرواتب المفصلة

### 🎯 شؤون الموظفين
- إدارة المكافآت والخصومات
- طلبات النقل والترقيات
- نظام الاقتراحات والشكاوى
- الإنذارات والتحذيرات

### 🛡️ التأمينات
- إدارة التأمين الاجتماعي والطبي
- تسجيل المعالين
- تتبع مؤسسات التأمين
- تقارير التأمينات

## التقنيات المستخدمة

### Backend (Laravel)
- **Laravel 9+** - إطار العمل الرئيسي
- **MySQL** - قاعدة البيانات
- **Laravel Sanctum** - نظام المصادقة
- **Laravel Storage** - إدارة الملفات
- **Carbon** - معالجة التواريخ

### Frontend (Flutter)
- **Flutter 3+** - تطوير التطبيق المحمول
- **Dart** - لغة البرمجة
- **HTTP Package** - للتواصل مع APIs
- **Provider/Bloc** - إدارة الحالة
- **Shared Preferences** - التخزين المحلي

## متطلبات النظام

### Backend
- PHP 8.0+
- Composer
- MySQL 5.7+
- Apache/Nginx

### Mobile App
- Flutter SDK 3.0+
- Android Studio / VS Code
- Android SDK (للأندرويد)
- Xcode (للـ iOS)

## التثبيت والإعداد

### Backend Setup

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd DossBackend
```

2. **تثبيت المتطلبات**
```bash
composer install
```

3. **إعداد البيئة**
```bash
cp .env.example .env
php artisan key:generate
```

4. **إعداد قاعدة البيانات**
```bash
# تحديث ملف .env بمعلومات قاعدة البيانات
php artisan migrate
php artisan db:seed
```

5. **إنشاء رابط التخزين**
```bash
php artisan storage:link
```

6. **تشغيل الخادم**
```bash
php artisan serve
```

### Mobile App Setup

1. **الانتقال لمجلد التطبيق**
```bash
cd ../DossApp
```

2. **تثبيت المتطلبات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

## هيكل المشروع

### Backend Structure
```
DossBackend/
├── app/
│   ├── Http/Controllers/Api/     # API Controllers
│   ├── Models/                   # نماذج البيانات
│   ├── Repositories/            # مستودعات البيانات
│   └── Services/                # خدمات العمل
├── database/
│   ├── migrations/              # ملفات الهجرة
│   └── seeders/                 # بيانات البذر
├── routes/
│   ├── api.php                  # مسارات API
│   └── web.php                  # مسارات الويب
└── docs/                        # التوثيق
```

### Mobile App Structure
```
DossApp/
├── lib/
│   ├── models/                  # نماذج البيانات
│   ├── services/                # خدمات API
│   ├── screens/                 # شاشات التطبيق
│   ├── widgets/                 # مكونات مخصصة
│   └── utils/                   # أدوات مساعدة
├── assets/                      # الموارد (صور، أيقونات)
└── pubspec.yaml                 # إعدادات المشروع
```

## APIs المتاحة

### المصادقة
- `POST /api/login` - تسجيل الدخول

### الرواتب
- `GET /api/payroll/current-salary` - الراتب الحالي
- `GET /api/payroll/history` - تاريخ الرواتب
- `POST /api/payroll/request-advance` - طلب سلفة

### الحضور
- `GET /api/attendance/today` - حضور اليوم
- `POST /api/attendance/check-in` - تسجيل الدخول
- `POST /api/attendance/check-out` - تسجيل الخروج

### الإجازات
- `GET /api/leaves/balance` - رصيد الإجازات
- `POST /api/leaves/submit-request` - طلب إجازة

### الملف الشخصي
- `GET /api/profile` - الملف الشخصي
- `POST /api/profile/update` - تحديث الملف الشخصي

[للمزيد من التفاصيل، راجع ملف API Documentation](docs/api-documentation.md)

## الحالة

- ✅ Backend APIs - مكتمل
- 🔄 Mobile App - قيد التطوير
- ⏳ Web Dashboard - مخطط
- ⏳ Testing - مخطط

---

**تم تطوير هذا النظام بواسطة فريق تطوير صيدلية دوس**
