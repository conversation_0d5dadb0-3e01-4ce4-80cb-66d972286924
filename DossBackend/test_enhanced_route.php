<?php

// Quick test to check if enhanced dashboard route exists
// Run this with: php artisan route:list | grep enhanced

echo "Testing Enhanced Dashboard Route:\n";
echo "================================\n\n";

// Check if route file exists
$routeFile = 'routes/web.php';
if (file_exists($routeFile)) {
    $content = file_get_contents($routeFile);
    
    if (strpos($content, 'enhanced.dashboard') !== false) {
        echo "✅ Route 'enhanced.dashboard' found in routes/web.php\n";
    } else {
        echo "❌ Route 'enhanced.dashboard' NOT found in routes/web.php\n";
    }
    
    if (strpos($content, 'AdminDashboardController') !== false) {
        echo "✅ AdminDashboardController referenced in routes\n";
    } else {
        echo "❌ AdminDashboardController NOT referenced in routes\n";
    }
} else {
    echo "❌ routes/web.php file not found\n";
}

// Check if controller exists
$controllerFile = 'app/Http/Controllers/Admin/AdminDashboardController.php';
if (file_exists($controllerFile)) {
    echo "✅ AdminDashboardController file exists\n";
    
    $controllerContent = file_get_contents($controllerFile);
    if (strpos($controllerContent, 'public function index()') !== false) {
        echo "✅ index() method found in controller\n";
    } else {
        echo "❌ index() method NOT found in controller\n";
    }
} else {
    echo "❌ AdminDashboardController file not found\n";
}

// Check if view exists
$viewFile = 'resources/views/admin/dashboard/index.blade.php';
if (file_exists($viewFile)) {
    echo "✅ View file exists: admin.dashboard.index\n";
} else {
    echo "❌ View file NOT found: admin.dashboard.index\n";
}

echo "\nSuggested fixes:\n";
echo "================\n";
echo "1. Clear route cache: php artisan route:clear\n";
echo "2. Clear config cache: php artisan config:clear\n";
echo "3. Clear view cache: php artisan view:clear\n";
echo "4. Regenerate autoload: composer dump-autoload\n";
echo "5. Check route list: php artisan route:list | grep enhanced\n";
