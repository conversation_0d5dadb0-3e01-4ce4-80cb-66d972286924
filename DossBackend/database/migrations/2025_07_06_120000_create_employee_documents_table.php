<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('document_type', [
                'cv',
                'contract',
                'certificate',
                'id_copy',
                'photo',
                'medical_report',
                'qualification',
                'experience_letter',
                'other'
            ])->comment('نوع المصوغة');
            $table->string('document_name')->comment('اسم المصوغة');
            $table->string('original_name')->comment('الاسم الأصلي للملف');
            $table->string('file_path', 500)->comment('مسار الملف');
            $table->integer('file_size')->comment('حجم الملف بالبايت');
            $table->string('mime_type', 100)->comment('نوع الملف');
            $table->string('file_extension', 10)->comment('امتداد الملف');
            $table->text('description')->nullable()->comment('وصف المصوغة');
            $table->boolean('is_required')->default(false)->comment('هل المصوغة مطلوبة');
            $table->boolean('is_verified')->default(false)->comment('هل تم التحقق من المصوغة');
            $table->timestamp('verified_at')->nullable()->comment('تاريخ التحقق');
            $table->unsignedBigInteger('verified_by')->nullable()->comment('من قام بالتحقق');
            $table->text('verification_notes')->nullable()->comment('ملاحظات التحقق');
            $table->unsignedBigInteger('uploaded_by')->comment('من قام بالرفع');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'document_type']);
            $table->index(['document_type', 'is_verified']);
            $table->index('uploaded_by');
            $table->index('verified_by');
        });


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_documents');
    }
};
