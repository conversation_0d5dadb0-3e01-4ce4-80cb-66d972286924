<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_suggestions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('title')->comment('عنوان الاقتراح');
            $table->text('description')->comment('وصف الاقتراح');
            $table->enum('category', ['process_improvement', 'cost_reduction', 'innovation', 'safety', 'customer_service', 'technology', 'other'])
                  ->comment('فئة الاقتراح');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])
                  ->default('medium')->comment('الأولوية');
            $table->enum('status', ['submitted', 'under_review', 'approved', 'rejected', 'implemented', 'on_hold'])
                  ->default('submitted')->comment('الحالة');
            $table->text('expected_benefit')->nullable()->comment('الفائدة المتوقعة');
            $table->decimal('implementation_cost', 10, 2)->nullable()->comment('تكلفة التنفيذ');
            $table->unsignedBigInteger('reviewed_by')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->text('review_notes')->nullable()->comment('ملاحظات المراجعة');
            $table->unsignedBigInteger('implemented_by')->nullable();
            $table->timestamp('implemented_at')->nullable();
            $table->text('implementation_notes')->nullable()->comment('ملاحظات التنفيذ');
            $table->decimal('reward_amount', 10, 2)->nullable()->comment('مبلغ المكافأة');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('category');
            $table->index('priority');
            $table->index('status');
            $table->index('reviewed_by');
            $table->index('implemented_by');
            $table->index('created_at');
            $table->index(['status', 'priority']);
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_suggestions');
    }
};
