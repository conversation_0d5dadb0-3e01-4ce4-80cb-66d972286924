<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_deductions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('deduction_type', ['disciplinary', 'loan', 'advance', 'insurance', 'tax', 'absence', 'late', 'other'])
                  ->comment('نوع الخصم');
            $table->decimal('amount', 10, 2)->comment('مبلغ الخصم');
            $table->string('description')->comment('وصف الخصم');
            $table->text('reason')->comment('سبب الخصم');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])
                  ->default('pending')->comment('حالة الموافقة');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->date('effective_date')->comment('تاريخ السريان');
            $table->integer('installments')->default(1)->comment('عدد الأقساط');
            $table->decimal('installment_amount', 10, 2)->nullable()->comment('مبلغ القسط');
            $table->decimal('remaining_amount', 10, 2)->default(0)->comment('المبلغ المتبقي');
            $table->boolean('is_recurring')->default(false)->comment('خصم متكرر');
            $table->enum('recurrence_period', ['monthly', 'quarterly', 'yearly'])->nullable()
                  ->comment('فترة التكرار');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('deduction_type');
            $table->index('approval_status');
            $table->index('effective_date');
            $table->index('is_recurring');
            $table->index('created_at');
            $table->index(['user_id', 'effective_date']);
            $table->index(['approval_status', 'effective_date']);
            $table->index(['is_recurring', 'recurrence_period']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_deductions');
    }
};
