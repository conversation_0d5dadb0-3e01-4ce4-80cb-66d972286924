<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_bonuses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('bonus_type', ['performance', 'annual', 'special', 'achievement', 'holiday', 'overtime'])
                  ->comment('نوع المكافأة');
            $table->enum('bonus_category', ['financial', 'in_kind', 'promotion', 'recognition'])
                  ->comment('فئة المكافأة');
            $table->decimal('amount', 10, 2)->nullable()->comment('المبلغ');
            $table->string('description')->comment('وصف المكافأة');
            $table->text('reason')->comment('سبب المنح');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])
                  ->default('pending')->comment('حالة الموافقة');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->date('effective_date')->comment('تاريخ السريان');
            $table->date('payment_date')->nullable()->comment('تاريخ الصرف');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('bonus_type');
            $table->index('approval_status');
            $table->index('effective_date');
            $table->index('created_at');
            $table->index(['user_id', 'effective_date']);
            $table->index(['approval_status', 'effective_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_bonuses');
    }
};
