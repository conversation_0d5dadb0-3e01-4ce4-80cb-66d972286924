<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_warnings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('warning_type', ['verbal', 'written', 'final', 'suspension'])
                  ->comment('نوع الإنذار');
            $table->enum('category', ['attendance', 'performance', 'conduct', 'policy_violation', 'safety', 'insubordination', 'other'])
                  ->comment('فئة المخالفة');
            $table->string('title')->comment('عنوان الإنذار');
            $table->text('description')->comment('وصف المخالفة');
            $table->enum('severity', ['minor', 'moderate', 'major', 'severe'])
                  ->default('moderate')->comment('درجة الخطورة');
            $table->date('incident_date')->comment('تاريخ الحادثة');
            $table->date('warning_date')->comment('تاريخ الإنذار');
            $table->unsignedBigInteger('issued_by')->comment('معرف مصدر الإنذار');
            $table->boolean('acknowledged_by_employee')->default(false)->comment('إقرار الموظف');
            $table->date('acknowledgment_date')->nullable()->comment('تاريخ الإقرار');
            $table->text('employee_response')->nullable()->comment('رد الموظف');
            $table->text('improvement_plan')->nullable()->comment('خطة التحسين');
            $table->date('review_date')->nullable()->comment('تاريخ المراجعة');
            $table->enum('status', ['active', 'resolved', 'expired'])
                  ->default('active')->comment('الحالة');
            $table->integer('escalation_level')->default(1)->comment('مستوى التصعيد 1-5');
            $table->text('next_action')->nullable()->comment('الإجراء التالي');
            $table->date('expiry_date')->nullable()->comment('تاريخ انتهاء الصلاحية');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('warning_type');
            $table->index('category');
            $table->index('severity');
            $table->index('status');
            $table->index('issued_by');
            $table->index('incident_date');
            $table->index('warning_date');
            $table->index('escalation_level');
            $table->index('expiry_date');
            $table->index('acknowledged_by_employee');
            $table->index('created_at');
            $table->index(['user_id', 'status']);
            $table->index(['status', 'escalation_level']);
            $table->index(['warning_type', 'severity']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_warnings');
    }
};
