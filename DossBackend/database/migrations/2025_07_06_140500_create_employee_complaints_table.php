<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_complaints', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('complaint_against_id')->nullable()->comment('الشكوى ضد');
            $table->string('title')->comment('عنوان الشكوى');
            $table->text('description')->comment('وصف الشكوى');
            $table->enum('category', ['harassment', 'discrimination', 'safety', 'management', 'colleague', 'workplace_environment', 'policy_violation', 'other'])
                  ->comment('فئة الشكوى');
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])
                  ->default('medium')->comment('درجة الخطورة');
            $table->enum('status', ['submitted', 'investigating', 'resolved', 'closed', 'escalated', 'on_hold'])
                  ->default('submitted')->comment('الحالة');
            $table->boolean('is_anonymous')->default(false)->comment('شكوى مجهولة');
            $table->date('incident_date')->nullable()->comment('تاريخ الحادثة');
            $table->string('location')->nullable()->comment('مكان الحادثة');
            $table->json('witnesses')->nullable()->comment('الشهود');
            $table->unsignedBigInteger('assigned_to')->nullable()->comment('معرف المحقق');
            $table->timestamp('assigned_at')->nullable();
            $table->text('investigation_notes')->nullable()->comment('ملاحظات التحقيق');
            $table->text('resolution')->nullable()->comment('القرار');
            $table->unsignedBigInteger('resolved_by')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->boolean('follow_up_required')->default(false)->comment('يتطلب متابعة');
            $table->date('follow_up_date')->nullable()->comment('تاريخ المتابعة');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('complaint_against_id');
            $table->index('category');
            $table->index('severity');
            $table->index('status');
            $table->index('assigned_to');
            $table->index('resolved_by');
            $table->index('incident_date');
            $table->index('is_anonymous');
            $table->index('created_at');
            $table->index(['status', 'severity']);
            $table->index(['assigned_to', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_complaints');
    }
};
