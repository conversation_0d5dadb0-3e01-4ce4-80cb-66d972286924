<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_transfers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('transfer_type', ['branch', 'department', 'position', 'temporary', 'permanent'])
                  ->comment('نوع النقل');
            $table->unsignedBigInteger('from_branch_id')->nullable();
            $table->unsignedBigInteger('to_branch_id')->nullable();
            $table->unsignedBigInteger('from_department_id')->nullable();
            $table->unsignedBigInteger('to_department_id')->nullable();
            $table->unsignedBigInteger('from_post_id')->nullable();
            $table->unsignedBigInteger('to_post_id')->nullable();
            $table->text('reason')->comment('سبب النقل');
            $table->date('request_date')->comment('تاريخ الطلب');
            $table->date('effective_date')->comment('تاريخ السريان');
            $table->date('end_date')->nullable()->comment('تاريخ الانتهاء للنقل المؤقت');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])
                  ->default('pending')->comment('حالة الموافقة');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('transfer_type');
            $table->index('approval_status');
            $table->index('effective_date');
            $table->index('from_branch_id');
            $table->index('to_branch_id');
            $table->index('from_department_id');
            $table->index('to_department_id');
            $table->index('created_at');
            $table->index(['user_id', 'effective_date']);
            $table->index(['approval_status', 'effective_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_transfers');
    }
};
