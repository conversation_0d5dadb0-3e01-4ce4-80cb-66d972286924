<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // جدول الرواتب الأساسية
        Schema::create('employee_salaries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->decimal('basic_salary', 10, 2)->comment('الراتب الأساسي');
            $table->decimal('housing_allowance', 10, 2)->default(0)->comment('بدل سكن');
            $table->decimal('transport_allowance', 10, 2)->default(0)->comment('بدل مواصلات');
            $table->decimal('food_allowance', 10, 2)->default(0)->comment('بدل طعام');
            $table->decimal('other_allowances', 10, 2)->default(0)->comment('بدلات أخرى');
            $table->decimal('total_salary', 10, 2)->comment('إجمالي الراتب');
            $table->date('effective_date')->comment('تاريخ السريان');
            $table->date('end_date')->nullable()->comment('تاريخ الانتهاء');
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->index('user_id');
            $table->index('effective_date');
            $table->index('is_active');
        });

        // جدول كشوف الرواتب الشهرية
        Schema::create('monthly_payrolls', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('branch_id');
            $table->year('payroll_year');
            $table->tinyInteger('payroll_month');
            $table->decimal('basic_salary', 10, 2);
            $table->decimal('allowances', 10, 2)->default(0);
            $table->decimal('overtime_amount', 10, 2)->default(0);
            $table->decimal('bonus_amount', 10, 2)->default(0);
            $table->decimal('deduction_amount', 10, 2)->default(0);
            $table->decimal('advance_amount', 10, 2)->default(0);
            $table->decimal('insurance_deduction', 10, 2)->default(0);
            $table->decimal('tax_deduction', 10, 2)->default(0);
            $table->decimal('gross_salary', 10, 2);
            $table->decimal('net_salary', 10, 2);
            $table->enum('status', ['draft', 'approved', 'paid'])->default('draft');
            $table->date('payment_date')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'payroll_year', 'payroll_month']);
            $table->index(['payroll_year', 'payroll_month']);
            $table->index('status');
        });

        // جدول السلف
        Schema::create('employee_advances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->decimal('amount', 10, 2);
            $table->text('reason');
            $table->enum('status', ['pending', 'approved', 'rejected', 'paid', 'completed'])->default('pending');
            $table->date('request_date');
            $table->date('approved_date')->nullable();
            $table->date('payment_date')->nullable();
            $table->integer('installments')->default(1)->comment('عدد الأقساط');
            $table->decimal('installment_amount', 10, 2)->comment('قيمة القسط');
            $table->decimal('paid_amount', 10, 2)->default(0);
            $table->decimal('remaining_amount', 10, 2);
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->text('approval_notes')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->index('user_id');
            $table->index('status');
            $table->index('request_date');
        });

        // جدول أقساط السلف
        Schema::create('advance_installments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('advance_id');
            $table->integer('installment_number');
            $table->decimal('amount', 10, 2);
            $table->date('due_date');
            $table->date('paid_date')->nullable();
            $table->enum('status', ['pending', 'paid'])->default('pending');
            $table->unsignedBigInteger('payroll_id')->nullable()->comment('كشف الراتب المخصوم منه');
            $table->timestamps();

            $table->foreign('advance_id')->references('id')->on('employee_advances')->onDelete('cascade');
            $table->index(['advance_id', 'installment_number']);
            $table->index('due_date');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('advance_installments');
        Schema::dropIfExists('employee_advances');
        Schema::dropIfExists('monthly_payrolls');
        Schema::dropIfExists('employee_salaries');
    }
};
