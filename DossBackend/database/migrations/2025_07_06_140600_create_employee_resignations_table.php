<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_resignations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('resignation_type', ['voluntary', 'involuntary', 'retirement', 'termination', 'mutual_agreement'])
                  ->comment('نوع الاستقالة');
            $table->text('reason')->comment('سبب الاستقالة');
            $table->integer('notice_period')->default(30)->comment('فترة الإشعار بالأيام');
            $table->date('last_working_date')->comment('آخر يوم عمل');
            $table->date('resignation_date')->comment('تاريخ الاستقالة');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])
                  ->default('pending')->comment('حالة الموافقة');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->boolean('exit_interview_completed')->default(false)->comment('مقابلة الخروج مكتملة');
            $table->date('exit_interview_date')->nullable()->comment('تاريخ مقابلة الخروج');
            $table->text('exit_interview_notes')->nullable()->comment('ملاحظات مقابلة الخروج');
            $table->boolean('handover_completed')->default(false)->comment('تسليم المهام مكتمل');
            $table->unsignedBigInteger('handover_to')->nullable()->comment('تسليم المهام إلى');
            $table->decimal('final_settlement', 10, 2)->nullable()->comment('التسوية النهائية');
            $table->boolean('rehire_eligible')->default(true)->comment('مؤهل للإعادة التوظيف');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('resignation_type');
            $table->index('approval_status');
            $table->index('last_working_date');
            $table->index('resignation_date');
            $table->index('approved_by');
            $table->index('handover_to');
            $table->index('exit_interview_completed');
            $table->index('handover_completed');
            $table->index('created_at');
            $table->index(['approval_status', 'resignation_date']);
            $table->index(['user_id', 'resignation_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_resignations');
    }
};
