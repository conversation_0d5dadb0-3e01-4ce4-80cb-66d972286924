<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('id_number')->nullable();
            $table->enum('insurance_type', ['social', 'medical'])->nullable();
            $table->enum('medical_insurance_category', ['public', 'private'])->nullable();
        
            $table->string('fingerprint_code')->nullable();
            $table->string('be_connect_code')->nullable();
            $table->string('be_connect_client_code')->nullable();
        
            $table->string('full_name_en')->nullable();
            $table->string('bank_branch_code')->nullable();
            $table->string('bank_account_number')->nullable();
        
            $table->enum('marital_status', ['single', 'married'])->nullable();
            $table->string('emergency_contact_number')->nullable();
        
            // علاقة تأمينية
            $table->unsignedBigInteger('insurance_institution_id')->nullable();
            $table->string('insurance_job_title')->nullable();
            $table->date('insurance_start_date')->nullable();
            $table->double('insurance_salary')->nullable();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            //
        });
    }
};
