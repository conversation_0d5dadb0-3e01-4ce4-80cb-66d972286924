<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_promotions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('promotion_type', ['position', 'salary', 'grade', 'combined'])
                  ->comment('نوع الترقية');
            $table->unsignedBigInteger('from_post_id')->nullable();
            $table->unsignedBigInteger('to_post_id')->nullable();
            $table->decimal('old_salary', 10, 2)->nullable()->comment('الراتب السابق');
            $table->decimal('new_salary', 10, 2)->nullable()->comment('الراتب الجديد');
            $table->decimal('salary_increase', 10, 2)->nullable()->comment('مقدار الزيادة');
            $table->string('old_grade')->nullable()->comment('الدرجة السابقة');
            $table->string('new_grade')->nullable()->comment('الدرجة الجديدة');
            $table->text('reason')->comment('سبب الترقية');
            $table->date('effective_date')->comment('تاريخ السريان');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])
                  ->default('pending')->comment('حالة الموافقة');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->enum('performance_rating', ['excellent', 'very_good', 'good', 'satisfactory', 'needs_improvement'])
                  ->nullable()->comment('تقييم الأداء');
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id');
            $table->index('promotion_type');
            $table->index('approval_status');
            $table->index('effective_date');
            $table->index('from_post_id');
            $table->index('to_post_id');
            $table->index('performance_rating');
            $table->index('created_at');
            $table->index(['user_id', 'effective_date']);
            $table->index(['approval_status', 'effective_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_promotions');
    }
};
