<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Attendance;
use App\Models\LeaveRequest;
use App\Models\Payroll;
use App\Models\EmployeeBonus;
use App\Models\EmployeeDeduction;
use App\Models\EmployeeSuggestion;
use App\Models\EmployeeComplaint;
use App\Models\Department;
use App\Models\Branch;
use App\Models\Company;
use App\Models\LeaveType;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class AdminDashboardSeeder extends Seeder
{
    public function run()
    {
        // Create sample company
        $company = Company::firstOrCreate([
            'name' => 'صيدلية دوس',
            'email' => '<EMAIL>',
            'phone' => '+966123456789',
            'address' => 'الرياض، المملكة العربية السعودية',
            'is_active' => 1,
        ]);

        // Create sample branches
        $branches = [
            ['name' => 'الفرع الرئيسي', 'address' => 'الرياض - حي النخيل'],
            ['name' => 'فرع الملز', 'address' => 'الرياض - حي الملز'],
            ['name' => 'فرع العليا', 'address' => 'الرياض - حي العليا'],
        ];

        foreach ($branches as $branchData) {
            Branch::firstOrCreate([
                'name' => $branchData['name'],
                'address' => $branchData['address'],
                'company_id' => $company->id,
                'is_active' => 1,
            ]);
        }

        // Create sample departments
        $departments = [
            'الصيدلة',
            'المبيعات',
            'المحاسبة',
            'الموارد البشرية',
            'التسويق',
            'خدمة العملاء',
        ];

        foreach ($departments as $deptName) {
            Department::firstOrCreate([
                'dept_name' => $deptName,
                'company_id' => $company->id,
                'is_active' => 1,
            ]);
        }

        // Create leave types
        $leaveTypes = [
            ['name' => 'إجازة سنوية', 'days' => 30],
            ['name' => 'إجازة مرضية', 'days' => 15],
            ['name' => 'إجازة طارئة', 'days' => 5],
            ['name' => 'إجازة أمومة', 'days' => 70],
            ['name' => 'إجازة حج', 'days' => 15],
        ];

        foreach ($leaveTypes as $typeData) {
            LeaveType::firstOrCreate([
                'name' => $typeData['name'],
                'days' => $typeData['days'],
                'company_id' => $company->id,
                'is_active' => 1,
            ]);
        }

        // Create sample employees
        $departments = Department::all();
        $branches = Branch::all();
        
        for ($i = 1; $i <= 50; $i++) {
            $user = User::firstOrCreate([
                'email' => "employee{$i}@dosspharmacy.com",
            ], [
                'name' => "موظف رقم {$i}",
                'password' => Hash::make('password'),
                'user_type' => 'employee',
                'company_id' => $company->id,
                'department_id' => $departments->random()->id,
                'branch_id' => $branches->random()->id,
                'is_active' => 1,
                'phone' => '+96650' . str_pad($i, 7, '0', STR_PAD_LEFT),
                'address' => 'الرياض، المملكة العربية السعودية',
                'join_date' => Carbon::now()->subDays(rand(30, 365)),
            ]);

            // Create attendance records for the last 30 days
            for ($day = 0; $day < 30; $day++) {
                $date = Carbon::today()->subDays($day);
                
                // Skip weekends (Friday and Saturday)
                if ($date->isFriday() || $date->isSaturday()) {
                    continue;
                }

                // 90% attendance rate
                if (rand(1, 100) <= 90) {
                    $checkIn = $date->copy()->setTime(8, rand(0, 30), 0);
                    $checkOut = $checkIn->copy()->addHours(8)->addMinutes(rand(0, 60));
                    
                    Attendance::firstOrCreate([
                        'user_id' => $user->id,
                        'attendance_date' => $date,
                    ], [
                        'check_in_at' => $checkIn,
                        'check_out_at' => $checkOut,
                        'working_from' => 'office',
                        'is_late' => $checkIn->hour > 8 || ($checkIn->hour == 8 && $checkIn->minute > 15),
                        'is_early_departure' => $checkOut->hour < 17,
                        'total_time' => $checkOut->diffInMinutes($checkIn),
                        'company_id' => $company->id,
                    ]);
                }
            }

            // Create some leave requests
            if (rand(1, 100) <= 30) { // 30% of employees have leave requests
                $leaveType = LeaveType::inRandomOrder()->first();
                $leaveFrom = Carbon::now()->addDays(rand(1, 30));
                $leaveTo = $leaveFrom->copy()->addDays(rand(1, 5));
                
                LeaveRequest::create([
                    'user_id' => $user->id,
                    'leave_type_id' => $leaveType->id,
                    'leave_from' => $leaveFrom,
                    'leave_to' => $leaveTo,
                    'no_of_days' => $leaveTo->diffInDays($leaveFrom) + 1,
                    'reason' => 'سبب شخصي',
                    'status' => collect(['pending', 'approved', 'rejected'])->random(),
                    'company_id' => $company->id,
                ]);
            }

            // Create payroll records
            for ($month = 1; $month <= 3; $month++) {
                $basicSalary = rand(5000, 15000);
                $allowances = rand(500, 2000);
                $deductions = rand(100, 500);
                
                Payroll::firstOrCreate([
                    'user_id' => $user->id,
                    'month' => Carbon::now()->subMonths($month)->month,
                    'year' => Carbon::now()->subMonths($month)->year,
                ], [
                    'basic_salary' => $basicSalary,
                    'allowances' => $allowances,
                    'overtime_amount' => rand(0, 1000),
                    'bonus_amount' => rand(0, 500),
                    'deduction_amount' => $deductions,
                    'advance_amount' => rand(0, 1000),
                    'insurance_deduction' => $basicSalary * 0.09, // 9% insurance
                    'tax_deduction' => 0, // No tax for now
                    'gross_salary' => $basicSalary + $allowances,
                    'net_salary' => $basicSalary + $allowances - $deductions - ($basicSalary * 0.09),
                    'status' => 'paid',
                    'company_id' => $company->id,
                ]);
            }

            // Create some suggestions and complaints
            if (rand(1, 100) <= 20) { // 20% have suggestions
                EmployeeSuggestion::create([
                    'user_id' => $user->id,
                    'title' => 'اقتراح لتحسين العمل',
                    'description' => 'اقتراح لتحسين بيئة العمل وزيادة الإنتاجية',
                    'status' => collect(['pending', 'approved', 'rejected'])->random(),
                    'company_id' => $company->id,
                ]);
            }

            if (rand(1, 100) <= 10) { // 10% have complaints
                EmployeeComplaint::create([
                    'user_id' => $user->id,
                    'title' => 'شكوى بخصوص العمل',
                    'description' => 'شكوى بخصوص ظروف العمل أو مشكلة معينة',
                    'status' => collect(['pending', 'in_progress', 'resolved'])->random(),
                    'company_id' => $company->id,
                ]);
            }
        }

        $this->command->info('تم إنشاء البيانات التجريبية للوحة التحكم بنجاح!');
    }
}
