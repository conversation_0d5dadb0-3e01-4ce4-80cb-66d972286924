@extends('layouts.admin')

@section('title', 'لوحة التحكم المحسنة')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line text-primary mr-2"></i>
            لوحة التحكم المحسنة
        </h1>
        <div class="d-none d-lg-inline-block">
            <span class="text-muted">آخر تحديث: {{ now()->format('Y-m-d H:i') }}</span>
        </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row">
        <!-- Real-time Stats -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الموظفين الحاضرين اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="employees-present">42</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Rate -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                معدل الحضور
                            </div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">87.5%</div>
                                </div>
                                <div class="col">
                                    <div class="progress progress-sm mr-2">
                                        <div class="progress-bar bg-info" role="progressbar"
                                             style="width: 87.5%" aria-valuenow="87.5" aria-valuemin="0"
                                             aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Requests -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                طلبات معلقة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">8</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Payroll -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                رواتب الشهر
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">450,000 ريال</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Attendance Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">إحصائيات الحضور الأسبوعية</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">خيارات:</div>
                            <a class="dropdown-item" href="#">تصدير البيانات</a>
                            <a class="dropdown-item" href="#">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="attendanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Department Distribution -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع الموظفين حسب الأقسام</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="departmentChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> الصيدلة
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> المبيعات
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-info"></i> المحاسبة
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-warning"></i> الموارد البشرية
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities and Quick Actions -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الأنشطة الأخيرة</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-user-plus text-success mr-2"></i>
                                تم إضافة موظف جديد: أحمد محمد
                            </div>
                            <small class="text-muted">منذ 5 دقائق</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-calendar-check text-info mr-2"></i>
                                تم الموافقة على إجازة: سارة أحمد
                            </div>
                            <small class="text-muted">منذ 15 دقيقة</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-clock text-warning mr-2"></i>
                                تأخر في الحضور: محمد علي
                            </div>
                            <small class="text-muted">منذ 30 دقيقة</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-money-bill-wave text-primary mr-2"></i>
                                تم إنشاء كشف راتب: قسم الصيدلة
                            </div>
                            <small class="text-muted">منذ ساعة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <a href="{{ route('admin.employees.index') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-users mr-2"></i>
                                إدارة الموظفين
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="{{ route('admin.attendance.index') }}" class="btn btn-success btn-block">
                                <i class="fas fa-clock mr-2"></i>
                                الحضور والانصراف
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="{{ route('admin.leaves.index') }}" class="btn btn-info btn-block">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                طلبات الإجازات
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="{{ route('admin.payroll.index') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-money-bill-wave mr-2"></i>
                                إدارة الرواتب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">مؤشرات الأداء الرئيسية</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="border-right">
                                <div class="h4 font-weight-bold text-success">92%</div>
                                <div class="text-xs text-uppercase text-muted">معدل الحضور الشهري</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border-right">
                                <div class="h4 font-weight-bold text-info">8.2</div>
                                <div class="text-xs text-uppercase text-muted">متوسط ساعات العمل</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border-right">
                                <div class="h4 font-weight-bold text-warning">15</div>
                                <div class="text-xs text-uppercase text-muted">متوسط التأخير (دقيقة)</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="h4 font-weight-bold text-primary">3</div>
                            <div class="text-xs text-uppercase text-muted">أيام الغياب هذا الشهر</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Attendance Chart
const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
const attendanceChart = new Chart(attendanceCtx, {
    type: 'line',
    data: {
        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
        datasets: [{
            label: 'الحضور',
            data: [42, 45, 48, 44, 46, 43, 0],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 50
            }
        }
    }
});

// Department Chart
const departmentCtx = document.getElementById('departmentChart').getContext('2d');
const departmentChart = new Chart(departmentCtx, {
    type: 'doughnut',
    data: {
        labels: ['الصيدلة', 'المبيعات', 'المحاسبة', 'الموارد البشرية'],
        datasets: [{
            data: [20, 15, 8, 5],
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        cutout: '80%'
    }
});

// Auto-refresh stats every 30 seconds
setInterval(function() {
    // Here you would typically make an AJAX call to get updated stats
    console.log('Refreshing stats...');
}, 30000);
</script>
@endpush
