@extends('layouts.master')

@section('title', 'كشوف الرواتب')

@section('nav-head', 'كشوف الرواتب')

@section('main-content')
<section class="content">
    <div class="container-fluid">
        <!-- Stats Row -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>{{ number_format(\App\Models\MonthlyPayroll::sum('net_salary')) }} ج.م</h3>
                        <p>إجمالي الرواتب</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>{{ \App\Models\MonthlyPayroll::where('status', 'paid')->count() }}</h3>
                        <p>الرواتب المدفوعة</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>{{ \App\Models\MonthlyPayroll::where('status', 'pending')->count() }}</h3>
                        <p>الرواتب المعلقة</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-secondary">
                    <div class="inner">
                        <h3>{{ number_format(\App\Models\MonthlyPayroll::avg('net_salary')) }} ج.م</h3>
                        <p>متوسط الراتب</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payroll Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">كشوف الرواتب</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم الموظف</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>البدلات</th>
                                        <th>الخصومات</th>
                                        <th>صافي الراتب</th>
                                        <th>الشهر</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse(\App\Models\MonthlyPayroll::with('user', 'user.department')->latest()->take(20)->get() as $payroll)
                                        <tr>
                                            <td>{{ $payroll->user->name ?? 'غير محدد' }}</td>
                                            <td>{{ $payroll->user->department->dept_name ?? 'غير محدد' }}</td>
                                            <td>{{ number_format($payroll->basic_salary) }} ج.م</td>
                                            <td>{{ number_format($payroll->allowances) }} ج.م</td>
                                            <td>{{ number_format($payroll->deductions) }} ج.م</td>
                                            <td>{{ number_format($payroll->net_salary) }} ج.م</td>
                                            <td>{{ $payroll->payroll_month }}/{{ $payroll->payroll_year }}</td>
                                            <td>
                                                @if($payroll->status == 'paid')
                                                    <span class="badge badge-success">مدفوع</span>
                                                @elseif($payroll->status == 'pending')
                                                    <span class="badge badge-warning">معلق</span>
                                                @else
                                                    <span class="badge badge-secondary">{{ $payroll->status }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">لا توجد بيانات رواتب</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
