@extends('layouts.admin')

@section('title', 'إدارة الرواتب')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الرواتب</h1>
        <div>
            <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-success shadow-sm mr-2">
                <i class="fas fa-download fa-sm text-white-50"></i> تصدير كشوف الرواتب
            </a>
            <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                <i class="fas fa-plus fa-sm text-white-50"></i> إنشاء كشف راتب
            </a>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Payroll Stats Cards -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الرواتب الشهرية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">450,000 ريال</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                كشوف مدفوعة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">48</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                كشوف معلقة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">2</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                متوسط الراتب
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">9,375 ريال</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Row -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">فلاتر البحث</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="month">الشهر:</label>
                            <select class="form-control" id="month">
                                <option value="">اختر الشهر</option>
                                <option value="1">يناير</option>
                                <option value="2">فبراير</option>
                                <option value="3">مارس</option>
                                <option value="4">أبريل</option>
                                <option value="5">مايو</option>
                                <option value="6">يونيو</option>
                                <option value="7">يوليو</option>
                                <option value="8">أغسطس</option>
                                <option value="9">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12" selected>ديسمبر</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="year">السنة:</label>
                            <select class="form-control" id="year">
                                <option value="2024" selected>2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="department">القسم:</label>
                            <select class="form-control" id="department">
                                <option value="">جميع الأقسام</option>
                                <option value="pharmacy">الصيدلة</option>
                                <option value="sales">المبيعات</option>
                                <option value="accounting">المحاسبة</option>
                                <option value="hr">الموارد البشرية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status">الحالة:</label>
                            <select class="form-control" id="status">
                                <option value="">جميع الحالات</option>
                                <option value="paid">مدفوع</option>
                                <option value="pending">معلق</option>
                                <option value="processing">قيد المعالجة</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <button class="btn btn-primary">بحث</button>
                            <button class="btn btn-secondary">إعادة تعيين</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payroll Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">كشوف الرواتب - ديسمبر 2024</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                    aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">إجراءات:</div>
                    <a class="dropdown-item" href="#">تصدير Excel</a>
                    <a class="dropdown-item" href="#">تصدير PDF</a>
                    <a class="dropdown-item" href="#">إرسال كشوف الرواتب</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الاستقطاعات</th>
                            <th>صافي الراتب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for($i = 1; $i <= 15; $i++)
                        <tr>
                            <td>{{ $i }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle mr-2" src="https://via.placeholder.com/35x35" width="35" height="35">
                                    <div>
                                        <div class="font-weight-bold">موظف رقم {{ $i }}</div>
                                        <div class="text-muted small">EMP{{ str_pad($i, 3, '0', STR_PAD_LEFT) }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                @php
                                    $departments = ['الصيدلة', 'المبيعات', 'المحاسبة', 'الموارد البشرية'];
                                    $dept = $departments[($i - 1) % count($departments)];
                                @endphp
                                <span class="badge badge-primary">{{ $dept }}</span>
                            </td>
                            <td>
                                @php
                                    $basicSalary = rand(5000, 15000);
                                @endphp
                                {{ number_format($basicSalary) }} ريال
                            </td>
                            <td>
                                @php
                                    $allowances = rand(500, 2000);
                                @endphp
                                {{ number_format($allowances) }} ريال
                            </td>
                            <td>
                                @php
                                    $deductions = rand(100, 1000);
                                @endphp
                                {{ number_format($deductions) }} ريال
                            </td>
                            <td>
                                @php
                                    $netSalary = $basicSalary + $allowances - $deductions;
                                @endphp
                                <strong>{{ number_format($netSalary) }} ريال</strong>
                            </td>
                            <td>
                                @if($i <= 48)
                                    <span class="badge badge-success">مدفوع</span>
                                @else
                                    <span class="badge badge-warning">معلق</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" title="تحرير">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endfor
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Quick Actions and Summary -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus text-primary mr-2"></i>
                            إنشاء كشف راتب جديد
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-calculator text-success mr-2"></i>
                            حساب الرواتب التلقائي
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-export text-info mr-2"></i>
                            تصدير تقرير الرواتب
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-envelope text-warning mr-2"></i>
                            إرسال كشوف الرواتب
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">ملخص الرواتب</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-right">
                                <div class="h5 font-weight-bold text-success">450,000</div>
                                <div class="text-xs text-uppercase text-muted">إجمالي الرواتب (ريال)</div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h5 font-weight-bold text-warning">45,000</div>
                            <div class="text-xs text-uppercase text-muted">إجمالي الاستقطاعات (ريال)</div>
                        </div>
                        <div class="col-6">
                            <div class="border-right">
                                <div class="h5 font-weight-bold text-info">25,000</div>
                                <div class="text-xs text-uppercase text-muted">إجمالي البدلات (ريال)</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 font-weight-bold text-primary">15,000</div>
                            <div class="text-xs text-uppercase text-muted">إجمالي العمل الإضافي (ريال)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Add any JavaScript for payroll management here
    console.log('Payroll management page loaded');
</script>
@endpush
