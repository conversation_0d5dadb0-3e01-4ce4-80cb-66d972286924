@extends('layouts.admin')

@section('title', 'حساب الرواتب')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">حساب الرواتب</h1>
        <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-success shadow-sm" id="calculateAllBtn">
            <i class="fas fa-calculator fa-sm text-white-50"></i> حساب جميع الرواتب
        </a>
    </div>

    <!-- Calculation Settings -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">إعدادات الحساب</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="calculationMonth">الشهر</label>
                        <select class="form-control" id="calculationMonth">
                            <option>يناير 2025</option>
                            <option>فبراير 2025</option>
                            <option>مارس 2025</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="calculationDepartment">القسم</label>
                        <select class="form-control" id="calculationDepartment">
                            <option>جميع الأقسام</option>
                            <option>قسم الصيدلة</option>
                            <option>قسم المبيعات</option>
                            <option>قسم المحاسبة</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="workingDays">أيام العمل</label>
                        <input type="number" class="form-control" id="workingDays" value="30">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="overtimeRate">معدل العمل الإضافي</label>
                        <input type="number" class="form-control" id="overtimeRate" value="1.5" step="0.1">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Salary Calculations -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">حساب رواتب الموظفين</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="salaryTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>اسم الموظف</th>
                            <th>الراتب الأساسي</th>
                            <th>أيام الحضور</th>
                            <th>ساعات إضافية</th>
                            <th>البدلات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>أحمد محمد علي</td>
                            <td>8,000 ج.م</td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="28" max="30">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="10" step="0.5">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="1500">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="500">
                            </td>
                            <td class="font-weight-bold text-success">8,900 ج.م</td>
                            <td><span class="badge badge-warning">قيد الحساب</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary calculate-btn">
                                    <i class="fas fa-calculator"></i>
                                </button>
                                <button class="btn btn-sm btn-success approve-btn">
                                    <i class="fas fa-check"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>فاطمة أحمد حسن</td>
                            <td>7,500 ج.م</td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="30" max="30">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="5" step="0.5">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="1200">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="300">
                            </td>
                            <td class="font-weight-bold text-success">8,550 ج.م</td>
                            <td><span class="badge badge-success">محسوب</span></td>
                            <td>
                                <button class="btn btn-sm btn-info view-btn">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-warning edit-btn">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>محمد حسن علي</td>
                            <td>9,000 ج.م</td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="25" max="30">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="15" step="0.5">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="2000">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="600">
                            </td>
                            <td class="font-weight-bold text-success">9,900 ج.م</td>
                            <td><span class="badge badge-warning">قيد الحساب</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary calculate-btn">
                                    <i class="fas fa-calculator"></i>
                                </button>
                                <button class="btn btn-sm btn-success approve-btn">
                                    <i class="fas fa-check"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>سارة محمود أحمد</td>
                            <td>8,500 ج.م</td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="29" max="30">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="8" step="0.5">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="1800">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm" value="400">
                            </td>
                            <td class="font-weight-bold text-success">10,100 ج.م</td>
                            <td><span class="badge badge-success">محسوب</span></td>
                            <td>
                                <button class="btn btn-sm btn-info view-btn">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-warning edit-btn">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Summary -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الرواتب المحسوبة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">37,450 ج.م</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الموظفين المحسوبين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">2</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                قيد الحساب
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">2</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                متوسط الراتب
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">9,362 ج.م</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center">
                    <button class="btn btn-primary btn-lg mr-3">
                        <i class="fas fa-save"></i> حفظ الحسابات
                    </button>
                    <button class="btn btn-success btn-lg mr-3">
                        <i class="fas fa-check-double"></i> اعتماد جميع الرواتب
                    </button>
                    <button class="btn btn-info btn-lg">
                        <i class="fas fa-file-export"></i> تصدير النتائج
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate individual salary
    document.querySelectorAll('.calculate-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Add calculation logic here
            alert('تم حساب الراتب بنجاح!');
        });
    });

    // Calculate all salaries
    document.getElementById('calculateAllBtn').addEventListener('click', function() {
        alert('تم حساب جميع الرواتب بنجاح!');
    });
});
</script>
@endsection
