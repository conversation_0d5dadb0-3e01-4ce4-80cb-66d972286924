@extends('layouts.admin')

@section('title', $title ?? 'قيد التطوير')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-tools fa-5x text-primary mb-3"></i>
                    </div>
                    
                    <h2 class="h3 mb-3 text-gray-800">{{ $title ?? 'هذه الصفحة' }}</h2>
                    <p class="lead text-gray-600 mb-4">
                        هذا القسم قيد التطوير حالياً وسيكون متاحاً قريباً
                    </p>
                    
                    <div class="row text-center mb-4">
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-code fa-2x text-info mb-2"></i>
                                <h5>تطوير متقدم</h5>
                                <p class="text-muted small">نعمل على تطوير أفضل الحلول</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                                <h5>تصميم متجاوب</h5>
                                <p class="text-muted small">يعمل على جميع الأجهزة</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-shield-alt fa-2x text-warning mb-2"></i>
                                <h5>أمان عالي</h5>
                                <p class="text-muted small">حماية متقدمة للبيانات</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>ملاحظة:</strong> يمكنك استخدام APIs المتاحة للوصول للبيانات برمجياً
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-primary btn-lg mr-3">
                            <i class="fas fa-arrow-left mr-2"></i>
                            العودة للوحة التحكم
                        </a>
                        <a href="{{ route('admin.enhanced.dashboard') }}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            لوحة التحكم المحسنة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Available APIs Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-code mr-2"></i>
                        APIs المتاحة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">APIs الموظفين</h6>
                            <ul class="list-unstyled">
                                <li><code>GET /api/profile</code> - الملف الشخصي</li>
                                <li><code>POST /api/profile/update</code> - تحديث الملف</li>
                                <li><code>POST /api/profile/change-password</code> - تغيير كلمة المرور</li>
                                <li><code>GET /api/profile/documents</code> - المستندات</li>
                            </ul>
                            
                            <h6 class="text-success">APIs الحضور</h6>
                            <ul class="list-unstyled">
                                <li><code>GET /api/attendance/today</code> - حضور اليوم</li>
                                <li><code>POST /api/attendance/check-in</code> - تسجيل دخول</li>
                                <li><code>POST /api/attendance/check-out</code> - تسجيل خروج</li>
                                <li><code>GET /api/attendance/history</code> - تاريخ الحضور</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-warning">APIs الإجازات</h6>
                            <ul class="list-unstyled">
                                <li><code>GET /api/leaves/balance</code> - رصيد الإجازات</li>
                                <li><code>POST /api/leaves/submit-request</code> - طلب إجازة</li>
                                <li><code>GET /api/leaves/history</code> - تاريخ الإجازات</li>
                                <li><code>GET /api/leaves/types</code> - أنواع الإجازات</li>
                            </ul>
                            
                            <h6 class="text-info">APIs الرواتب</h6>
                            <ul class="list-unstyled">
                                <li><code>GET /api/payroll/current-salary</code> - الراتب الحالي</li>
                                <li><code>GET /api/payroll/history</code> - تاريخ الرواتب</li>
                                <li><code>GET /api/payroll/advances</code> - السلف</li>
                                <li><code>POST /api/payroll/request-advance</code> - طلب سلفة</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-light mt-3">
                        <i class="fas fa-key mr-2"></i>
                        <strong>المصادقة:</strong> جميع APIs تتطلب Bearer Token في header Authorization
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-item {
    padding: 1rem;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.card {
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175) !important;
}

code {
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    color: #e83e8c;
}
</style>
@endsection
