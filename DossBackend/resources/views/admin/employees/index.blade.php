@extends('layouts.admin')

@section('title', 'إدارة الموظفين')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الموظفين</h1>
        <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> إضافة موظف جديد
        </a>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Employee Stats Cards -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الموظفين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalEmployees ?? 50 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الموظفين النشطين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activeEmployees ?? 48 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                حاضرين اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $presentToday ?? 42 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الأقسام
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalDepartments ?? 6 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الموظفين</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                    aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">إجراءات:</div>
                    <a class="dropdown-item" href="#">تصدير Excel</a>
                    <a class="dropdown-item" href="#">تصدير PDF</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <input type="text" class="form-control" placeholder="البحث بالاسم أو البريد الإلكتروني...">
                </div>
                <div class="col-md-3">
                    <select class="form-control">
                        <option value="">جميع الأقسام</option>
                        <option value="pharmacy">الصيدلة</option>
                        <option value="sales">المبيعات</option>
                        <option value="accounting">المحاسبة</option>
                        <option value="hr">الموارد البشرية</option>
                        <option value="marketing">التسويق</option>
                        <option value="customer_service">خدمة العملاء</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-control">
                        <option value="">جميع الفروع</option>
                        <option value="main">الفرع الرئيسي</option>
                        <option value="malaz">فرع الملز</option>
                        <option value="olaya">فرع العليا</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary btn-block">بحث</button>
                </div>
            </div>

            <!-- Table -->
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>القسم</th>
                            <th>الفرع</th>
                            <th>تاريخ الانضمام</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for($i = 1; $i <= 10; $i++)
                        <tr>
                            <td>{{ $i }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img class="rounded-circle mr-2" src="https://via.placeholder.com/40x40" width="40" height="40">
                                    <div>
                                        <div class="font-weight-bold">موظف رقم {{ $i }}</div>
                                        <div class="text-muted small">ID: EMP{{ str_pad($i, 3, '0', STR_PAD_LEFT) }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>employee{{ $i }}@dosspharmacy.com</td>
                            <td>
                                @php
                                    $departments = ['الصيدلة', 'المبيعات', 'المحاسبة', 'الموارد البشرية', 'التسويق', 'خدمة العملاء'];
                                    $dept = $departments[($i - 1) % count($departments)];
                                @endphp
                                <span class="badge badge-primary">{{ $dept }}</span>
                            </td>
                            <td>
                                @php
                                    $branches = ['الفرع الرئيسي', 'فرع الملز', 'فرع العليا'];
                                    $branch = $branches[($i - 1) % count($branches)];
                                @endphp
                                {{ $branch }}
                            </td>
                            <td>{{ now()->subDays(rand(30, 365))->format('Y-m-d') }}</td>
                            <td>
                                @if($i <= 48)
                                    <span class="badge badge-success">نشط</span>
                                @else
                                    <span class="badge badge-secondary">غير نشط</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" title="تحرير">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endfor
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">السابق</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">التالي</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Add any JavaScript for employee management here
    console.log('Employee management page loaded');
</script>
@endpush
