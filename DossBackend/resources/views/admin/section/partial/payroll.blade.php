@canany([
    'list_payroll',
    'list_employee_advance',
    'list_payroll_report'
])
    <li class="nav-item {{
                           request()->routeIs('admin.payroll.*') ||
                           request()->routeIs('admin.employee-advances.*') ||
                           request()->routeIs('admin.salary-settings.*')
                        ? 'active' : ''
                        }}">
        <a class="nav-link" data-bs-toggle="collapse"
           href="#payroll_management"
           data-href="#"
           role="button" aria-expanded="false" aria-controls="payroll_management">
            <i class="link-icon" data-feather="dollar-sign"></i>
            <span class="link-title">إدارة الرواتب</span>
            <i class="link-arrow" data-feather="chevron-down"></i>
        </a>
        <div class="{{
                           request()->routeIs('admin.payroll.*') ||
                           request()->routeIs('admin.employee-advances.*') ||
                           request()->routeIs('admin.salary-settings.*')
                        ? '' : 'collapse'
                        }}" id="payroll_management">
            <ul class="nav sub-menu">
                
                <!-- Payroll Dashboard -->
                <li class="nav-item">
                    <a href="{{ route('admin.payroll.index') }}"
                       data-href="{{ route('admin.payroll.index') }}"
                       class="nav-link {{ request()->routeIs('admin.payroll.index') ? 'active' : '' }}">
                        <i class="link-icon" data-feather="pie-chart"></i>
                        لوحة الرواتب
                    </a>
                </li>

                <!-- Payroll Management -->
                @can('list_payroll')
                    <li class="nav-item">
                        <a href="{{ route('admin.payroll.list') }}"
                           data-href="{{ route('admin.payroll.list') }}"
                           class="nav-link {{ request()->routeIs('admin.payroll.list') ? 'active' : '' }}">
                            <i class="link-icon" data-feather="file-text"></i>
                            كشوف الرواتب
                        </a>
                    </li>
                @endcan

                <!-- Salary Calculation -->
                <li class="nav-item">
                    <a href="{{ route('admin.payroll.calculate') }}"
                       data-href="{{ route('admin.payroll.calculate') }}"
                       class="nav-link {{ request()->routeIs('admin.payroll.calculate') ? 'active' : '' }}">
                        <i class="link-icon" data-feather="calculator"></i>
                        حساب الرواتب
                    </a>
                </li>

                <!-- Employee Advances -->
                @can('list_employee_advance')
                    <li class="nav-item">
                        <a href="{{ route('admin.employee-advances.index') }}"
                           data-href="{{ route('admin.employee-advances.index') }}"
                           class="nav-link {{ request()->routeIs('admin.employee-advances.*') ? 'active' : '' }}">
                            <i class="link-icon" data-feather="trending-down"></i>
                            السلف
                        </a>
                    </li>
                @endcan

                <!-- Overtime Management -->
                <li class="nav-item">
                    <a href="{{ route('admin.payroll.overtime') }}"
                       data-href="{{ route('admin.payroll.overtime') }}"
                       class="nav-link {{ request()->routeIs('admin.payroll.overtime') ? 'active' : '' }}">
                        <i class="link-icon" data-feather="clock"></i>
                        العمل الإضافي
                    </a>
                </li>

                <!-- Salary Settings -->
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="collapse"
                       href="#salary_settings"
                       data-href="#"
                       role="button" aria-expanded="false" aria-controls="salary_settings">
                        <i class="link-icon" data-feather="settings"></i>
                        <span class="link-title">إعدادات الرواتب</span>
                        <i class="link-arrow" data-feather="chevron-down"></i>
                    </a>
                    <div class="collapse" id="salary_settings">
                        <ul class="nav sub-menu">
                            <li class="nav-item">
                                <a href="{{ route('admin.salary-settings.basic') }}"
                                   data-href="{{ route('admin.salary-settings.basic') }}"
                                   class="nav-link">
                                    <i class="link-icon" data-feather="dollar-sign"></i>
                                    الرواتب الأساسية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ route('admin.salary-settings.allowances') }}"
                                   data-href="{{ route('admin.salary-settings.allowances') }}"
                                   class="nav-link">
                                    <i class="link-icon" data-feather="plus-circle"></i>
                                    البدلات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ route('admin.salary-settings.deductions') }}"
                                   data-href="{{ route('admin.salary-settings.deductions') }}"
                                   class="nav-link">
                                    <i class="link-icon" data-feather="minus-circle"></i>
                                    الاستقطاعات
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

            </ul>
        </div>
    </li>
@endcanany
