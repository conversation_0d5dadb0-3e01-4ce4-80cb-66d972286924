<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="نظام إدارة الموظفين - صيدلية دوس">
    <meta name="author" content="Doss Pharmacy">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'لوحة التحكم') - نظام إدارة الموظفين</title>

    <!-- Custom fonts for this template-->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fc;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #2196F3 10%, #1976D2 100%);
            min-height: 100vh;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 1.5rem;
            border-radius: 0.35rem;
            margin: 0.25rem 1rem;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 1.5rem;
            text-align: center;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #fff !important;
        }
        
        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .border-left-primary {
            border-right: 0.25rem solid #2196F3 !important;
        }
        
        .border-left-success {
            border-right: 0.25rem solid #4CAF50 !important;
        }
        
        .border-left-warning {
            border-right: 0.25rem solid #FF9800 !important;
        }
        
        .border-left-info {
            border-right: 0.25rem solid #00BCD4 !important;
        }
        
        .border-left-danger {
            border-right: 0.25rem solid #F44336 !important;
        }
        
        .border-left-secondary {
            border-right: 0.25rem solid #6C757D !important;
        }
        
        .text-primary { color: #2196F3 !important; }
        .text-success { color: #4CAF50 !important; }
        .text-warning { color: #FF9800 !important; }
        .text-info { color: #00BCD4 !important; }
        .text-danger { color: #F44336 !important; }
        
        .bg-primary { background-color: #2196F3 !important; }
        .bg-success { background-color: #4CAF50 !important; }
        .bg-warning { background-color: #FF9800 !important; }
        .bg-info { background-color: #00BCD4 !important; }
        .bg-danger { background-color: #F44336 !important; }
        
        .icon-circle {
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .topbar {
            background-color: #fff;
            border-bottom: 1px solid #e3e6f0;
        }
        
        .dropdown-list {
            max-height: 15rem;
            overflow-y: auto;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1050;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .content-wrapper {
                margin-right: 0 !important;
            }
        }
    </style>
    
    @stack('styles')
</head>

<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="p-3">
                <a class="navbar-brand d-flex align-items-center justify-content-center" href="{{ route('admin.dashboard') }}">
                    <div class="sidebar-brand-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="sidebar-brand-text mx-3">صيدلية دوس</div>
                </a>
            </div>
            
            <hr class="sidebar-divider my-0">
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                       href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-fw fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>


                <hr class="sidebar-divider">
                
                <div class="sidebar-heading text-white-50 small">
                    إدارة الموظفين
                </div>
                
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.employees.*') ? 'active' : '' }}"
                       href="{{ route('admin.employees.index') }}">
                        <i class="fas fa-fw fa-users"></i>
                        <span>الموظفين</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.departments.*') ? 'active' : '' }}"
                       href="{{ route('admin.departments.index') }}">
                        <i class="fas fa-fw fa-building"></i>
                        <span>الأقسام</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.branches.*') ? 'active' : '' }}"
                       href="{{ route('admin.branches.index') }}">
                        <i class="fas fa-fw fa-map-marker-alt"></i>
                        <span>الفروع</span>
                    </a>
                </li>
                
                <hr class="sidebar-divider">
                
                <div class="sidebar-heading text-white-50 small">
                    الحضور والإجازات
                </div>
                
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.attendance.*') ? 'active' : '' }}" 
                       href="{{ route('admin.attendance.index') }}">
                        <i class="fas fa-fw fa-clock"></i>
                        <span>الحضور والانصراف</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.leaves.*') ? 'active' : '' }}"
                       href="{{ route('admin.leaves.index') }}">
                        <i class="fas fa-fw fa-calendar-alt"></i>
                        <span>الإجازات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.holidays.*') ? 'active' : '' }}"
                       href="{{ route('admin.holidays.index') }}">
                        <i class="fas fa-fw fa-calendar-check"></i>
                        <span>الإجازات الرسمية</span>
                    </a>
                </li>
                
                <hr class="sidebar-divider">
                
                <div class="sidebar-heading text-white-50 small">
                    المالية
                </div>
                
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.payroll.*') ? 'active' : '' }}"
                       href="{{ route('admin.payroll.index') }}">
                        <i class="fas fa-fw fa-money-bill-wave"></i>
                        <span>الرواتب</span>
                    </a>
                </li>


                <hr class="sidebar-divider">

                <div class="sidebar-heading text-white-50 small">
                    الإعدادات
                </div>
                
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}"
                       href="{{ route('admin.settings.index') }}">
                        <i class="fas fa-fw fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.profile') ? 'active' : '' }}"
                       href="{{ route('admin.profile') }}">
                        <i class="fas fa-fw fa-user"></i>
                        <span>الملف الشخصي</span>
                    </a>
                </li>

                <hr class="sidebar-divider d-none d-md-block">
            </ul>
        </nav>

        <!-- Content Wrapper -->
        <div class="flex-grow-1 content-wrapper">
            <!-- Topbar -->
            <nav class="navbar navbar-expand navbar-light topbar mb-4 static-top shadow">
                <!-- Sidebar Toggle (Topbar) -->
                <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                    <i class="fa fa-bars"></i>
                </button>

                <!-- Topbar Navbar -->
                <ul class="navbar-nav mr-auto">
                    <!-- Nav Item - User Information -->
                    <li class="nav-item dropdown no-arrow">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                           data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span class="mr-2 d-none d-lg-inline text-gray-600 small">{{ auth()->user()->name ?? 'المدير' }}</span>
                            <img class="img-profile rounded-circle" src="https://via.placeholder.com/40x40" width="40">
                        </a>
                        <!-- Dropdown - User Information -->
                        <div class="dropdown-menu dropdown-menu-left shadow animated--grow-in"
                             aria-labelledby="userDropdown">
                            <a class="dropdown-item" href="{{ route('admin.profile') }}">
                                <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                الملف الشخصي
                            </a>
                            <a class="dropdown-item" href="{{ route('admin.settings.index') }}">
                                <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>
                                الإعدادات
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#logoutModal">
                                <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                تسجيل الخروج
                            </a>
                        </div>
                    </li>
                </ul>
            </nav>
            <!-- End of Topbar -->

            <!-- Begin Page Content -->
            <div class="container-fluid">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @yield('content')
            </div>
            <!-- /.container-fluid -->
        </div>
        <!-- End of Content Wrapper -->
    </div>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">تأكيد تسجيل الخروج</h5>
                    <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">هل أنت متأكد من رغبتك في تسجيل الخروج؟</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" action="{{ route('admin.logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-primary">تسجيل الخروج</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle the side navigation
        document.getElementById("sidebarToggleTop").addEventListener("click", function() {
            document.getElementById("sidebar").classList.toggle("show");
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>

    @stack('scripts')
</body>
</html>
