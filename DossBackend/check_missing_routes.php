<?php

// <PERSON>ript to check missing routes from sidebar partials
// Run this with: php check_missing_routes.php

echo "Checking Missing Routes from Sidebar Partials\n";
echo "=============================================\n\n";

// Routes that sidebar partials expect
$expectedRoutes = [
    // From user.blade.php
    'admin.users.index' => 'users',
    'admin.logout-requests.index' => 'logout-requests',
    
    // From attendance.blade.php
    'admin.attendances.index' => 'attendances',
    
    // From leave.blade.php
    'admin.leaves.index' => 'leaves',
    'admin.leave-request.index' => 'leave-request',
    
    // From holiday.blade.php
    'admin.holidays.index' => 'holidays',
    
    // From company.blade.php
    'admin.companies.index' => 'companies',
    
    // From employee-affairs.blade.php
    'admin.employee-affairs.index' => 'employee-affairs',
    'admin.awards.index' => 'awards',
    'admin.deductions.index' => 'deductions',
    
    // From notice.blade.php
    'admin.notices.index' => 'notices',
    
    // From ticket.blade.php
    'admin.tickets.index' => 'tickets',
    
    // From shiftManagement.blade.php
    'admin.shifts.index' => 'shifts',
    
    // From staticPageContent.blade.php
    'admin.static-pages.index' => 'static-pages',
    
    // From assetManagement.blade.php
    'admin.assets.index' => 'assets',
    
    // From InsuranceInstitution.blade.php
    'admin.insurance-institutions.index' => 'insurance-institutions',
];

// Check routes file
$routesFile = 'routes/web.php';
if (!file_exists($routesFile)) {
    echo "❌ Routes file not found!\n";
    exit;
}

$routesContent = file_get_contents($routesFile);

echo "Checking Expected Routes:\n";
echo "========================\n";

$missingRoutes = [];
$foundRoutes = [];

foreach ($expectedRoutes as $routeName => $routePath) {
    // Check if route name exists in routes file
    if (strpos($routesContent, "name('{$routeName}')") !== false || 
        strpos($routesContent, "->name('" . str_replace('admin.', '', $routeName) . "')") !== false) {
        echo "✅ {$routeName} - Found\n";
        $foundRoutes[] = $routeName;
    } else {
        echo "❌ {$routeName} - Missing\n";
        $missingRoutes[] = [
            'name' => $routeName,
            'path' => $routePath
        ];
    }
}

echo "\nSummary:\n";
echo "========\n";
echo "Total expected routes: " . count($expectedRoutes) . "\n";
echo "Found routes: " . count($foundRoutes) . "\n";
echo "Missing routes: " . count($missingRoutes) . "\n\n";

if (!empty($missingRoutes)) {
    echo "Missing Routes to Add:\n";
    echo "=====================\n";
    
    foreach ($missingRoutes as $route) {
        $routeName = str_replace('admin.', '', $route['name']);
        echo "Route::get('{$route['path']}', function() { return view('admin.coming-soon', ['title' => '{$route['path']}']); })->name('{$routeName}');\n";
    }
    
    echo "\nAdd these routes to the admin group in routes/web.php\n";
} else {
    echo "🎉 All routes are present!\n";
}

echo "\nNext Steps:\n";
echo "===========\n";
echo "1. Add missing routes to routes/web.php\n";
echo "2. Clear route cache: php artisan route:clear\n";
echo "3. Test the sidebar links\n";
echo "4. Create proper view files for important routes\n";
?>
