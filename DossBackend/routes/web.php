<?php

use App\Http\Controllers\Auth\AdminAuthController;
use App\Http\Controllers\Web\AppSettingController;
use App\Http\Controllers\Web\AssetController;
use App\Http\Controllers\Web\AssetTypeController;
use App\Http\Controllers\Web\AttachmentController;
use App\Http\Controllers\Web\AttendanceController;
use App\Http\Controllers\Web\BranchController;
use App\Http\Controllers\Web\ClientController;
use App\Http\Controllers\Web\CompanyController;
use App\Http\Controllers\Web\DashboardController;
use App\Http\Controllers\Web\DataExportController;
use App\Http\Controllers\Web\DepartmentController;
use App\Http\Controllers\Web\EmployeeDocumentController;
use App\Http\Controllers\Web\EmployeeLogOutRequestController;
use App\Http\Controllers\Web\GeneralSettingController;
use App\Http\Controllers\Web\HolidayController;
use App\Http\Controllers\Web\InsuranceInstitutionController;
use App\Http\Controllers\Web\LeaveController;
use App\Http\Controllers\Web\LeaveTypeController;
use App\Http\Controllers\Web\NoticeController;
use App\Http\Controllers\Web\NotificationController;
use App\Http\Controllers\Web\OfficeTimeController;
use App\Http\Controllers\Web\PostController;
use App\Http\Controllers\Web\PrivacyPolicyController;
use App\Http\Controllers\Web\ProjectController;
use App\Http\Controllers\Web\RoleController;
use App\Http\Controllers\Web\RouterController;
use App\Http\Controllers\Web\StaticPageContentController;
use App\Http\Controllers\Web\SupportController;
use App\Http\Controllers\Web\TadaAttachmentController;
use App\Http\Controllers\Web\TadaController;
use App\Http\Controllers\Web\TaskChecklistController;
use App\Http\Controllers\Web\TaskCommentController;
use App\Http\Controllers\Web\TaskController;
use App\Http\Controllers\Web\TeamMeetingController;
use App\Http\Controllers\Web\UserController;
use App\Http\Controllers\EmployeeAffairsController;
use App\Http\Controllers\EmployeeBonusController;
use App\Http\Controllers\EmployeeDeductionController;
use App\Http\Controllers\EmployeeTransferController;
use App\Http\Controllers\EmployeePromotionController;
use App\Http\Controllers\EmployeeSuggestionController;
use App\Http\Controllers\EmployeeComplaintController;
use App\Http\Controllers\EmployeeResignationController;
use App\Http\Controllers\EmployeeWarningController;
use App\Http\Controllers\Admin\AdminDashboardController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Auth::routes([
    'register' => false,
    'login' => false,
    'logout' => false
]);

Route::get('/', function () {
    return redirect()->route('admin.login');
});

/** app privacy policy route */
Route::get('privacy', [PrivacyPolicyController::class, 'index'])->name('privacy-policy');

/** Quick test routes - moved to proper admin group */

Route::group([
    'prefix' => 'admin',
    'as' => 'admin.',
    'middleware' => ['web']
], function () {
    Route::get('login', [AdminAuthController::class, 'showAdminLoginForm'])->name('login');
    Route::post('login', [AdminAuthController::class, 'login'])->name('login.process');

    Route::group(['middleware' => ['admin.auth','permission']], function () {

        Route::post('logout', [AdminAuthController::class, 'logout'])->name('logout');
        Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

        // Additional dashboard and management routes
        Route::get('payroll', function() { return view('admin.payroll.index'); })->name('payroll.index');
        Route::get('enhanced-dashboard', function() { return view('admin.enhanced.dashboard'); })->name('enhanced.dashboard');
        Route::get('reports', function() { return view('admin.coming-soon', ['title' => 'لوحة التقارير']); })->name('reports.index');

        /** User route */
        Route::resource('users', UserController::class);
        Route::get('users/toggle-status/{id}', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::get('users/delete/{id}', [UserController::class, 'delete'])->name('users.delete');
        Route::get('users/change-workspace/{id}', [UserController::class, 'changeWorkSpace'])->name('users.change-workspace');
        Route::get('users/get-company-employee/{companyId}', [UserController::class, 'getAllCompanyEmployeeDetail'])->name('users.getAllCompanyUsers');
        Route::post('users/change-password/{userId}', [UserController::class, 'changePassword'])->name('users.change-password');
        Route::get('users/force-logout/{userId}', [UserController::class, 'forceLogOutEmployee'])->name('users.force-logout');

        /** Employee Insurance routes */
        Route::get('users/{userId}/insurance', [UserController::class, 'showInsurance'])->name('users.insurance.show');
        Route::get('users/{userId}/insurance/create', [UserController::class, 'createInsurance'])->name('users.insurance.create');
        Route::post('users/{userId}/insurance', [UserController::class, 'storeInsurance'])->name('users.insurance.store');
        Route::get('users/{userId}/insurance/edit', [UserController::class, 'editInsurance'])->name('users.insurance.edit');
        Route::put('users/{userId}/insurance', [UserController::class, 'updateInsurance'])->name('users.insurance.update');

        /** Employee Documents routes */
        Route::get('users/{userId}/documents', [EmployeeDocumentController::class, 'index'])->name('admin.employee.documents.index');
        Route::get('users/{userId}/documents/create', [EmployeeDocumentController::class, 'create'])->name('admin.employee.documents.create');
        Route::post('employee-documents', [EmployeeDocumentController::class, 'store'])->name('admin.employee.documents.store');
        Route::get('employee-documents/{documentId}', [EmployeeDocumentController::class, 'show'])->name('admin.employee.documents.show');
        Route::get('employee-documents/{documentId}/edit', [EmployeeDocumentController::class, 'edit'])->name('admin.employee.documents.edit');
        Route::put('employee-documents/{documentId}', [EmployeeDocumentController::class, 'update'])->name('admin.employee.documents.update');
        Route::post('employee-documents/{documentId}/verify', [EmployeeDocumentController::class, 'verify'])->name('admin.employee.documents.verify');
        Route::get('employee-documents/{documentId}/download', [EmployeeDocumentController::class, 'download'])->name('admin.employee.documents.download');
        Route::delete('employee-documents/{documentId}', [EmployeeDocumentController::class, 'destroy'])->name('admin.employee.documents.destroy');
        Route::get('employee-documents/types/list', [EmployeeDocumentController::class, 'getDocumentTypes'])->name('admin.employee.documents.types');

        /** company route */
        Route::resource('company', CompanyController::class);

        /** branch route */
        Route::resource('branch', BranchController::class);
        Route::get('branch/toggle-status/{id}', [BranchController::class, 'toggleStatus'])->name('branch.toggle-status');
        Route::get('branch/delete/{id}', [BranchController::class, 'delete'])->name('branch.delete');


        /** Department route */
        Route::resource('departments', DepartmentController::class);
        Route::get('departments/toggle-status/{id}', [DepartmentController::class, 'toggleStatus'])->name('departments.toggle-status');
        Route::get('departments/delete/{id}', [DepartmentController::class, 'delete'])->name('departments.delete');
        Route::get('departments/get-All-Departments/{branchId}', [DepartmentController::class, 'getAllDepartmentsByBranchId'])->name('departments.getAllDepartmentsByBranchId');


        /** post route */
        Route::resource('posts', PostController::class);
        Route::get('posts/toggle-status/{id}', [PostController::class, 'toggleStatus'])->name('posts.toggle-status');
        Route::get('posts/delete/{id}', [PostController::class, 'delete'])->name('posts.delete');
        Route::get('posts/get-All-posts/{deptId}', [PostController::class, 'getAllPostsByBranchId'])->name('posts.getAllPostsByBranchId');

        /** roles & permissions route */
        Route::resource('roles', RoleController::class);
        Route::get('roles/toggle-status/{id}', [RoleController::class, 'toggleStatus'])->name('roles.toggle-status');
        Route::get('roles/delete/{id}', [RoleController::class, 'delete'])->name('roles.delete');
        Route::get('roles/permissions/{roleId}', [RoleController::class, 'createPermission'])->name('roles.permission');
        Route::put('roles/assign-permissions/{roleId}', [RoleController::class, 'assignPermissionToRole'])->name('role.assign-permissions');

        /** office_time route */
        Route::resource('office-times', OfficeTimeController::class);
        Route::get('office-times/toggle-status/{id}', [OfficeTimeController::class, 'toggleStatus'])->name('office-times.toggle-status');
        Route::get('office-times/delete/{id}', [OfficeTimeController::class, 'delete'])->name('office-times.delete');

        /** branch_router route */
        Route::resource('routers', RouterController::class);
        Route::get('routers/toggle-status/{id}', [RouterController::class, 'toggleStatus'])->name('routers.toggle-status');
        Route::get('routers/delete/{id}', [RouterController::class, 'delete'])->name('routers.delete');

        /** insurance institution */        
        Route::resource('insurance_institution', InsuranceInstitutionController::class);
        Route::get('insurance_institution/delete/{id}', [InsuranceInstitutionController::class, 'delete'])->name('insurance_institution.delete');

        /** holiday route */
        Route::get('holidays/import-csv', [HolidayController::class, 'holidayImport'])->name('holidays.import-csv.show');
        Route::post('holidays/import-csv', [HolidayController::class, 'importHolidays'])->name('holidays.import-csv.store');
        Route::resource('holidays', HolidayController::class);
        Route::get('holidays/toggle-status/{id}', [HolidayController::class, 'toggleStatus'])->name('holidays.toggle-status');
        Route::get('holidays/delete/{id}', [HolidayController::class, 'delete'])->name('holidays.delete');

        /** app settings */
        Route::get('app-settings/index', [AppSettingController::class, 'index'])->name('app-settings.index');
        Route::get('app-settings/toggle-status/{id}', [AppSettingController::class, 'toggleStatus'])->name('app-settings.toggle-status');
        Route::get('app-settings/changeTheme', [AppSettingController::class, 'changeTheme'])->name('app-settings.change-theme');

        /** General settings */
        Route::resource('general-settings', GeneralSettingController::class);
        Route::get('general-settings/delete/{id}', [GeneralSettingController::class, 'delete'])->name('general-settings.delete');

        /** Leave route */
        Route::resource('leaves', LeaveTypeController::class);
        Route::get('leaves/toggle-status/{id}', [LeaveTypeController::class, 'toggleStatus'])->name('leaves.toggle-status');
        Route::get('leaves/toggle-early-exit/{id}', [LeaveTypeController::class, 'toggleEarlyExit'])->name('leaves.toggle-early-exit');
        Route::get('leaves/delete/{id}', [LeaveTypeController::class, 'delete'])->name('leaves.delete');
        Route::get('leaves/get-leave-types/{earlyExitStatus}', [LeaveTypeController::class, 'getLeaveTypesBasedOnEarlyExitStatus']);

        /** Company Content Management route */
        Route::resource('static-page-contents', StaticPageContentController::class);
        Route::get('static-page-contents/toggle-status/{id}', [StaticPageContentController::class, 'toggleStatus'])->name('static-page-contents.toggle-status');
        Route::get('static-page-contents/delete/{id}', [StaticPageContentController::class, 'delete'])->name('static-page-contents.delete');

        /** Notification route */
        Route::get('notifications/get-nav-notification', [NotificationController::class, 'getNotificationForNavBar'])->name('nav-notifications');
        Route::resource('notifications', NotificationController::class);
        Route::get('notifications/toggle-status/{id}', [NotificationController::class, 'toggleStatus'])->name('notifications.toggle-status');
        Route::get('notifications/delete/{id}', [NotificationController::class, 'delete'])->name('notifications.delete');
        Route::get('notifications/send-notification/{id}', [NotificationController::class, 'sendNotificationToAllCompanyUser'])->name('notifications.send-notification');

        /** Attendance route */
        Route::resource('attendances', AttendanceController::class);
        Route::get('employees/attendance/check-in/{companyId}/{userId}', [AttendanceController::class, 'checkInEmployee'])->name('employees.check-in');
        Route::get('employees/attendance/check-out/{companyId}/{userId}', [AttendanceController::class, 'checkOutEmployee'])->name('employees.check-out');
        Route::get('employees/attendance/delete/{id}', [AttendanceController::class, 'delete'])->name('attendance.delete');
        Route::get('employees/attendance/change-status/{id}', [AttendanceController::class, 'changeAttendanceStatus'])->name('attendances.change-status');
        Route::get('employees/attendance/{type}', [AttendanceController::class, 'dashboardAttendance'])->name('dashboard.takeAttendance');


        /** Leave route */
        Route::get('employees/leave-request', [LeaveController::class, 'index'])->name('leave-request.index');
        Route::post('leave-request/store', [LeaveController::class, 'storeLeaveRequest'])->name('employee-leave-request.store');
        Route::get('employees/leave-request/show/{leaveId}', [LeaveController::class, 'show'])->name('leave-request.show');
        Route::put('employees/leave-request/status-update/{leaveRequestId}', [LeaveController::class, 'updateLeaveRequestStatus'])->name('leave-request.update-status');
        Route::get('leave-request/create', [LeaveController::class, 'createLeaveRequest'])->name('leave-request.create');


        /**logout request Routes */
        Route::get('employee/logout-requests', [EmployeeLogOutRequestController::class, 'getAllCompanyEmployeeLogOutRequest'])->name('logout-requests.index');
        Route::get('employee/logout-requests/toggle-status/{employeeId}', [EmployeeLogOutRequestController::class, 'acceptLogoutRequest'])->name('logout-requests.accept');

        /** Notice route */
        Route::resource('notices', NoticeController::class);
        Route::get('notices/toggle-status/{id}', [NoticeController::class, 'toggleStatus'])->name('notices.toggle-status');
        Route::get('notices/delete/{id}', [NoticeController::class, 'delete'])->name('notices.delete');
        Route::get('notices/send-notice/{id}', [NoticeController::class, 'sendNotice'])->name('notices.send-notice');

        /** Team Meeting route */
        Route::resource('team-meetings', TeamMeetingController::class);
        Route::get('team-meetings/delete/{id}', [TeamMeetingController::class, 'delete'])->name('team-meetings.delete');
        Route::get('team-meetings/remove-image/{id}', [TeamMeetingController::class, 'removeImage'])->name('team-meetings.remove-image');

        /** Clients route */
        Route::post('clients/ajax/store', [ClientController::class, 'ajaxClientStore'])->name('clients.ajax-store');
        Route::resource('clients', ClientController::class);
        Route::get('clients/delete/{id}', [ClientController::class, 'delete'])->name('clients.delete');
        Route::get('clients/toggle-status/{id}', [ClientController::class, 'toggleIsActiveStatus'])->name('clients.toggle-status');

        /** Project Management route */
        Route::resource('projects', ProjectController::class);
        Route::get('projects/delete/{id}', [ProjectController::class, 'delete'])->name('projects.delete');
        Route::get('projects/toggle-status/{id}', [ProjectController::class, 'toggleStatus'])->name('projects.toggle-status');
        Route::get('projects/get-assigned-members/{projectId}', [ProjectController::class, 'getProjectAssignedMembersByProjectId'])->name('projects.get-assigned-members');
        Route::get('projects/get-employees-to-add/{addEmployeeType}/{projectId}', [ProjectController::class, 'getEmployeesToAddTpProject'])->name('projects.add-employee');
        Route::post('projects/update-leaders', [ProjectController::class, 'updateLeaderToProject'])->name('projects.update-leader-data');
        Route::post('projects/update-members', [ProjectController::class, 'updateMemberToProject'])->name('projects.update-member-data');

        /** Project & Task Attachment route */
        Route::get('projects/attachment/create/{projectId}', [AttachmentController::class, 'createProjectAttachment'])->name('project-attachment.create');
        Route::post('projects/attachment/store', [AttachmentController::class, 'storeProjectAttachment'])->name('project-attachment.store');
        Route::get('tasks/attachment/create/{taskId}', [AttachmentController::class, 'createTaskAttachment'])->name('task-attachment.create');
        Route::post('tasks/attachment/store', [AttachmentController::class, 'storeTaskAttachment'])->name('task-attachment.store');
        Route::get('attachment/delete/{id}', [AttachmentController::class, 'deleteAttachmentById'])->name('attachment.delete');


        /** Task Management route */
        Route::resource('tasks', TaskController::class);
        Route::get('projects/task/create/{projectId}', [TaskController::class, 'createTaskFromProjectPage'])->name('project-task.create');
        Route::get('tasks/delete/{id}', [TaskController::class, 'delete'])->name('tasks.delete');
        Route::get('tasks/toggle-status/{id}', [TaskController::class, 'toggleStatus'])->name('tasks.toggle-status');

        /** Task Checklist route */
//        Route::get('task-checklists/{taskId}', [TaskChecklistController::class,'getTaskDetailByTaskId'])->name('task-checklists.get-task-detail');
        Route::post('task-checklists/save', [TaskChecklistController::class, 'store'])->name('task-checklists.store');
        Route::get('task-checklists/edit/{id}', [TaskChecklistController::class, 'edit'])->name('task-checklists.edit');
        Route::put('task-checklists/update/{id}', [TaskChecklistController::class, 'update'])->name('task-checklists.update');
        Route::get('task-checklists/delete/{id}', [TaskChecklistController::class, 'delete'])->name('task-checklists.delete');
        Route::get('task-checklists/toggle-status/{id}', [TaskChecklistController::class, 'toggleIsCompletedStatus'])->name('task-checklists.toggle-status');

        /** Task Comments  route */
        Route::post('task-comment/store', [TaskCommentController::class, 'saveCommentDetail'])->name('task-comment.store');
        Route::get('task-comment/delete/{commentId}', [TaskCommentController::class, 'deleteComment'])->name('comment.delete');
        Route::get('task-comment/reply/delete/{replyId}', [TaskCommentController::class, 'deleteReply'])->name('reply.delete');

        /** Support route */
        Route::get('supports/get-all-query',[SupportController::class,'getAllQueriesPaginated'])->name('supports.index');
        Route::get('supports/change-seen-status/{queryId}', [SupportController::class, 'changeIsSeenStatus'])->name('supports.changeSeenStatus');
        Route::put('supports/update-status/{id}', [SupportController::class, 'changeQueryStatus'])->name('supports.updateStatus');
        Route::get('supports/delete/{id}', [SupportController::class, 'delete'])->name('supports.delete');

        /** Tada route */
        Route::put('tadas/update-status/{id}', [TadaController::class, 'changeTadaStatus'])->name('tadas.update-status');
        Route::resource('tadas', TadaController::class);
        Route::get('tadas/delete/{id}', [TadaController::class, 'delete'])->name('tadas.delete');
        Route::get('tadas/toggle-active-status/{id}', [TadaController::class, 'toggleTadaIsActive'])->name('tadas.toggle-status');

        /** Tada Attachment route */
        Route::get('tadas/attachment/create/{tadaId}', [TadaAttachmentController::class, 'create'])->name('tadas.attachment.create');
        Route::post('tadas/attachment/store', [TadaAttachmentController::class, 'store'])->name('tadas.attachment.store');
        Route::get('tadas/attachment/delete/{id}', [TadaAttachmentController::class, 'delete'])->name('tadas.attachment-delete');

        /** Export data route */
        Route::get('leave-types-export', [DataExportController::class, 'exportLeaveType'])->name('leave-type-export');
        Route::get('leave-requests-export', [DataExportController::class, 'exportEmployeeLeaveRequestLists'])->name('leave-request-export');
        Route::get('employee-detail-export', [DataExportController::class, 'exportEmployeeDetail'])->name('employee-lists-export');
        Route::get('attendance-detail-export', [DataExportController::class, 'exportAttendanceDetail'])->name('attendance-lists-export');

        /** Asset Management route */
        Route::resource('asset-types', AssetTypeController::class,[
            'except' => ['destroy']
        ]);
        Route::get('asset-types/delete/{id}', [AssetTypeController::class, 'delete'])->name('asset-types.delete');
        Route::get('asset-types/toggle-status/{id}', [AssetTypeController::class, 'toggleIsActiveStatus'])->name('asset-types.toggle-status');

        Route::resource('assets', AssetController::class,[
            'except' => ['destroy']
        ]);
        Route::get('assets/delete/{id}', [AssetController::class, 'delete'])->name('assets.delete');
        Route::get('assets/toggle-status/{id}', [AssetController::class, 'changeAvailabilityStatus'])->name('assets.change-Availability-status');

        /** Employee Affairs Routes */
        // Main Employee Affairs Dashboard
        Route::get('employee-affairs', [EmployeeAffairsController::class, 'index'])->name('employee-affairs.index');
        Route::get('employee-affairs/profile/{userId?}', [EmployeeAffairsController::class, 'profile'])->name('employee-affairs.profile');
        Route::get('employee-affairs/reports', [EmployeeAffairsController::class, 'reportsIndex'])->name('employee-affairs.reports.index');
        Route::get('employee-affairs/reports/monthly', [EmployeeAffairsController::class, 'monthlyReport'])->name('employee-affairs.reports.monthly');
        Route::get('employee-affairs/reports/comprehensive', [EmployeeAffairsController::class, 'comprehensiveReport'])->name('employee-affairs.reports.comprehensive');
        Route::get('employee-affairs/reports/financial', [EmployeeAffairsController::class, 'financialReport'])->name('employee-affairs.reports.financial');
        Route::get('employee-affairs/reports/performance', [EmployeeAffairsController::class, 'performanceReport'])->name('employee-affairs.reports.performance');
        Route::get('employee-affairs/reports/disciplinary', [EmployeeAffairsController::class, 'disciplinaryReport'])->name('employee-affairs.reports.disciplinary');

        // AJAX Routes for Employee Affairs
        Route::post('employee-affairs/process-recurring-deductions', [EmployeeAffairsController::class, 'processRecurringDeductions'])->name('employee-affairs.process-recurring-deductions');
        Route::post('employee-affairs/execute-transfers', [EmployeeAffairsController::class, 'executeTransfers'])->name('employee-affairs.execute-transfers');
        Route::post('employee-affairs/execute-promotions', [EmployeeAffairsController::class, 'executePromotions'])->name('employee-affairs.execute-promotions');
        Route::post('employee-affairs/update-expired-warnings', [EmployeeAffairsController::class, 'updateExpiredWarnings'])->name('employee-affairs.update-expired-warnings');
        Route::get('employee-affairs/api/pending-approvals', [EmployeeAffairsController::class, 'getPendingApprovals'])->name('employee-affairs.api.pending-approvals');
        Route::get('employee-affairs/api/follow-up-items', [EmployeeAffairsController::class, 'getFollowUpItems'])->name('employee-affairs.api.follow-up-items');
        Route::get('employee-affairs/api/dashboard-stats', [EmployeeAffairsController::class, 'getDashboardStats'])->name('employee-affairs.api.dashboard-stats');

        // Employee Bonuses
        Route::resource('employee-bonuses', EmployeeBonusController::class);
        Route::post('employee-bonuses/{employeeBonus}/approve', [EmployeeBonusController::class, 'approve'])->name('employee-bonuses.approve');
        Route::post('employee-bonuses/{employeeBonus}/reject', [EmployeeBonusController::class, 'reject'])->name('employee-bonuses.reject');
        Route::get('employee-bonuses/user/{userId}', [EmployeeBonusController::class, 'getUserBonuses'])->name('employee-bonuses.user');
        Route::get('employee-bonuses/api/stats', [EmployeeBonusController::class, 'getStats'])->name('employee-bonuses.api.stats');

        // Employee Deductions
        Route::resource('employee-deductions', EmployeeDeductionController::class);
        Route::post('employee-deductions/{employeeDeduction}/approve', [EmployeeDeductionController::class, 'approve'])->name('employee-deductions.approve');
        Route::post('employee-deductions/{employeeDeduction}/reject', [EmployeeDeductionController::class, 'reject'])->name('employee-deductions.reject');
        Route::post('employee-deductions/{employeeDeduction}/process-installment', [EmployeeDeductionController::class, 'processInstallment'])->name('employee-deductions.process-installment');
        Route::get('employee-deductions/user/{userId}', [EmployeeDeductionController::class, 'getUserDeductions'])->name('employee-deductions.user');
        Route::get('employee-deductions/api/stats', [EmployeeDeductionController::class, 'getStats'])->name('employee-deductions.api.stats');

        // Employee Transfers
        Route::resource('employee-transfers', EmployeeTransferController::class);
        Route::post('employee-transfers/{employeeTransfer}/approve', [EmployeeTransferController::class, 'approve'])->name('employee-transfers.approve');
        Route::post('employee-transfers/{employeeTransfer}/reject', [EmployeeTransferController::class, 'reject'])->name('employee-transfers.reject');
        Route::post('employee-transfers/{employeeTransfer}/execute', [EmployeeTransferController::class, 'execute'])->name('employee-transfers.execute');
        Route::get('employee-transfers/user/{userId}', [EmployeeTransferController::class, 'getUserTransfers'])->name('employee-transfers.user');
        Route::get('employee-transfers/api/stats', [EmployeeTransferController::class, 'getStats'])->name('employee-transfers.api.stats');

        // Employee Promotions
        Route::resource('employee-promotions', EmployeePromotionController::class);
        Route::post('employee-promotions/{employeePromotion}/approve', [EmployeePromotionController::class, 'approve'])->name('employee-promotions.approve');
        Route::post('employee-promotions/{employeePromotion}/reject', [EmployeePromotionController::class, 'reject'])->name('employee-promotions.reject');
        Route::post('employee-promotions/{employeePromotion}/execute', [EmployeePromotionController::class, 'execute'])->name('employee-promotions.execute');
        Route::get('employee-promotions/user/{userId}', [EmployeePromotionController::class, 'getUserPromotions'])->name('employee-promotions.user');
        Route::get('employee-promotions/api/stats', [EmployeePromotionController::class, 'getStats'])->name('employee-promotions.api.stats');

        // Employee Suggestions
        Route::resource('employee-suggestions', EmployeeSuggestionController::class);
        Route::post('employee-suggestions/{employeeSuggestion}/review', [EmployeeSuggestionController::class, 'review'])->name('employee-suggestions.review');
        Route::post('employee-suggestions/{employeeSuggestion}/implement', [EmployeeSuggestionController::class, 'implement'])->name('employee-suggestions.implement');
        Route::get('employee-suggestions/user/{userId}', [EmployeeSuggestionController::class, 'getUserSuggestions'])->name('employee-suggestions.user');
        Route::get('employee-suggestions/api/stats', [EmployeeSuggestionController::class, 'getStats'])->name('employee-suggestions.api.stats');

        // Employee Complaints
        Route::resource('employee-complaints', EmployeeComplaintController::class);
        Route::post('employee-complaints/{employeeComplaint}/investigate', [EmployeeComplaintController::class, 'investigate'])->name('employee-complaints.investigate');
        Route::post('employee-complaints/{employeeComplaint}/resolve', [EmployeeComplaintController::class, 'resolve'])->name('employee-complaints.resolve');
        Route::get('employee-complaints/user/{userId}', [EmployeeComplaintController::class, 'getUserComplaints'])->name('employee-complaints.user');
        Route::get('employee-complaints/api/stats', [EmployeeComplaintController::class, 'getStats'])->name('employee-complaints.api.stats');

        // Employee Resignations
        Route::resource('employee-resignations', EmployeeResignationController::class);
        Route::post('employee-resignations/{employeeResignation}/approve', [EmployeeResignationController::class, 'approve'])->name('employee-resignations.approve');
        Route::post('employee-resignations/{employeeResignation}/reject', [EmployeeResignationController::class, 'reject'])->name('employee-resignations.reject');
        Route::post('employee-resignations/{employeeResignation}/complete-handover', [EmployeeResignationController::class, 'completeHandover'])->name('employee-resignations.complete-handover');
        Route::post('employee-resignations/{employeeResignation}/complete-exit-interview', [EmployeeResignationController::class, 'completeExitInterview'])->name('employee-resignations.complete-exit-interview');
        Route::post('employee-resignations/{employeeResignation}/process-final-settlement', [EmployeeResignationController::class, 'processFinalSettlement'])->name('employee-resignations.process-final-settlement');
        Route::get('employee-resignations/user/{userId}', [EmployeeResignationController::class, 'getUserResignations'])->name('employee-resignations.user');
        Route::get('employee-resignations/api/stats', [EmployeeResignationController::class, 'getStats'])->name('employee-resignations.api.stats');

        // Employee Warnings
        Route::resource('employee-warnings', EmployeeWarningController::class);
        Route::post('employee-warnings/{employeeWarning}/acknowledge', [EmployeeWarningController::class, 'acknowledge'])->name('employee-warnings.acknowledge');
        Route::post('employee-warnings/{employeeWarning}/follow-up', [EmployeeWarningController::class, 'followUp'])->name('employee-warnings.follow-up');
        Route::post('employee-warnings/{employeeWarning}/resolve', [EmployeeWarningController::class, 'resolve'])->name('employee-warnings.resolve');
        Route::post('employee-warnings/{employeeWarning}/escalate', [EmployeeWarningController::class, 'escalate'])->name('employee-warnings.escalate');
        Route::get('employee-warnings/user/{userId}', [EmployeeWarningController::class, 'getUserWarnings'])->name('employee-warnings.user');
        Route::get('employee-warnings/api/stats', [EmployeeWarningController::class, 'getStats'])->name('employee-warnings.api.stats');
    });
});

// Test Routes (without middleware for debugging)
Route::get('/test-payroll', function() {
    return response()->file(base_path('test_payroll_route.php'));
});

Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/enhanced-quick-stats', [AdminDashboardController::class, 'getQuickStats'])->name('enhanced.quick-stats');

    // Missing Admin Routes
    Route::get('/employees', function() { return view('admin.employees.index'); })->name('employees.index');
    Route::get('/departments', function() { return view('admin.department.index'); })->name('departments.index');
    Route::get('/branches', function() { return view('admin.branch.index'); })->name('branches.index');
    Route::get('/attendance', function() { return view('admin.attendance.index'); })->name('attendance.index');
    Route::get('/leaves', function() { return view('admin.leaveRequest.index'); })->name('leaves.index');
    Route::get('/holidays', function() { return view('admin.holiday.index'); })->name('holidays.index');
    // Additional Payroll Management Routes
    Route::get('/payroll/list', function() { return view('admin.coming-soon', ['title' => 'كشوف الرواتب']); })->name('payroll.list');
    Route::get('/payroll/settings', function() { return view('admin.coming-soon', ['title' => 'إعدادات الرواتب']); })->name('payroll.settings');
    Route::get('/payroll/reports/monthly', function() { return view('admin.coming-soon', ['title' => 'التقرير الشهري']); })->name('payroll.reports.monthly');
    Route::get('/payroll/reports/annual', function() { return view('admin.coming-soon', ['title' => 'التقرير السنوي']); })->name('payroll.reports.annual');
    Route::get('/payroll/reports/advances', function() { return view('admin.coming-soon', ['title' => 'تقرير السلف']); })->name('payroll.reports.advances');

    // Employee Advances Routes
    Route::get('/employee-advances', function() { return view('admin.coming-soon', ['title' => 'إدارة السلف']); })->name('employee-advances.index');

    // Employee Affairs Routes
    Route::get('/employee-affairs', function() { return view('admin.coming-soon', ['title' => 'شؤون الموظفين']); })->name('employee-affairs.index');
    Route::get('/suggestions', function() { return view('admin.coming-soon', ['title' => 'الاقتراحات']); })->name('suggestions.index');
    Route::get('/complaints', function() { return view('admin.coming-soon', ['title' => 'الشكاوى']); })->name('complaints.index');

    // Reports Routes
    Route::get('/reports', function() { return view('admin.coming-soon', ['title' => 'لوحة التقارير']); })->name('reports.index');
    Route::get('/reports/attendance', function() { return view('admin.coming-soon', ['title' => 'تقارير الحضور']); })->name('reports.attendance');
    Route::get('/reports/leaves', function() { return view('admin.coming-soon', ['title' => 'تقارير الإجازات']); })->name('reports.leaves');
    Route::get('/reports/payroll', function() { return view('admin.coming-soon', ['title' => 'تقارير الرواتب']); })->name('reports.payroll');
    Route::get('/reports/employees', function() { return view('admin.coming-soon', ['title' => 'تقارير الموظفين']); })->name('reports.employees');
    Route::get('/reports/performance', function() { return view('admin.coming-soon', ['title' => 'تقارير الأداء']); })->name('reports.performance');
    Route::get('/reports/custom', function() { return view('admin.coming-soon', ['title' => 'تقارير مخصصة']); })->name('reports.custom');
    Route::get('/settings', function() { return view('admin.generalSetting.index'); })->name('settings.index');
    Route::get('/profile', function() { return view('admin.coming-soon', ['title' => 'الملف الشخصي']); })->name('profile');
});

Route::fallback(function() {
    return view('errors.404');
});

