<?php

use App\Http\Controllers\Api\AttendanceApiController;
use App\Http\Controllers\Api\DashboardApiController;
use App\Http\Controllers\Api\EmployeeLeaveApiController;
use App\Http\Controllers\Api\HolidayApiController;
use App\Http\Controllers\Api\LeaveApiController;
use App\Http\Controllers\Api\LeaveTypeApiController;
use App\Http\Controllers\Api\NoticeApiController;
use App\Http\Controllers\Api\NotificationApiController;
use App\Http\Controllers\Api\ProjectApiController;
use App\Http\Controllers\Api\ProjectManagementDashboardApiController;
use App\Http\Controllers\Api\StaticPageContentApiController;
use App\Http\Controllers\Api\SupportApiController;
use App\Http\Controllers\Api\TadaApiController;
use App\Http\Controllers\Api\TaskApiController;
use App\Http\Controllers\Api\TaskChecklistApiController;
use App\Http\Controllers\Api\TaskCommentApiController;
use App\Http\Controllers\Api\TeamMeetingApiController;
use App\Http\Controllers\Api\UserProfileApiController;
use App\Http\Controllers\Api\PayrollApiController;
use App\Http\Controllers\Api\EmployeeAffairsApiController;
use App\Http\Controllers\Api\InsuranceApiController;
use App\Http\Controllers\Api\EnhancedAttendanceApiController;
use App\Http\Controllers\Api\EnhancedLeaveApiController;
use App\Http\Controllers\Api\EnhancedUserProfileApiController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\Auth\AuthApiController;

/**   user login **/
Route::post('login', [AuthApiController::class,'login']);

Route::get('team-meetings/{id}', [TeamMeetingApiController::class, 'findTeamMeetingDetail']);
Route::group([
    'middleware' => ['auth:api','permission']
], function () {

    /**   user logout **/
    Route::get('logout', [AuthApiController::class, 'logout'])->name('user.logout');

    /** Users Routes **/
    Route::get('users/profile', [UserProfileApiController::class, 'userProfileDetail'])->name('users.profile');
    Route::post('users/change-password', [UserProfileApiController::class, 'changePassword'])->name('users.change-password');
    Route::post('users/update-profile', [UserProfileApiController::class, 'updateUserProfile'])->name('users.update-profile');
    Route::get('users/profile-detail/{userId}', [UserProfileApiController::class, 'findEmployeeDetailById']);
    Route::get('users/company/team-sheet', [UserProfileApiController::class, 'getTeamSheetOfCompany'])->name('users.company.team-sheet');

    /** content management Routes **/
    Route::get('static-page-content/{contentType}', [StaticPageContentApiController::class, 'getStaticPageContentByContentType']);
    Route::get('company-rules', [StaticPageContentApiController::class, 'getCompanyRulesDetail']);
    Route::get('static-page-content/{contentType}/{titleSlug}', [StaticPageContentApiController::class, 'getStaticPageContentByContentTypeAndTitleSlug']);

    /** notifications Routes **/
    Route::get('notifications', [NotificationApiController::class, 'getAllRecentPublishedNotification']);

    /** notice Routes **/
    Route::get('notices', [NoticeApiController::class, 'getAllRecentlyReceivedNotice']);

    /** Dashboard Routes **/
    Route::get('dashboard', [DashboardApiController::class, 'userDashboardDetail']);

    /** Attendance Routes **/
    Route::post('employees/check-in', [AttendanceApiController::class, 'employeeCheckIn']);
    Route::post('employees/check-out', [AttendanceApiController::class, 'employeeCheckOut']);
    Route::get('employees/attendance-detail', [AttendanceApiController::class, 'getEmployeeAllAttendanceDetailOfTheMonth']);

    /** Leave Request Routes **/
    Route::get('leave-types', [LeaveTypeApiController::class, 'getAllLeaveTypeWithEmployeeLeaveRecord']);
    Route::post('leave-requests/store', [LeaveApiController::class, 'saveLeaveRequestDetail']);
    Route::get('leave-requests/employee-leave-requests', [LeaveApiController::class, 'getAllLeaveRequestOfEmployee']);
    Route::get('leave-requests/employee-leave-calendar', [LeaveApiController::class, 'getLeaveCountDetailOfEmployeeOfTwoMonth']);
    Route::get('leave-requests/employee-leave-list', [LeaveApiController::class, 'getAllEmployeeLeaveDetailBySpecificDay']);
    Route::get('leave-requests/cancel/{leaveRequestId}', [LeaveApiController::class, 'cancelLeaveRequest']);


    /** Team Meeting Routes **/
    Route::get('team-meetings', [TeamMeetingApiController::class, 'getAllAssignedTeamMeetingDetail']);

    /** Holiday route */
    Route::get('holidays', [HolidayApiController::class, 'getAllActiveHoliday'])->name('holiday.getAllHolidays');

    /** Project Management Dashboard route */
    Route::get('project-management-dashboard', [ProjectManagementDashboardApiController::class, 'getUserProjectManagementDashboardDetail']);

    /** Project route */
    Route::get('assigned-projects-list', [ProjectApiController::class, 'getUserAssignedAllProjects']);
    Route::get('assigned-projects-detail/{projectId}', [ProjectApiController::class, 'getProjectDetailById']);

    /** Tasks route */
    Route::get('assigned-task-list', [TaskApiController::class, 'getUserAssignedAllTasks']);
    Route::get('assigned-task-detail/{taskId}', [TaskApiController::class, 'getTaskDetailById']);
    Route::get('assigned-task-detail/change-status/{taskId}', [TaskApiController::class, 'changeTaskStatus']);
    Route::get('assigned-task-comments', [TaskApiController::class, 'getTaskComments']);

    /** Task checklist route */
    Route::get('assigned-task-checklist/toggle-status/{checklistId}', [TaskChecklistApiController::class, 'toggleCheckListIsCompletedStatus']);

    /** Task Comment route */
    Route::post('assigned-task/comments/store', [TaskCommentApiController::class, 'saveComment']);
    Route::get('assigned-task/comment/delete/{commentId}', [TaskCommentApiController::class, 'deleteComment']);
    Route::get('assigned-task/reply/delete/{replyId}', [TaskCommentApiController::class, 'deleteReply']);

    /** Support route */
    Route::post('support/query-store', [SupportApiController::class, 'store']);
    Route::get('support/department-lists', [SupportApiController::class, 'getAuthUserBranchDepartmentLists']);
    Route::get('support/get-user-query-lists', [SupportApiController::class, 'getAllAuthUserSupportQueryList']);

    /** Tada route */
    Route::get('employee/tada-lists', [TadaApiController::class, 'getEmployeesTadaLists']);
    Route::get('employee/tada-details/{tadaId}', [TadaApiController::class, 'getEmployeesTadaDetail']);
    Route::post('employee/tada/store', [TadaApiController::class, 'storeTadaDetail']);
    Route::post('employee/tada/update', [TadaApiController::class, 'updateTadaDetail']);
    Route::get('employee/tada/delete-attachment/{attachmentId}', [TadaApiController::class, 'deleteTadaAttachment']);

    /** Payroll Routes */
    Route::get('payroll/current-salary', [PayrollApiController::class, 'getCurrentSalary']);
    Route::get('payroll/history', [PayrollApiController::class, 'getPayrollHistory']);
    Route::get('payroll/details/{id}', [PayrollApiController::class, 'getPayrollDetails']);
    Route::get('payroll/advances', [PayrollApiController::class, 'getAdvances']);
    Route::post('payroll/request-advance', [PayrollApiController::class, 'requestAdvance']);

    /** Employee Affairs Routes */
    Route::get('employee-affairs/bonuses', [EmployeeAffairsApiController::class, 'getBonuses']);
    Route::get('employee-affairs/deductions', [EmployeeAffairsApiController::class, 'getDeductions']);
    Route::get('employee-affairs/transfers', [EmployeeAffairsApiController::class, 'getTransfers']);
    Route::get('employee-affairs/promotions', [EmployeeAffairsApiController::class, 'getPromotions']);
    Route::get('employee-affairs/suggestions', [EmployeeAffairsApiController::class, 'getSuggestions']);
    Route::post('employee-affairs/submit-suggestion', [EmployeeAffairsApiController::class, 'submitSuggestion']);
    Route::get('employee-affairs/complaints', [EmployeeAffairsApiController::class, 'getComplaints']);
    Route::post('employee-affairs/submit-complaint', [EmployeeAffairsApiController::class, 'submitComplaint']);
    Route::get('employee-affairs/warnings', [EmployeeAffairsApiController::class, 'getWarnings']);

    /** Insurance Routes */
    Route::get('insurance/details', [InsuranceApiController::class, 'getInsuranceDetails']);
    Route::get('insurance/dependents', [InsuranceApiController::class, 'getDependents']);
    Route::post('insurance/add-dependent', [InsuranceApiController::class, 'addDependent']);
    Route::put('insurance/update-dependent/{id}', [InsuranceApiController::class, 'updateDependent']);
    Route::delete('insurance/delete-dependent/{id}', [InsuranceApiController::class, 'deleteDependent']);
    Route::get('insurance/institutions', [InsuranceApiController::class, 'getInsuranceInstitutions']);
    Route::get('insurance/summary', [InsuranceApiController::class, 'getInsuranceSummary']);

    /** Enhanced Attendance Routes */
    Route::get('attendance/today', [EnhancedAttendanceApiController::class, 'getTodayAttendance']);
    Route::get('attendance/history', [EnhancedAttendanceApiController::class, 'getAttendanceHistory']);
    Route::post('attendance/check-in', [EnhancedAttendanceApiController::class, 'checkIn']);
    Route::post('attendance/check-out', [EnhancedAttendanceApiController::class, 'checkOut']);
    Route::get('attendance/summary', [EnhancedAttendanceApiController::class, 'getAttendanceSummary']);

    /** Enhanced Leave Routes */
    Route::get('leaves/balance', [EnhancedLeaveApiController::class, 'getLeaveBalance']);
    Route::get('leaves/history', [EnhancedLeaveApiController::class, 'getLeaveHistory']);
    Route::post('leaves/submit-request', [EnhancedLeaveApiController::class, 'submitLeaveRequest']);
    Route::put('leaves/cancel-request/{id}', [EnhancedLeaveApiController::class, 'cancelLeaveRequest']);
    Route::get('leaves/types', [EnhancedLeaveApiController::class, 'getLeaveTypes']);
    Route::get('leaves/holidays', [EnhancedLeaveApiController::class, 'getHolidays']);
    Route::get('leaves/summary', [EnhancedLeaveApiController::class, 'getLeaveSummary']);

    /** Enhanced User Profile Routes */
    Route::get('profile', [EnhancedUserProfileApiController::class, 'getProfile']);
    Route::post('profile/update', [EnhancedUserProfileApiController::class, 'updateProfile']);
    Route::post('profile/change-password', [EnhancedUserProfileApiController::class, 'changePassword']);
    Route::get('profile/documents', [EnhancedUserProfileApiController::class, 'getDocuments']);
    Route::post('profile/upload-document', [EnhancedUserProfileApiController::class, 'uploadDocument']);
    Route::delete('profile/delete-document/{id}', [EnhancedUserProfileApiController::class, 'deleteDocument']);
    Route::get('profile/summary', [EnhancedUserProfileApiController::class, 'getProfileSummary']);

});


