<?php

// Test script to check all admin routes
// Run this with: php test_routes.php

$routes = [
    'admin.dashboard' => '/admin/dashboard',
    'admin.enhanced.dashboard' => '/admin/enhanced-dashboard',
    'admin.employees.index' => '/admin/employees',
    'admin.departments.index' => '/admin/departments',
    'admin.branches.index' => '/admin/branches',
    'admin.attendance.index' => '/admin/attendance',
    'admin.leaves.index' => '/admin/leaves',
    'admin.holidays.index' => '/admin/holidays',
    'admin.payroll.index' => '/admin/payroll',
    'admin.advances.index' => '/admin/advances',
    'admin.employee-affairs.index' => '/admin/employee-affairs',
    'admin.suggestions.index' => '/admin/suggestions',
    'admin.complaints.index' => '/admin/complaints',
    'admin.reports.index' => '/admin/reports',
    'admin.settings.index' => '/admin/settings',
    'admin.profile' => '/admin/profile',
];

echo "Testing Admin Routes:\n";
echo "====================\n\n";

foreach ($routes as $name => $url) {
    echo "Route: {$name}\n";
    echo "URL: {$url}\n";
    
    // Check if route file exists (basic check)
    $viewPath = str_replace(['admin.', '.index'], ['admin/', '/index'], $name);
    $viewPath = str_replace('.', '/', $viewPath);
    $filePath = "resources/views/{$viewPath}.blade.php";
    
    if (file_exists($filePath)) {
        echo "Status: ✅ View file exists\n";
    } else {
        echo "Status: ❌ View file missing: {$filePath}\n";
    }
    
    echo "---\n";
}

echo "\nSummary:\n";
echo "========\n";
echo "Total routes: " . count($routes) . "\n";

// Count existing view files
$existingFiles = 0;
foreach ($routes as $name => $url) {
    $viewPath = str_replace(['admin.', '.index'], ['admin/', '/index'], $name);
    $viewPath = str_replace('.', '/', $viewPath);
    $filePath = "resources/views/{$viewPath}.blade.php";
    
    if (file_exists($filePath)) {
        $existingFiles++;
    }
}

echo "Existing view files: {$existingFiles}\n";
echo "Missing view files: " . (count($routes) - $existingFiles) . "\n";

if ($existingFiles == count($routes)) {
    echo "\n🎉 All routes have corresponding view files!\n";
} else {
    echo "\n⚠️  Some view files are missing. Check the list above.\n";
}
