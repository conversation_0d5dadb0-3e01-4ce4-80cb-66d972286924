<?php

namespace App\Services\TeamMeeting;

use App\Helpers\AppHelper;
use App\Repositories\TeamMeetingRepository;
use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;

class TeamMeetingService
{
    private TeamMeetingRepository $teamMeetingRepo;

    public function __construct(TeamMeetingRepository $teamMeetingRepo)
    {
        $this->teamMeetingRepo = $teamMeetingRepo;
    }

    public function getAllCompanyTeamMeetings($filterParameters, $select = ['*'], $with = [])
    {
        if(AppHelper::ifDateInBsEnabled()){
            $filterParameters['meeting_from'] = isset($filterParameters['meeting_from']) ?
                AppHelper::dateInYmdFormatNepToEng($filterParameters['meeting_from']): null;
            $filterParameters['meeting_to'] = isset($filterParameters['meeting_to']) ?
                AppHelper::dateInYmdFormatNepToEng($filterParameters['meeting_to']): null;
        }
        return $this->teamMeetingRepo->getAllCompanyTeamMeetings($filterParameters, $select, $with);
    }


    public function getAllAssignedTeamMeetingDetail($perPage)
    {
        return $this->teamMeetingRepo->getAllAssignedEmployeeTeamMeetings($perPage);
    }

    /**
     * @param $id
     * @param $select
     * @param $with
     * @return mixed
     * @throws Exception
     */
    public function findOrFailTeamMeetingDetailById($id, $select = ['*'], $with = [])
    {
        $teamMeetingDetail = $this->teamMeetingRepo->findTeamMeetingDetailById($id, $select, $with);
        if (!$teamMeetingDetail) {
            throw new Exception('Team Meeting Detail Not Found', 400);
        }
        return $teamMeetingDetail;
    }

    /**
     * @param $validatedData
     * @return mixed
     * @throws Exception
     */
    public function store($validatedData)
    {
        try {
            DB::beginTransaction();
            $teamMeeting = $this->teamMeetingRepo->store($validatedData);
            if ($teamMeeting) {
                $this->createManyTeamMeetingParticipator($teamMeeting, $validatedData);
            }
            DB::commit();
            return $teamMeeting;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    /**
     * @param $teamMeetingDetail
     * @param $validatedData
     * @return bool
     * @throws Exception
     */
    public function update($teamMeetingDetail, $validatedData)
    {
        try {
            DB::beginTransaction();
            $teamMeeting = $this->teamMeetingRepo->update($teamMeetingDetail, $validatedData);
            if ($teamMeeting) {
                $deleteParticipatorDetail = $this->teamMeetingRepo->deleteTeamMeetingParticipatorsDetail($teamMeeting);
                if ($deleteParticipatorDetail) {
                    $this->createManyTeamMeetingParticipator($teamMeeting, $validatedData);
                }
            }
            DB::commit();
            return $teamMeeting;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    /**
     * @param $id
     * @return void
     * @throws Exception
     */

    public function deleteTeamMeeting($id)
    {
        try {
            DB::beginTransaction();
                $teamMeetingDetail = $this->findOrFailTeamMeetingDetailById($id);
                $this->teamMeetingRepo->delete($teamMeetingDetail);
            DB::commit();
            return;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function removeMeetingImage($id)
    {
        try {
            DB::beginTransaction();
            $teamMeetingDetail = $this->findOrFailTeamMeetingDetailById($id);
            if($teamMeetingDetail->image){
                $this->teamMeetingRepo->deleteMeetingImage($teamMeetingDetail);
            }
            DB::commit();
            return;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function createManyTeamMeetingParticipator($teamMeetingDetail, $validatedData)
    {
        try {
            DB::commit();
                $this->teamMeetingRepo->createManyTeamMeetingParticipator($teamMeetingDetail,$validatedData['participator']);
            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

}

