<?php

namespace App\Services\InsuranceInstitution;

use App\Helpers\AppHelper;
use App\Repositories\InsuranceInstitutionRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

class InsuranceInstitutionService
{
    private InsuranceInstitutionRepository $repo;

    public function __construct(InsuranceInstitutionRepository $repo)
    {
        $this->repo = $repo;
    }

    public function getAllLists($filterParameters = [], $select = ['*'], $with = [])
    {
        try {
            return $this->repo->getAllLists($filterParameters, $select, $with);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    public function findDetailById($id)
    {
        try {
            $detail = $this->repo->findDetailById($id);
            if (!$detail) {
                throw new Exception('Detail Not Found', 404);
            }
            return $detail;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    public function store($validatedData)
    {
        try {
            DB::beginTransaction();
            $item = $this->repo->store($validatedData);
            DB::commit();
            return $item;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update($validatedData, $id)
    {
        try {
            $detail = $this->findDetailById($id);
            DB::beginTransaction();
            $update = $this->repo->update($detail, $validatedData);
            DB::commit();
            return $update;
        } catch (Exception $e) {
            throw $e;
        }
    }

    public function toggleStatus($id)
    {
        try {
            DB::beginTransaction();
            $detail = $this->findDetailById($id);
            $status = $this->repo->toggleStatus($detail);
            DB::commit();
            return $status;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function delete($id)
    {
        try {
            $detail = $this->findDetailById($id);
            DB::beginTransaction();
            $delete = $this->repo->delete($detail);
            DB::commit();
            return $delete;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

}
