<?php

namespace App\Services\AssetManagement;

use App\Repositories\AssetTypeRepository;
use Exception;
use Illuminate\Support\Facades\DB;

class AssetTypeService
{
    public function __construct(
        private AssetTypeRepository $assetTypeRepo
    ){}

    public function getAllAssetTypes($select= ['*'],$with=[])
    {
        return $this->assetTypeRepo->getAllAssetTypes($select,$with);
    }

    public function getAllActiveAssetTypes($select= ['*'],$with=[])
    {
        return $this->assetTypeRepo->getAllActiveAssetTypes($select,$with);
    }

    public function findAssetTypeById($id,$select=['*'],$with=[])
    {
        try{
            $assetType =  $this->assetTypeRepo->findAssetTypeById($id,$select,$with);
            if(!$assetType){
                throw new \Exception('Asset Type Not Found',400);
            }
            return $assetType;
        }catch(Exception $exception){
            throw $exception;
        }
    }

    public function store($validatedData)
    {
        try {
            DB::beginTransaction();
            $assetTypeDetail = $this->assetTypeRepo->create($validatedData);
            DB::commit();
            return $assetTypeDetail;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function updateAssetType($id, $validatedData)
    {
        try {
            $assetTypeDetail = $this->findAssetTypeById($id);
            DB::beginTransaction();
            $updateStatus = $this->assetTypeRepo->update($assetTypeDetail, $validatedData);
            DB::commit();
            return $updateStatus;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function deleteAssetType($id): bool
    {
        try {
            $assetTypeDetail = $this->findAssetTypeById($id);
            DB::beginTransaction();
            $this->assetTypeRepo->delete($assetTypeDetail);
            DB::commit();
            return true;
        } catch (\Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function toggleIsActiveStatus($id): bool
    {
        try {
            DB::beginTransaction();
            $clientDetail = $this->findAssetTypeById($id);
            $this->assetTypeRepo->toggleIsActiveStatus($clientDetail);
            DB::commit();
            return true;
        } catch (\Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

}
