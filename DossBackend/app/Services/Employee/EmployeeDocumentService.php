<?php

namespace App\Services\Employee;

use App\Models\EmployeeDocument;
use App\Models\User;
use App\Traits\ImageService;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class EmployeeDocumentService
{
    use ImageService;

    /**
     * Get all documents for a specific employee
     */
    public function getEmployeeDocuments(int $userId, array $filters = [])
    {
        $query = EmployeeDocument::where('user_id', $userId)
            ->with(['uploadedBy:id,name', 'verifiedBy:id,name']);

        // Apply filters
        if (isset($filters['document_type'])) {
            $query->where('document_type', $filters['document_type']);
        }

        if (isset($filters['is_verified'])) {
            $query->where('is_verified', $filters['is_verified']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('document_name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('original_name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->latest()->paginate(EmployeeDocument::RECORDS_PER_PAGE);
    }

    /**
     * Upload a new document for an employee
     */
    public function uploadDocument(array $data, UploadedFile $file): EmployeeDocument
    {
        try {
            DB::beginTransaction();

            // Validate file
            $this->validateFile($file);

            // Store file
            $fileData = $this->storeFile($file);

            // Create document record
            $document = EmployeeDocument::create([
                'user_id' => $data['user_id'],
                'document_type' => $data['document_type'],
                'document_name' => $data['document_name'],
                'original_name' => $file->getClientOriginalName(),
                'file_path' => $fileData['file_path'],
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_extension' => $file->getClientOriginalExtension(),
                'description' => $data['description'] ?? null,
                'is_required' => in_array($data['document_type'], EmployeeDocument::REQUIRED_DOCUMENTS),
                'uploaded_by' => Auth::id()
            ]);

            DB::commit();
            return $document->load(['uploadedBy:id,name']);

        } catch (Exception $e) {
            DB::rollBack();
            // Clean up uploaded file if exists
            if (isset($fileData['file_path'])) {
                $this->deleteFile($fileData['file_path']);
            }
            throw $e;
        }
    }

    /**
     * Update document information
     */
    public function updateDocument(int $documentId, array $data): EmployeeDocument
    {
        $document = EmployeeDocument::findOrFail($documentId);

        $document->update([
            'document_name' => $data['document_name'] ?? $document->document_name,
            'description' => $data['description'] ?? $document->description,
        ]);

        return $document->load(['uploadedBy:id,name', 'verifiedBy:id,name']);
    }

    /**
     * Verify a document
     */
    public function verifyDocument(int $documentId, array $data): EmployeeDocument
    {
        $document = EmployeeDocument::findOrFail($documentId);

        $document->update([
            'is_verified' => $data['is_verified'],
            'verified_at' => $data['is_verified'] ? now() : null,
            'verified_by' => $data['is_verified'] ? Auth::id() : null,
            'verification_notes' => $data['verification_notes'] ?? null,
        ]);

        return $document->load(['uploadedBy:id,name', 'verifiedBy:id,name']);
    }

    /**
     * Delete a document
     */
    public function deleteDocument(int $documentId): bool
    {
        try {
            DB::beginTransaction();

            $document = EmployeeDocument::findOrFail($documentId);
            
            // Delete physical file
            $this->deleteFile($document->file_path);
            
            // Delete database record
            $document->delete();

            DB::commit();
            return true;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get document statistics for an employee
     */
    public function getEmployeeDocumentStats(int $userId): array
    {
        $total = EmployeeDocument::where('user_id', $userId)->count();
        $verified = EmployeeDocument::where('user_id', $userId)->where('is_verified', true)->count();
        $required = EmployeeDocument::where('user_id', $userId)->where('is_required', true)->count();
        $requiredVerified = EmployeeDocument::where('user_id', $userId)
            ->where('is_required', true)
            ->where('is_verified', true)
            ->count();

        return [
            'total_documents' => $total,
            'verified_documents' => $verified,
            'required_documents' => $required,
            'required_verified' => $requiredVerified,
            'completion_percentage' => $required > 0 ? round(($requiredVerified / $required) * 100, 2) : 0
        ];
    }

    /**
     * Get missing required documents for an employee
     */
    public function getMissingRequiredDocuments(int $userId): array
    {
        $existingTypes = EmployeeDocument::where('user_id', $userId)
            ->whereIn('document_type', EmployeeDocument::REQUIRED_DOCUMENTS)
            ->pluck('document_type')
            ->toArray();

        $missingTypes = array_diff(EmployeeDocument::REQUIRED_DOCUMENTS, $existingTypes);

        return array_map(function ($type) {
            return [
                'type' => $type,
                'label' => EmployeeDocument::DOCUMENT_TYPES[$type]
            ];
        }, $missingTypes);
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file): void
    {
        // Check file size
        if (!EmployeeDocument::isValidFileSize($file->getSize())) {
            throw new Exception('حجم الملف كبير جداً. الحد الأقصى ' . EmployeeDocument::MAX_FILE_SIZE . ' KB');
        }

        // Check file type
        if (!EmployeeDocument::isAllowedFileType($file->getMimeType(), $file->getClientOriginalExtension())) {
            throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', EmployeeDocument::ALLOWED_EXTENSIONS));
        }
    }

    /**
     * Store file and return file data
     */
    private function storeFile(UploadedFile $file): array
    {
        $uploadPath = public_path(EmployeeDocument::UPLOAD_PATH);
        
        // Create directory if it doesn't exist
        if (!File::exists($uploadPath)) {
            File::makeDirectory($uploadPath, 0755, true);
        }

        // Generate unique filename
        $fileName = Str::uuid() . '_' . time() . '.' . $file->getClientOriginalExtension();
        
        // Move file
        $file->move($uploadPath, $fileName);

        return [
            'file_path' => $fileName,
            'full_path' => $uploadPath . $fileName
        ];
    }

    /**
     * Delete physical file
     */
    private function deleteFile(string $fileName): void
    {
        $filePath = public_path(EmployeeDocument::UPLOAD_PATH . $fileName);
        
        if (File::exists($filePath)) {
            File::delete($filePath);
        }
    }

    /**
     * Get document by ID with relations
     */
    public function getDocumentById(int $documentId): EmployeeDocument
    {
        return EmployeeDocument::with(['user:id,name', 'uploadedBy:id,name', 'verifiedBy:id,name'])
            ->findOrFail($documentId);
    }

    /**
     * Check if user can access document
     */
    public function canUserAccessDocument(int $documentId, int $userId): bool
    {
        $document = EmployeeDocument::find($documentId);
        
        if (!$document) {
            return false;
        }

        // User can access their own documents
        if ($document->user_id === $userId) {
            return true;
        }

        // Admin or HR can access all documents
        $user = User::find($userId);
        if ($user && $user->role && in_array($user->role->slug, ['admin', 'hr'])) {
            return true;
        }

        return false;
    }
}
