<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Attendance;
use App\Models\LeaveRequest;
use App\Models\Payroll;
use App\Models\EmployeeBonus;
use App\Models\EmployeeDeduction;
use App\Models\EmployeeSuggestion;
use App\Models\EmployeeComplaint;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    public function index()
    {
        $stats = $this->getDashboardStats();
        $charts = $this->getChartData();
        $recentActivities = $this->getRecentActivities();
        
        return view('admin.dashboard.index', compact('stats', 'charts', 'recentActivities'));
    }

    private function getDashboardStats()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        
        return [
            'total_employees' => User::where('user_type', 'employee')->count(),
            'active_employees' => User::where('user_type', 'employee')
                                    ->where('is_active', 1)->count(),
            'present_today' => Attendance::whereDate('attendance_date', $today)
                                        ->whereNotNull('check_in_at')->count(),
            'absent_today' => User::where('user_type', 'employee')
                                 ->where('is_active', 1)->count() - 
                             Attendance::whereDate('attendance_date', $today)
                                      ->whereNotNull('check_in_at')->count(),
            'pending_leaves' => LeaveRequest::where('status', 'pending')->count(),
            'approved_leaves_today' => LeaveRequest::where('status', 'approved')
                                                  ->whereDate('leave_from', '<=', $today)
                                                  ->whereDate('leave_to', '>=', $today)
                                                  ->count(),
            'total_payroll_this_month' => Payroll::whereYear('created_at', $thisMonth->year)
                                                 ->whereMonth('created_at', $thisMonth->month)
                                                 ->sum('net_salary'),
            'pending_suggestions' => EmployeeSuggestion::where('status', 'pending')->count(),
            'pending_complaints' => EmployeeComplaint::where('status', 'pending')->count(),
            'late_arrivals_today' => Attendance::whereDate('attendance_date', $today)
                                              ->where('is_late', 1)->count(),
        ];
    }

    private function getChartData()
    {
        return [
            'attendance_chart' => $this->getAttendanceChartData(),
            'leave_chart' => $this->getLeaveChartData(),
            'payroll_chart' => $this->getPayrollChartData(),
            'department_chart' => $this->getDepartmentChartData(),
        ];
    }

    private function getAttendanceChartData()
    {
        $last7Days = collect();
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::today()->subDays($i);
            $present = Attendance::whereDate('attendance_date', $date)
                                ->whereNotNull('check_in_at')->count();
            $absent = User::where('user_type', 'employee')
                         ->where('is_active', 1)->count() - $present;
            
            $last7Days->push([
                'date' => $date->format('Y-m-d'),
                'present' => $present,
                'absent' => $absent,
                'late' => Attendance::whereDate('attendance_date', $date)
                                   ->where('is_late', 1)->count(),
            ]);
        }
        
        return $last7Days;
    }

    private function getLeaveChartData()
    {
        $leaveTypes = DB::table('leave_requests')
            ->join('leave_types', 'leave_requests.leave_type_id', '=', 'leave_types.id')
            ->select('leave_types.name', DB::raw('count(*) as count'))
            ->where('leave_requests.status', 'approved')
            ->whereYear('leave_requests.created_at', Carbon::now()->year)
            ->groupBy('leave_types.name')
            ->get();
            
        return $leaveTypes;
    }

    private function getPayrollChartData()
    {
        $last6Months = collect();
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $total = Payroll::whereYear('created_at', $month->year)
                           ->whereMonth('created_at', $month->month)
                           ->sum('net_salary');
            
            $last6Months->push([
                'month' => $month->format('M Y'),
                'total' => $total,
            ]);
        }
        
        return $last6Months;
    }

    private function getDepartmentChartData()
    {
        $departments = DB::table('users')
            ->join('departments', 'users.department_id', '=', 'departments.id')
            ->select('departments.dept_name', DB::raw('count(*) as count'))
            ->where('users.user_type', 'employee')
            ->where('users.is_active', 1)
            ->groupBy('departments.dept_name')
            ->get();
            
        return $departments;
    }

    private function getRecentActivities()
    {
        $activities = collect();
        
        // Recent check-ins
        $recentCheckIns = Attendance::with('user')
            ->whereDate('attendance_date', Carbon::today())
            ->whereNotNull('check_in_at')
            ->orderBy('check_in_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($attendance) {
                return [
                    'type' => 'check_in',
                    'message' => $attendance->user->name . ' سجل الحضور',
                    'time' => $attendance->check_in_at,
                    'icon' => 'login',
                    'color' => 'success'
                ];
            });
        
        // Recent leave requests
        $recentLeaves = LeaveRequest::with('user', 'leaveType')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($leave) {
                return [
                    'type' => 'leave_request',
                    'message' => $leave->user->name . ' طلب إجازة ' . $leave->leaveType->name,
                    'time' => $leave->created_at,
                    'icon' => 'beach_access',
                    'color' => 'warning'
                ];
            });
        
        // Recent suggestions
        $recentSuggestions = EmployeeSuggestion::with('user')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($suggestion) {
                return [
                    'type' => 'suggestion',
                    'message' => $suggestion->user->name . ' قدم اقتراح جديد',
                    'time' => $suggestion->created_at,
                    'icon' => 'lightbulb',
                    'color' => 'info'
                ];
            });
        
        // Recent complaints
        $recentComplaints = EmployeeComplaint::with('user')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($complaint) {
                return [
                    'type' => 'complaint',
                    'message' => $complaint->user->name . ' قدم شكوى جديدة',
                    'time' => $complaint->created_at,
                    'icon' => 'report_problem',
                    'color' => 'danger'
                ];
            });
        
        return $activities
            ->merge($recentCheckIns)
            ->merge($recentLeaves)
            ->merge($recentSuggestions)
            ->merge($recentComplaints)
            ->sortByDesc('time')
            ->take(15);
    }

    public function getQuickStats()
    {
        $today = Carbon::today();
        
        return response()->json([
            'employees_present' => Attendance::whereDate('attendance_date', $today)
                                            ->whereNotNull('check_in_at')->count(),
            'pending_leaves' => LeaveRequest::where('status', 'pending')->count(),
            'pending_suggestions' => EmployeeSuggestion::where('status', 'pending')->count(),
            'pending_complaints' => EmployeeComplaint::where('status', 'pending')->count(),
        ]);
    }
}
