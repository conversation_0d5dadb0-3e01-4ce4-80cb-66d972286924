<?php

namespace App\Http\Controllers;

use App\Models\EmployeePromotion;
use App\Models\User;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeePromotionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of promotions
     */
    public function index(Request $request)
    {
        $query = EmployeePromotion::with(['user', 'fromPost', 'toPost', 'approvedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('promotion_type')) {
            $query->where('promotion_type', $request->promotion_type);
        }

        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        if ($request->filled('date_from')) {
            $query->where('effective_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('effective_date', '<=', $request->date_to);
        }

        $promotions = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.promotions.index', compact('promotions', 'users'));
    }

    /**
     * Show the form for creating a new promotion
     */
    public function create()
    {
        $employees = User::with(['post'])->get();
        $posts = Post::select('id', 'name')->get();

        return view('employee-affairs.promotions.create', compact('employees', 'posts'));
    }

    /**
     * Store a newly created promotion
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'promotion_type' => 'required|in:position,salary,grade,combined',
            'from_post_id' => 'nullable|exists:posts,id',
            'to_post_id' => 'nullable|exists:posts,id',
            'current_salary' => 'required|numeric|min:0',
            'new_salary' => 'required|numeric|min:0',
            'current_grade' => 'nullable|string|max:50',
            'new_grade' => 'nullable|string|max:50',
            'performance_rating' => 'nullable|in:excellent,very_good,good,satisfactory,needs_improvement',
            'reason' => 'required|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Validate promotion logic
        $user = User::findOrFail($request->user_id);
        $validationResult = $this->validatePromotionData($request, $user);
        
        if (!$validationResult['valid']) {
            return redirect()->back()
                ->withErrors(['promotion' => $validationResult['message']])
                ->withInput();
        }

        // Calculate salary increase
        $salaryIncrease = $request->new_salary - $request->current_salary;
        $increasePercentage = $request->current_salary > 0 ? 
            ($salaryIncrease / $request->current_salary) * 100 : 0;

        EmployeePromotion::create([
            'user_id' => $request->user_id,
            'promotion_type' => $request->promotion_type,
            'from_post_id' => $request->from_post_id,
            'to_post_id' => $request->to_post_id,
            'current_salary' => $request->current_salary,
            'new_salary' => $request->new_salary,
            'salary_increase' => $salaryIncrease,
            'increase_percentage' => $increasePercentage,
            'current_grade' => $request->current_grade,
            'new_grade' => $request->new_grade,
            'performance_rating' => $request->performance_rating,
            'reason' => $request->reason,
            'approval_status' => 'pending',
            'effective_date' => $request->effective_date,
            'notes' => $request->notes,
            'created_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-promotions.index')
            ->with('success', 'تم إنشاء طلب الترقية بنجاح وهو في انتظار الموافقة');
    }

    /**
     * Display the specified promotion
     */
    public function show(EmployeePromotion $employeePromotion)
    {
        $employeePromotion->load(['user', 'fromPost', 'toPost', 'approvedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.promotions.show', compact('employeePromotion'));
    }

    /**
     * Show the form for editing the specified promotion
     */
    public function edit(EmployeePromotion $employeePromotion)
    {
        if (!$employeePromotion->canBeApproved()) {
            return redirect()->route('admin.employee-promotions.index')
                ->with('error', 'لا يمكن تعديل هذه الترقية');
        }

        $users = User::with(['post'])->get();
        $posts = Post::select('id', 'name')->get();

        return view('employee-affairs.promotions.edit', compact('employeePromotion', 'users', 'posts'));
    }

    /**
     * Update the specified promotion
     */
    public function update(Request $request, EmployeePromotion $employeePromotion)
    {
        if (!$employeePromotion->canBeApproved()) {
            return redirect()->route('admin.employee-promotions.index')
                ->with('error', 'لا يمكن تعديل هذه الترقية');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'promotion_type' => 'required|in:position,salary,grade,combined',
            'from_post_id' => 'nullable|exists:posts,id',
            'to_post_id' => 'nullable|exists:posts,id',
            'current_salary' => 'required|numeric|min:0',
            'new_salary' => 'required|numeric|min:0',
            'current_grade' => 'nullable|string|max:50',
            'new_grade' => 'nullable|string|max:50',
            'performance_rating' => 'nullable|in:excellent,very_good,good,satisfactory,needs_improvement',
            'reason' => 'required|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Validate promotion logic
        $user = User::findOrFail($request->user_id);
        $validationResult = $this->validatePromotionData($request, $user);
        
        if (!$validationResult['valid']) {
            return redirect()->back()
                ->withErrors(['promotion' => $validationResult['message']])
                ->withInput();
        }

        // Calculate salary increase
        $salaryIncrease = $request->new_salary - $request->current_salary;
        $increasePercentage = $request->current_salary > 0 ? 
            ($salaryIncrease / $request->current_salary) * 100 : 0;

        $employeePromotion->update([
            'user_id' => $request->user_id,
            'promotion_type' => $request->promotion_type,
            'from_post_id' => $request->from_post_id,
            'to_post_id' => $request->to_post_id,
            'current_salary' => $request->current_salary,
            'new_salary' => $request->new_salary,
            'salary_increase' => $salaryIncrease,
            'increase_percentage' => $increasePercentage,
            'current_grade' => $request->current_grade,
            'new_grade' => $request->new_grade,
            'performance_rating' => $request->performance_rating,
            'reason' => $request->reason,
            'effective_date' => $request->effective_date,
            'notes' => $request->notes,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-promotions.index')
            ->with('success', 'تم تحديث طلب الترقية بنجاح');
    }

    /**
     * Approve the specified promotion
     */
    public function approve(EmployeePromotion $employeePromotion)
    {
        if (!$employeePromotion->canBeApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن الموافقة على هذه الترقية');
        }

        $employeePromotion->approve();

        return redirect()->back()
            ->with('success', 'تم الموافقة على الترقية بنجاح');
    }

    /**
     * Reject the specified promotion
     */
    public function reject(EmployeePromotion $employeePromotion)
    {
        if (!$employeePromotion->canBeRejected()) {
            return redirect()->back()
                ->with('error', 'لا يمكن رفض هذه الترقية');
        }

        $employeePromotion->reject();

        return redirect()->back()
            ->with('success', 'تم رفض الترقية');
    }

    /**
     * Execute the specified promotion
     */
    public function execute(EmployeePromotion $employeePromotion)
    {
        if (!$employeePromotion->isApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن تنفيذ ترقية غير معتمدة');
        }

        if ($employeePromotion->executePromotion()) {
            return redirect()->back()
                ->with('success', 'تم تنفيذ الترقية بنجاح');
        }

        return redirect()->back()
            ->with('error', 'فشل في تنفيذ الترقية');
    }

    /**
     * Remove the specified promotion
     */
    public function destroy(EmployeePromotion $employeePromotion)
    {
        if (!$employeePromotion->isPending()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذه الترقية');
        }

        $employeePromotion->delete();

        return redirect()->route('admin.employee-promotions.index')
            ->with('success', 'تم حذف طلب الترقية بنجاح');
    }

    /**
     * Get promotions for specific user (AJAX)
     */
    public function getUserPromotions($userId)
    {
        $promotions = EmployeePromotion::where('user_id', $userId)
            ->with(['fromPost', 'toPost', 'approvedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $promotions
        ]);
    }

    /**
     * Get promotion statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeePromotion::count(),
            'pending' => EmployeePromotion::pending()->count(),
            'approved' => EmployeePromotion::approved()->count(),
            'rejected' => EmployeePromotion::rejected()->count(),
            'this_year' => EmployeePromotion::thisYear()->approved()->count(),
            'total_salary_increase' => EmployeePromotion::approved()->sum('salary_increase'),
            'average_increase_percentage' => EmployeePromotion::approved()->avg('increase_percentage'),
            'by_type' => EmployeePromotion::approved()
                ->selectRaw('promotion_type, COUNT(*) as count, AVG(increase_percentage) as avg_increase')
                ->groupBy('promotion_type')
                ->get(),
            'by_performance' => EmployeePromotion::approved()
                ->whereNotNull('performance_rating')
                ->selectRaw('performance_rating, COUNT(*) as count')
                ->groupBy('performance_rating')
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Validate promotion data
     */
    private function validatePromotionData(Request $request, User $user): array
    {
        $promotionType = $request->promotion_type;

        // Check salary increase for salary/combined promotions
        if (in_array($promotionType, ['salary', 'combined'])) {
            if ($request->new_salary <= $request->current_salary) {
                return [
                    'valid' => false,
                    'message' => 'الراتب الجديد يجب أن يكون أكبر من الراتب الحالي'
                ];
            }
        }

        // Check position change for position/combined promotions
        if (in_array($promotionType, ['position', 'combined'])) {
            if (!$request->to_post_id || $request->to_post_id == $request->from_post_id) {
                return [
                    'valid' => false,
                    'message' => 'يجب تحديد منصب جديد مختلف عن المنصب الحالي'
                ];
            }
        }

        // Check grade change for grade/combined promotions
        if (in_array($promotionType, ['grade', 'combined'])) {
            if (!$request->new_grade || $request->new_grade == $request->current_grade) {
                return [
                    'valid' => false,
                    'message' => 'يجب تحديد درجة جديدة مختلفة عن الدرجة الحالية'
                ];
            }
        }

        return ['valid' => true];
    }
}
