<?php

namespace App\Http\Controllers;

use App\Models\EmployeeComplaint;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeeComplaintController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of complaints
     */
    public function index(Request $request)
    {
        $query = EmployeeComplaint::with(['user', 'againstUser', 'investigatedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('complaint_type')) {
            $query->where('complaint_type', $request->complaint_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        if ($request->filled('is_anonymous')) {
            $query->where('is_anonymous', $request->boolean('is_anonymous'));
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        $complaints = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.complaints.index', compact('complaints', 'users'));
    }

    /**
     * Show the form for creating a new complaint
     */
    public function create()
    {
        $employees = User::select('id', 'name', 'id_number')->get();
        return view('employee-affairs.complaints.create', compact('employees'));
    }

    /**
     * Store a newly created complaint
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id',
            'complaint_type' => 'required|in:harassment,discrimination,safety,management,colleagues,work_environment,policy_violation,other',
            'against_user_id' => 'nullable|exists:users,id',
            'title' => 'required|string|max:200',
            'description' => 'required|string|max:2000',
            'incident_date' => 'nullable|date|before_or_equal:today',
            'incident_location' => 'nullable|string|max:200',
            'severity' => 'required|in:low,medium,high,critical',
            'is_anonymous' => 'boolean',
            'witnesses' => 'nullable|json',
            'evidence' => 'nullable|string|max:1000',
            'desired_outcome' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle anonymous complaints
        $userId = $request->boolean('is_anonymous') ? null : $request->user_id;

        EmployeeComplaint::create([
            'user_id' => $userId,
            'complaint_type' => $request->complaint_type,
            'against_user_id' => $request->against_user_id,
            'title' => $request->title,
            'description' => $request->description,
            'incident_date' => $request->incident_date,
            'incident_location' => $request->incident_location,
            'severity' => $request->severity,
            'status' => 'submitted',
            'is_anonymous' => $request->boolean('is_anonymous'),
            'witnesses' => $request->witnesses ? json_decode($request->witnesses, true) : null,
            'evidence' => $request->evidence,
            'desired_outcome' => $request->desired_outcome,
            'created_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-complaints.index')
            ->with('success', 'تم تقديم الشكوى بنجاح وسيتم التحقيق فيها');
    }

    /**
     * Display the specified complaint
     */
    public function show(EmployeeComplaint $employeeComplaint)
    {
        $employeeComplaint->load(['user', 'againstUser', 'investigatedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.complaints.show', compact('employeeComplaint'));
    }

    /**
     * Show the form for editing the specified complaint
     */
    public function edit(EmployeeComplaint $employeeComplaint)
    {
        if (!$employeeComplaint->canBeEdited()) {
            return redirect()->route('admin.employee-complaints.index')
                ->with('error', 'لا يمكن تعديل هذه الشكوى');
        }

        $users = User::select('id', 'name')->get();
        return view('employee-affairs.complaints.edit', compact('employeeComplaint', 'users'));
    }

    /**
     * Update the specified complaint
     */
    public function update(Request $request, EmployeeComplaint $employeeComplaint)
    {
        if (!$employeeComplaint->canBeEdited()) {
            return redirect()->route('admin.employee-complaints.index')
                ->with('error', 'لا يمكن تعديل هذه الشكوى');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id',
            'complaint_type' => 'required|in:harassment,discrimination,safety,management,colleagues,work_environment,policy_violation,other',
            'against_user_id' => 'nullable|exists:users,id',
            'title' => 'required|string|max:200',
            'description' => 'required|string|max:2000',
            'incident_date' => 'nullable|date|before_or_equal:today',
            'incident_location' => 'nullable|string|max:200',
            'severity' => 'required|in:low,medium,high,critical',
            'is_anonymous' => 'boolean',
            'witnesses' => 'nullable|json',
            'evidence' => 'nullable|string|max:1000',
            'desired_outcome' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle anonymous complaints
        $userId = $request->boolean('is_anonymous') ? null : $request->user_id;

        $employeeComplaint->update([
            'user_id' => $userId,
            'complaint_type' => $request->complaint_type,
            'against_user_id' => $request->against_user_id,
            'title' => $request->title,
            'description' => $request->description,
            'incident_date' => $request->incident_date,
            'incident_location' => $request->incident_location,
            'severity' => $request->severity,
            'is_anonymous' => $request->boolean('is_anonymous'),
            'witnesses' => $request->witnesses ? json_decode($request->witnesses, true) : null,
            'evidence' => $request->evidence,
            'desired_outcome' => $request->desired_outcome,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-complaints.index')
            ->with('success', 'تم تحديث الشكوى بنجاح');
    }

    /**
     * Start investigation for the specified complaint
     */
    public function investigate(Request $request, EmployeeComplaint $employeeComplaint)
    {
        if (!$employeeComplaint->canBeInvestigated()) {
            return redirect()->back()
                ->with('error', 'لا يمكن بدء التحقيق في هذه الشكوى');
        }

        $validator = Validator::make($request->all(), [
            'investigation_notes' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeComplaint->update([
            'status' => 'under_investigation',
            'investigation_notes' => $request->investigation_notes,
            'investigation_started_at' => now(),
            'investigated_by' => Auth::id(),
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم بدء التحقيق في الشكوى');
    }

    /**
     * Resolve the specified complaint
     */
    public function resolve(Request $request, EmployeeComplaint $employeeComplaint)
    {
        if (!$employeeComplaint->canBeResolved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حل هذه الشكوى');
        }

        $validator = Validator::make($request->all(), [
            'resolution' => 'required|in:resolved,dismissed,escalated',
            'resolution_notes' => 'required|string|max:1000',
            'action_taken' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeComplaint->update([
            'status' => $request->resolution,
            'resolution_notes' => $request->resolution_notes,
            'action_taken' => $request->action_taken,
            'resolved_at' => now(),
            'updated_by' => Auth::id()
        ]);

        $resolutionMessages = [
            'resolved' => 'تم حل الشكوى بنجاح',
            'dismissed' => 'تم رفض الشكوى',
            'escalated' => 'تم تصعيد الشكوى'
        ];

        return redirect()->back()
            ->with('success', $resolutionMessages[$request->resolution]);
    }

    /**
     * Remove the specified complaint
     */
    public function destroy(EmployeeComplaint $employeeComplaint)
    {
        if (!$employeeComplaint->canBeDeleted()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذه الشكوى');
        }

        $employeeComplaint->delete();

        return redirect()->route('admin.employee-complaints.index')
            ->with('success', 'تم حذف الشكوى بنجاح');
    }

    /**
     * Get complaints for specific user (AJAX)
     */
    public function getUserComplaints($userId)
    {
        $complaints = EmployeeComplaint::where('user_id', $userId)
            ->with(['againstUser', 'investigatedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $complaints
        ]);
    }

    /**
     * Get complaint statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeeComplaint::count(),
            'submitted' => EmployeeComplaint::submitted()->count(),
            'under_investigation' => EmployeeComplaint::underInvestigation()->count(),
            'resolved' => EmployeeComplaint::resolved()->count(),
            'dismissed' => EmployeeComplaint::dismissed()->count(),
            'escalated' => EmployeeComplaint::escalated()->count(),
            'anonymous' => EmployeeComplaint::anonymous()->count(),
            'this_month' => EmployeeComplaint::thisMonth()->count(),
            'by_type' => EmployeeComplaint::selectRaw('complaint_type, COUNT(*) as count')
                ->groupBy('complaint_type')
                ->get(),
            'by_severity' => EmployeeComplaint::selectRaw('severity, COUNT(*) as count')
                ->groupBy('severity')
                ->get(),
            'resolution_rate' => EmployeeComplaint::count() > 0 ? 
                (EmployeeComplaint::resolved()->count() / EmployeeComplaint::count()) * 100 : 0,
            'average_resolution_time' => EmployeeComplaint::resolved()
                ->whereNotNull('resolved_at')
                ->whereNotNull('investigation_started_at')
                ->selectRaw('AVG(DATEDIFF(resolved_at, investigation_started_at)) as avg_days')
                ->value('avg_days')
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
