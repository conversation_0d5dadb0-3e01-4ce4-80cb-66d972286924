<?php

namespace App\Http\Controllers;

use App\Models\EmployeeResignation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeeResignationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of resignations
     */
    public function index(Request $request)
    {
        $query = EmployeeResignation::with(['user', 'approvedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('resignation_type')) {
            $query->where('resignation_type', $request->resignation_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->where('resignation_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('resignation_date', '<=', $request->date_to);
        }

        $resignations = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.resignations.index', compact('resignations', 'users'));
    }

    /**
     * Show the form for creating a new resignation
     */
    public function create()
    {
        $employees = User::select('id', 'name', 'id_number')->get();
        return view('employee-affairs.resignations.create', compact('employees'));
    }

    /**
     * Store a newly created resignation
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'resignation_type' => 'required|in:voluntary,involuntary,retirement,termination,mutual_agreement',
            'resignation_date' => 'required|date|after_or_equal:today',
            'notice_period_days' => 'required|integer|min:0|max:365',
            'reason' => 'required|string|max:1000',
            'final_settlement' => 'nullable|numeric|min:0',
            'benefits_due' => 'nullable|numeric|min:0',
            'deductions' => 'nullable|numeric|min:0',
            'handover_required' => 'boolean',
            'exit_interview_required' => 'boolean',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Calculate last working day
        $lastWorkingDay = \Carbon\Carbon::parse($request->resignation_date)
            ->addDays($request->notice_period_days);

        EmployeeResignation::create([
            'user_id' => $request->user_id,
            'resignation_type' => $request->resignation_type,
            'resignation_date' => $request->resignation_date,
            'notice_period_days' => $request->notice_period_days,
            'last_working_day' => $lastWorkingDay,
            'reason' => $request->reason,
            'status' => 'submitted',
            'final_settlement' => $request->final_settlement,
            'benefits_due' => $request->benefits_due,
            'deductions' => $request->deductions,
            'handover_required' => $request->boolean('handover_required'),
            'exit_interview_required' => $request->boolean('exit_interview_required'),
            'notes' => $request->notes,
            'created_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-resignations.index')
            ->with('success', 'تم تقديم طلب الاستقالة بنجاح وهو في انتظار الموافقة');
    }

    /**
     * Display the specified resignation
     */
    public function show(EmployeeResignation $employeeResignation)
    {
        $employeeResignation->load(['user', 'approvedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.resignations.show', compact('employeeResignation'));
    }

    /**
     * Show the form for editing the specified resignation
     */
    public function edit(EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->canBeEdited()) {
            return redirect()->route('admin.employee-resignations.index')
                ->with('error', 'لا يمكن تعديل هذه الاستقالة');
        }

        $users = User::select('id', 'name')->get();
        return view('employee-affairs.resignations.edit', compact('employeeResignation', 'users'));
    }

    /**
     * Update the specified resignation
     */
    public function update(Request $request, EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->canBeEdited()) {
            return redirect()->route('admin.employee-resignations.index')
                ->with('error', 'لا يمكن تعديل هذه الاستقالة');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'resignation_type' => 'required|in:voluntary,involuntary,retirement,termination,mutual_agreement',
            'resignation_date' => 'required|date|after_or_equal:today',
            'notice_period_days' => 'required|integer|min:0|max:365',
            'reason' => 'required|string|max:1000',
            'final_settlement' => 'nullable|numeric|min:0',
            'benefits_due' => 'nullable|numeric|min:0',
            'deductions' => 'nullable|numeric|min:0',
            'handover_required' => 'boolean',
            'exit_interview_required' => 'boolean',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Calculate last working day
        $lastWorkingDay = \Carbon\Carbon::parse($request->resignation_date)
            ->addDays($request->notice_period_days);

        $employeeResignation->update([
            'user_id' => $request->user_id,
            'resignation_type' => $request->resignation_type,
            'resignation_date' => $request->resignation_date,
            'notice_period_days' => $request->notice_period_days,
            'last_working_day' => $lastWorkingDay,
            'reason' => $request->reason,
            'final_settlement' => $request->final_settlement,
            'benefits_due' => $request->benefits_due,
            'deductions' => $request->deductions,
            'handover_required' => $request->boolean('handover_required'),
            'exit_interview_required' => $request->boolean('exit_interview_required'),
            'notes' => $request->notes,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-resignations.index')
            ->with('success', 'تم تحديث طلب الاستقالة بنجاح');
    }

    /**
     * Approve the specified resignation
     */
    public function approve(EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->canBeApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن الموافقة على هذه الاستقالة');
        }

        $employeeResignation->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => Auth::id(),
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم الموافقة على الاستقالة بنجاح');
    }

    /**
     * Reject the specified resignation
     */
    public function reject(EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->canBeRejected()) {
            return redirect()->back()
                ->with('error', 'لا يمكن رفض هذه الاستقالة');
        }

        $employeeResignation->update([
            'status' => 'rejected',
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم رفض الاستقالة');
    }

    /**
     * Complete handover for the specified resignation
     */
    public function completeHandover(Request $request, EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->isApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن إكمال التسليم لاستقالة غير معتمدة');
        }

        $validator = Validator::make($request->all(), [
            'handover_notes' => 'required|string|max:1000',
            'handover_completed_by' => 'required|exists:users,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeResignation->update([
            'handover_completed' => true,
            'handover_notes' => $request->handover_notes,
            'handover_completed_at' => now(),
            'handover_completed_by' => $request->handover_completed_by,
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم إكمال تسليم المهام بنجاح');
    }

    /**
     * Complete exit interview for the specified resignation
     */
    public function completeExitInterview(Request $request, EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->isApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن إجراء مقابلة الخروج لاستقالة غير معتمدة');
        }

        $validator = Validator::make($request->all(), [
            'exit_interview_notes' => 'required|string|max:1000',
            'exit_interview_conducted_by' => 'required|exists:users,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeResignation->update([
            'exit_interview_completed' => true,
            'exit_interview_notes' => $request->exit_interview_notes,
            'exit_interview_completed_at' => now(),
            'exit_interview_conducted_by' => $request->exit_interview_conducted_by,
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم إكمال مقابلة الخروج بنجاح');
    }

    /**
     * Process final settlement for the specified resignation
     */
    public function processFinalSettlement(Request $request, EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->isApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن معالجة التسوية النهائية لاستقالة غير معتمدة');
        }

        $validator = Validator::make($request->all(), [
            'final_settlement' => 'required|numeric|min:0',
            'benefits_due' => 'nullable|numeric|min:0',
            'deductions' => 'nullable|numeric|min:0',
            'settlement_notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $netAmount = $request->final_settlement + ($request->benefits_due ?? 0) - ($request->deductions ?? 0);

        $employeeResignation->update([
            'final_settlement' => $request->final_settlement,
            'benefits_due' => $request->benefits_due,
            'deductions' => $request->deductions,
            'net_settlement' => $netAmount,
            'settlement_processed' => true,
            'settlement_notes' => $request->settlement_notes,
            'settlement_processed_at' => now(),
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم معالجة التسوية النهائية بنجاح');
    }

    /**
     * Remove the specified resignation
     */
    public function destroy(EmployeeResignation $employeeResignation)
    {
        if (!$employeeResignation->canBeDeleted()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذه الاستقالة');
        }

        $employeeResignation->delete();

        return redirect()->route('admin.employee-resignations.index')
            ->with('success', 'تم حذف طلب الاستقالة بنجاح');
    }

    /**
     * Get resignations for specific user (AJAX)
     */
    public function getUserResignations($userId)
    {
        $resignations = EmployeeResignation::where('user_id', $userId)
            ->with(['approvedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $resignations
        ]);
    }

    /**
     * Get resignation statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeeResignation::count(),
            'submitted' => EmployeeResignation::submitted()->count(),
            'approved' => EmployeeResignation::approved()->count(),
            'rejected' => EmployeeResignation::rejected()->count(),
            'completed' => EmployeeResignation::completed()->count(),
            'this_month' => EmployeeResignation::thisMonth()->count(),
            'this_year' => EmployeeResignation::thisYear()->count(),
            'by_type' => EmployeeResignation::selectRaw('resignation_type, COUNT(*) as count')
                ->groupBy('resignation_type')
                ->get(),
            'pending_handover' => EmployeeResignation::approved()
                ->where('handover_required', true)
                ->where('handover_completed', false)
                ->count(),
            'pending_exit_interview' => EmployeeResignation::approved()
                ->where('exit_interview_required', true)
                ->where('exit_interview_completed', false)
                ->count(),
            'pending_settlement' => EmployeeResignation::approved()
                ->where('settlement_processed', false)
                ->count(),
            'total_settlements' => EmployeeResignation::where('settlement_processed', true)
                ->sum('net_settlement'),
            'average_notice_period' => EmployeeResignation::avg('notice_period_days')
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
