<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\EmployeeSalary;
use App\Models\MonthlyPayroll;
use App\Models\EmployeeAdvance;
use App\Models\AdvanceInstallment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Exception;

class PayrollApiController extends Controller
{
    /**
     * Get employee current salary details
     */
    public function getCurrentSalary(): JsonResponse
    {
        try {
            $userId = Auth::id();
            
            $salary = EmployeeSalary::where('user_id', $userId)
                ->active()
                ->current()
                ->first();

            if (!$salary) {
                return AppHelper::sendErrorResponse('لم يتم العثور على بيانات الراتب', 404);
            }

            $data = [
                'id' => $salary->id,
                'basic_salary' => $salary->basic_salary,
                'housing_allowance' => $salary->housing_allowance,
                'transport_allowance' => $salary->transport_allowance,
                'food_allowance' => $salary->food_allowance,
                'other_allowances' => $salary->other_allowances,
                'total_allowances' => $salary->total_allowances,
                'total_salary' => $salary->total_salary,
                'effective_date' => $salary->effective_date->format('Y-m-d'),
                'formatted_total_salary' => $salary->formatted_total_salary,
                'formatted_basic_salary' => $salary->formatted_basic_salary
            ];

            return AppHelper::sendSuccessResponse('تم جلب بيانات الراتب بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee payroll history
     */
    public function getPayrollHistory(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $year = $request->get('year', now()->year);
            $month = $request->get('month');

            $query = MonthlyPayroll::where('user_id', $userId)
                ->where('payroll_year', $year);

            if ($month) {
                $query->where('payroll_month', $month);
            }

            $payrolls = $query->orderBy('payroll_year', 'desc')
                ->orderBy('payroll_month', 'desc')
                ->get();

            $data = $payrolls->map(function ($payroll) {
                return [
                    'id' => $payroll->id,
                    'year' => $payroll->payroll_year,
                    'month' => $payroll->payroll_month,
                    'month_name' => $payroll->month_name,
                    'period' => $payroll->payroll_period,
                    'basic_salary' => $payroll->basic_salary,
                    'allowances' => $payroll->allowances,
                    'overtime_amount' => $payroll->overtime_amount,
                    'bonus_amount' => $payroll->bonus_amount,
                    'deduction_amount' => $payroll->deduction_amount,
                    'advance_amount' => $payroll->advance_amount,
                    'insurance_deduction' => $payroll->insurance_deduction,
                    'tax_deduction' => $payroll->tax_deduction,
                    'gross_salary' => $payroll->gross_salary,
                    'net_salary' => $payroll->net_salary,
                    'status' => $payroll->status,
                    'status_label' => $payroll->status_label,
                    'payment_date' => $payroll->payment_date?->format('Y-m-d'),
                    'formatted_net_salary' => $payroll->formatted_net_salary,
                    'formatted_gross_salary' => $payroll->formatted_gross_salary
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب تاريخ الرواتب بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get specific payroll details
     */
    public function getPayrollDetails($id): JsonResponse
    {
        try {
            $userId = Auth::id();
            
            $payroll = MonthlyPayroll::where('user_id', $userId)
                ->where('id', $id)
                ->first();

            if (!$payroll) {
                return AppHelper::sendErrorResponse('لم يتم العثور على كشف الراتب', 404);
            }

            $data = [
                'id' => $payroll->id,
                'year' => $payroll->payroll_year,
                'month' => $payroll->payroll_month,
                'month_name' => $payroll->month_name,
                'period' => $payroll->payroll_period,
                'basic_salary' => $payroll->basic_salary,
                'allowances' => $payroll->allowances,
                'overtime_amount' => $payroll->overtime_amount,
                'bonus_amount' => $payroll->bonus_amount,
                'deduction_amount' => $payroll->deduction_amount,
                'advance_amount' => $payroll->advance_amount,
                'insurance_deduction' => $payroll->insurance_deduction,
                'tax_deduction' => $payroll->tax_deduction,
                'gross_salary' => $payroll->gross_salary,
                'net_salary' => $payroll->net_salary,
                'status' => $payroll->status,
                'status_label' => $payroll->status_label,
                'payment_date' => $payroll->payment_date?->format('Y-m-d'),
                'notes' => $payroll->notes,
                'formatted_net_salary' => $payroll->formatted_net_salary,
                'formatted_gross_salary' => $payroll->formatted_gross_salary,
                'created_at' => $payroll->created_at->format('Y-m-d H:i:s')
            ];

            return AppHelper::sendSuccessResponse('تم جلب تفاصيل كشف الراتب بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee advances
     */
    public function getAdvances(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $status = $request->get('status');

            $query = EmployeeAdvance::where('user_id', $userId);

            if ($status) {
                $query->where('status', $status);
            }

            $advances = $query->orderBy('created_at', 'desc')->get();

            $data = $advances->map(function ($advance) {
                return [
                    'id' => $advance->id,
                    'amount' => $advance->amount,
                    'reason' => $advance->reason,
                    'status' => $advance->status,
                    'status_label' => $advance->status_label,
                    'request_date' => $advance->request_date->format('Y-m-d'),
                    'approved_date' => $advance->approved_date?->format('Y-m-d'),
                    'payment_date' => $advance->payment_date?->format('Y-m-d'),
                    'installments' => $advance->installments,
                    'installment_amount' => $advance->installment_amount,
                    'paid_amount' => $advance->paid_amount,
                    'remaining_amount' => $advance->remaining_amount,
                    'payment_progress' => $advance->payment_progress,
                    'formatted_amount' => $advance->formatted_amount,
                    'formatted_remaining_amount' => $advance->formatted_remaining_amount,
                    'formatted_paid_amount' => $advance->formatted_paid_amount,
                    'approval_notes' => $advance->approval_notes,
                    'rejection_reason' => $advance->rejection_reason
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب بيانات السلف بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Request new advance
     */
    public function requestAdvance(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1|max:50000',
                'reason' => 'required|string|max:500',
                'installments' => 'required|integer|min:1|max:12'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $userId = Auth::id();

            // التحقق من وجود سلف نشطة
            $activeAdvance = EmployeeAdvance::where('user_id', $userId)
                ->whereIn('status', ['pending', 'approved', 'paid'])
                ->exists();

            if ($activeAdvance) {
                return AppHelper::sendErrorResponse('لديك سلفة نشطة بالفعل', 422);
            }

            $advance = EmployeeAdvance::create([
                'user_id' => $userId,
                'amount' => $request->amount,
                'reason' => $request->reason,
                'installments' => $request->installments,
                'request_date' => now()->toDateString()
            ]);

            return AppHelper::sendSuccessResponse('تم تقديم طلب السلفة بنجاح', [
                'id' => $advance->id,
                'amount' => $advance->amount,
                'installments' => $advance->installments,
                'installment_amount' => $advance->installment_amount,
                'status' => $advance->status,
                'status_label' => $advance->status_label
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }
}
