<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\EmployeeBonus;
use App\Models\EmployeeDeduction;
use App\Models\EmployeeTransfer;
use App\Models\EmployeePromotion;
use App\Models\EmployeeSuggestion;
use App\Models\EmployeeComplaint;
use App\Models\EmployeeWarning;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Exception;

class EmployeeAffairsApiController extends Controller
{
    /**
     * Get employee bonuses
     */
    public function getBonuses(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $status = $request->get('status');
            $type = $request->get('type');

            $query = EmployeeBonus::where('user_id', $userId);

            if ($status) {
                $query->where('approval_status', $status);
            }

            if ($type) {
                $query->where('bonus_type', $type);
            }

            $bonuses = $query->orderBy('created_at', 'desc')->get();

            $data = $bonuses->map(function ($bonus) {
                return [
                    'id' => $bonus->id,
                    'bonus_type' => $bonus->bonus_type,
                    'bonus_type_label' => $bonus->bonus_type_label,
                    'bonus_category' => $bonus->bonus_category,
                    'bonus_category_label' => $bonus->bonus_category_label,
                    'amount' => $bonus->amount,
                    'description' => $bonus->description,
                    'reason' => $bonus->reason,
                    'approval_status' => $bonus->approval_status,
                    'approval_status_label' => $bonus->approval_status_label,
                    'effective_date' => $bonus->effective_date->format('Y-m-d'),
                    'payment_date' => $bonus->payment_date?->format('Y-m-d'),
                    'notes' => $bonus->notes,
                    'formatted_amount' => $bonus->formatted_amount,
                    'approved_at' => $bonus->approved_at?->format('Y-m-d H:i:s'),
                    'created_at' => $bonus->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب بيانات المكافآت بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee deductions
     */
    public function getDeductions(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $status = $request->get('status');
            $type = $request->get('type');

            $query = EmployeeDeduction::where('user_id', $userId);

            if ($status) {
                $query->where('approval_status', $status);
            }

            if ($type) {
                $query->where('deduction_type', $type);
            }

            $deductions = $query->orderBy('created_at', 'desc')->get();

            $data = $deductions->map(function ($deduction) {
                return [
                    'id' => $deduction->id,
                    'deduction_type' => $deduction->deduction_type,
                    'deduction_type_label' => $deduction->deduction_type_label,
                    'amount' => $deduction->amount,
                    'description' => $deduction->description,
                    'reason' => $deduction->reason,
                    'approval_status' => $deduction->approval_status,
                    'approval_status_label' => $deduction->approval_status_label,
                    'effective_date' => $deduction->effective_date->format('Y-m-d'),
                    'deduction_date' => $deduction->deduction_date?->format('Y-m-d'),
                    'notes' => $deduction->notes,
                    'formatted_amount' => $deduction->formatted_amount,
                    'approved_at' => $deduction->approved_at?->format('Y-m-d H:i:s'),
                    'created_at' => $deduction->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب بيانات الخصومات بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee transfers
     */
    public function getTransfers(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $transfers = EmployeeTransfer::where('user_id', $userId)
                ->with(['fromBranch', 'toBranch', 'fromDepartment', 'toDepartment'])
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $transfers->map(function ($transfer) {
                return [
                    'id' => $transfer->id,
                    'transfer_type' => $transfer->transfer_type,
                    'transfer_type_label' => $transfer->transfer_type_label,
                    'from_branch' => $transfer->fromBranch?->name,
                    'to_branch' => $transfer->toBranch?->name,
                    'from_department' => $transfer->fromDepartment?->dept_name,
                    'to_department' => $transfer->toDepartment?->dept_name,
                    'reason' => $transfer->reason,
                    'approval_status' => $transfer->approval_status,
                    'approval_status_label' => $transfer->approval_status_label,
                    'effective_date' => $transfer->effective_date->format('Y-m-d'),
                    'notes' => $transfer->notes,
                    'approved_at' => $transfer->approved_at?->format('Y-m-d H:i:s'),
                    'created_at' => $transfer->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب بيانات النقل بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee promotions
     */
    public function getPromotions(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $promotions = EmployeePromotion::where('user_id', $userId)
                ->with(['fromPost', 'toPost'])
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $promotions->map(function ($promotion) {
                return [
                    'id' => $promotion->id,
                    'promotion_type' => $promotion->promotion_type,
                    'promotion_type_label' => $promotion->promotion_type_label,
                    'from_post' => $promotion->fromPost?->post_name,
                    'to_post' => $promotion->toPost?->post_name,
                    'old_salary' => $promotion->old_salary,
                    'new_salary' => $promotion->new_salary,
                    'salary_increase' => $promotion->salary_increase,
                    'reason' => $promotion->reason,
                    'approval_status' => $promotion->approval_status,
                    'approval_status_label' => $promotion->approval_status_label,
                    'effective_date' => $promotion->effective_date->format('Y-m-d'),
                    'notes' => $promotion->notes,
                    'formatted_old_salary' => $promotion->formatted_old_salary,
                    'formatted_new_salary' => $promotion->formatted_new_salary,
                    'formatted_salary_increase' => $promotion->formatted_salary_increase,
                    'approved_at' => $promotion->approved_at?->format('Y-m-d H:i:s'),
                    'created_at' => $promotion->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب بيانات الترقيات بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Submit employee suggestion
     */
    public function submitSuggestion(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string|max:1000',
                'category' => 'required|in:process_improvement,cost_reduction,quality_improvement,safety,technology,other',
                'expected_benefit' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $suggestion = EmployeeSuggestion::create([
                'user_id' => Auth::id(),
                'title' => $request->title,
                'description' => $request->description,
                'category' => $request->category,
                'expected_benefit' => $request->expected_benefit,
                'submission_date' => now()->toDateString()
            ]);

            return AppHelper::sendSuccessResponse('تم تقديم الاقتراح بنجاح', [
                'id' => $suggestion->id,
                'title' => $suggestion->title,
                'status' => $suggestion->status,
                'status_label' => $suggestion->status_label,
                'submission_date' => $suggestion->submission_date->format('Y-m-d')
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Submit employee complaint
     */
    public function submitComplaint(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string|max:1000',
                'category' => 'required|in:workplace_harassment,discrimination,safety_violation,policy_violation,management_issue,other',
                'priority' => 'required|in:low,medium,high,urgent'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $complaint = EmployeeComplaint::create([
                'user_id' => Auth::id(),
                'title' => $request->title,
                'description' => $request->description,
                'category' => $request->category,
                'priority' => $request->priority,
                'submission_date' => now()->toDateString()
            ]);

            return AppHelper::sendSuccessResponse('تم تقديم الشكوى بنجاح', [
                'id' => $complaint->id,
                'title' => $complaint->title,
                'status' => $complaint->status,
                'status_label' => $complaint->status_label,
                'priority' => $complaint->priority,
                'priority_label' => $complaint->priority_label,
                'submission_date' => $complaint->submission_date->format('Y-m-d')
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee suggestions
     */
    public function getSuggestions(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $suggestions = EmployeeSuggestion::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $suggestions->map(function ($suggestion) {
                return [
                    'id' => $suggestion->id,
                    'title' => $suggestion->title,
                    'description' => $suggestion->description,
                    'category' => $suggestion->category,
                    'category_label' => $suggestion->category_label,
                    'expected_benefit' => $suggestion->expected_benefit,
                    'status' => $suggestion->status,
                    'status_label' => $suggestion->status_label,
                    'submission_date' => $suggestion->submission_date->format('Y-m-d'),
                    'review_date' => $suggestion->review_date?->format('Y-m-d'),
                    'implementation_date' => $suggestion->implementation_date?->format('Y-m-d'),
                    'feedback' => $suggestion->feedback,
                    'created_at' => $suggestion->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب الاقتراحات بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee complaints
     */
    public function getComplaints(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $complaints = EmployeeComplaint::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $complaints->map(function ($complaint) {
                return [
                    'id' => $complaint->id,
                    'title' => $complaint->title,
                    'description' => $complaint->description,
                    'category' => $complaint->category,
                    'category_label' => $complaint->category_label,
                    'priority' => $complaint->priority,
                    'priority_label' => $complaint->priority_label,
                    'status' => $complaint->status,
                    'status_label' => $complaint->status_label,
                    'submission_date' => $complaint->submission_date->format('Y-m-d'),
                    'investigation_date' => $complaint->investigation_date?->format('Y-m-d'),
                    'resolution_date' => $complaint->resolution_date?->format('Y-m-d'),
                    'resolution' => $complaint->resolution,
                    'created_at' => $complaint->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب الشكاوى بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee warnings
     */
    public function getWarnings(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $warnings = EmployeeWarning::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $warnings->map(function ($warning) {
                return [
                    'id' => $warning->id,
                    'warning_type' => $warning->warning_type,
                    'warning_type_label' => $warning->warning_type_label,
                    'severity' => $warning->severity,
                    'severity_label' => $warning->severity_label,
                    'title' => $warning->title,
                    'description' => $warning->description,
                    'incident_date' => $warning->incident_date->format('Y-m-d'),
                    'warning_date' => $warning->warning_date->format('Y-m-d'),
                    'expiry_date' => $warning->expiry_date?->format('Y-m-d'),
                    'is_active' => $warning->is_active,
                    'created_at' => $warning->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب الإنذارات بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }
}
