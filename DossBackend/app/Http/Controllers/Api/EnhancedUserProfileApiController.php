<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\EmployeeDocument;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Exception;

class EnhancedUserProfileApiController extends Controller
{
    /**
     * Get user profile
     */
    public function getProfile(): JsonResponse
    {
        try {
            $user = Auth::user();
            $user->load(['company', 'branch', 'department', 'post', 'role', 'supervisor', 'officeTime']);

            $data = [
                'personal_info' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'full_name_en' => $user->full_name_en,
                    'email' => $user->email,
                    'username' => $user->username,
                    'phone' => $user->phone,
                    'emergency_contact_number' => $user->emergency_contact_number,
                    'address' => $user->address,
                    'dob' => $user->dob?->format('Y-m-d'),
                    'age' => $user->dob ? now()->diffInYears($user->dob) : null,
                    'gender' => $user->gender,
                    'marital_status' => $user->marital_status,
                    'id_number' => $user->id_number,
                    'avatar' => $user->avatar ? asset($user->avatar) : null
                ],
                'employment_info' => [
                    'employee_id' => $user->id,
                    'joining_date' => $user->joining_date?->format('Y-m-d'),
                    'employment_type' => $user->employment_type,
                    'user_type' => $user->user_type,
                    'workspace_type' => $user->workspace_type,
                    'status' => $user->status,
                    'is_active' => $user->is_active,
                    'company' => $user->company?->name,
                    'branch' => $user->branch?->name,
                    'department' => $user->department?->dept_name,
                    'post' => $user->post?->post_name,
                    'role' => $user->role?->name,
                    'supervisor' => $user->supervisor?->name,
                    'office_time' => $user->officeTime ? [
                        'shift' => $user->officeTime->shift,
                        'opening_time' => $user->officeTime->opening_time,
                        'closing_time' => $user->officeTime->closing_time
                    ] : null
                ],
                'financial_info' => [
                    'bank_account_number' => $user->bank_account_number,
                    'bank_branch_code' => $user->bank_branch_code,
                    'insurance_salary' => $user->insurance_salary,
                    'formatted_insurance_salary' => $user->insurance_salary ? number_format($user->insurance_salary, 2) . ' ريال' : null
                ],
                'insurance_info' => [
                    'insurance_type' => $user->insurance_type,
                    'medical_insurance_category' => $user->medical_insurance_category,
                    'insurance_job_title' => $user->insurance_job_title,
                    'insurance_start_date' => $user->insurance_start_date?->format('Y-m-d'),
                    'insurance_institution' => $user->insuranceInstitution?->name
                ],
                'system_info' => [
                    'fingerprint_code' => $user->fingerprint_code,
                    'be_connect_code' => $user->be_connect_code,
                    'be_connect_client_code' => $user->be_connect_client_code,
                    'device_type' => $user->device_type,
                    'online_status' => $user->online_status,
                    'last_login' => $user->updated_at->format('Y-m-d H:i:s')
                ]
            ];

            return AppHelper::sendSuccessResponse('تم جلب الملف الشخصي بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Update profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'phone' => 'sometimes|string|max:20',
                'emergency_contact_number' => 'sometimes|string|max:20',
                'address' => 'sometimes|string|max:500',
                'marital_status' => 'sometimes|in:single,married',
                'avatar' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $updateData = $request->only([
                'name', 'phone', 'emergency_contact_number', 'address', 'marital_status'
            ]);

            // رفع الصورة الشخصية
            if ($request->hasFile('avatar')) {
                // حذف الصورة القديمة
                if ($user->avatar && Storage::exists($user->avatar)) {
                    Storage::delete($user->avatar);
                }

                $avatarPath = $request->file('avatar')->store('uploads/user/avatar', 'public');
                $updateData['avatar'] = $avatarPath;
            }

            $user->update($updateData);

            return AppHelper::sendSuccessResponse('تم تحديث الملف الشخصي بنجاح', [
                'name' => $user->name,
                'phone' => $user->phone,
                'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Change password
     */
    public function changePassword(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'current_password' => 'required|string',
                'new_password' => 'required|string|min:8|confirmed',
                'new_password_confirmation' => 'required|string'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $user = Auth::user();

            // التحقق من كلمة المرور الحالية
            if (!Hash::check($request->current_password, $user->password)) {
                return AppHelper::sendErrorResponse('كلمة المرور الحالية غير صحيحة', 422);
            }

            // تحديث كلمة المرور
            $user->update([
                'password' => Hash::make($request->new_password)
            ]);

            return AppHelper::sendSuccessResponse('تم تغيير كلمة المرور بنجاح');

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee documents
     */
    public function getDocuments(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $documents = EmployeeDocument::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $documents->map(function ($document) {
                return [
                    'id' => $document->id,
                    'document_type' => $document->document_type,
                    'document_type_label' => $this->getDocumentTypeLabel($document->document_type),
                    'document_name' => $document->document_name,
                    'file_path' => $document->file_path ? asset('storage/' . $document->file_path) : null,
                    'file_size' => $document->file_size,
                    'file_extension' => $document->file_extension,
                    'upload_date' => $document->upload_date->format('Y-m-d'),
                    'expiry_date' => $document->expiry_date?->format('Y-m-d'),
                    'is_expired' => $document->expiry_date ? $document->expiry_date < now() : false,
                    'notes' => $document->notes,
                    'is_verified' => $document->is_verified,
                    'created_at' => $document->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب المستندات بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Upload document
     */
    public function uploadDocument(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'document_type' => 'required|in:id_copy,passport,contract,certificate,medical_report,other',
                'document_name' => 'required|string|max:255',
                'file' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120', // 5MB
                'expiry_date' => 'nullable|date|after:today',
                'notes' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $userId = Auth::id();
            $file = $request->file('file');

            // رفع الملف
            $filePath = $file->store('uploads/employee/documents/' . $userId, 'public');

            $document = EmployeeDocument::create([
                'user_id' => $userId,
                'document_type' => $request->document_type,
                'document_name' => $request->document_name,
                'file_path' => $filePath,
                'file_size' => $file->getSize(),
                'file_extension' => $file->getClientOriginalExtension(),
                'upload_date' => now(),
                'expiry_date' => $request->expiry_date,
                'notes' => $request->notes
            ]);

            return AppHelper::sendSuccessResponse('تم رفع المستند بنجاح', [
                'id' => $document->id,
                'document_name' => $document->document_name,
                'document_type_label' => $this->getDocumentTypeLabel($document->document_type),
                'file_url' => asset('storage/' . $document->file_path)
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Delete document
     */
    public function deleteDocument($id): JsonResponse
    {
        try {
            $userId = Auth::id();

            $document = EmployeeDocument::where('user_id', $userId)
                ->where('id', $id)
                ->first();

            if (!$document) {
                return AppHelper::sendErrorResponse('لم يتم العثور على المستند', 404);
            }

            // حذف الملف من التخزين
            if ($document->file_path && Storage::disk('public')->exists($document->file_path)) {
                Storage::disk('public')->delete($document->file_path);
            }

            $document->delete();

            return AppHelper::sendSuccessResponse('تم حذف المستند بنجاح');

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get profile summary
     */
    public function getProfileSummary(): JsonResponse
    {
        try {
            $user = Auth::user();

            // حساب سنوات الخبرة
            $experienceYears = $user->joining_date ? 
                now()->diffInYears($user->joining_date) : 0;

            // عدد المستندات
            $documentsCount = EmployeeDocument::where('user_id', $user->id)->count();

            // المستندات المنتهية الصلاحية
            $expiredDocuments = EmployeeDocument::where('user_id', $user->id)
                ->where('expiry_date', '<', now())
                ->count();

            $data = [
                'employee_id' => $user->id,
                'name' => $user->name,
                'position' => $user->post?->post_name,
                'department' => $user->department?->dept_name,
                'branch' => $user->branch?->name,
                'joining_date' => $user->joining_date?->format('Y-m-d'),
                'experience_years' => $experienceYears,
                'employment_type' => $user->employment_type,
                'status' => $user->status,
                'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null,
                'documents_count' => $documentsCount,
                'expired_documents_count' => $expiredDocuments,
                'profile_completion' => $this->calculateProfileCompletion($user)
            ];

            return AppHelper::sendSuccessResponse('تم جلب ملخص الملف الشخصي بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    // Helper methods
    private function getDocumentTypeLabel($type): string
    {
        $types = [
            'id_copy' => 'صورة الهوية',
            'passport' => 'جواز السفر',
            'contract' => 'عقد العمل',
            'certificate' => 'شهادة',
            'medical_report' => 'تقرير طبي',
            'other' => 'أخرى'
        ];

        return $types[$type] ?? $type;
    }

    private function calculateProfileCompletion($user): int
    {
        $fields = [
            'name', 'email', 'phone', 'address', 'dob', 'gender',
            'joining_date', 'employment_type', 'company_id', 'branch_id',
            'department_id', 'post_id', 'emergency_contact_number'
        ];

        $completedFields = 0;
        foreach ($fields as $field) {
            if (!empty($user->$field)) {
                $completedFields++;
            }
        }

        return round(($completedFields / count($fields)) * 100);
    }
}
