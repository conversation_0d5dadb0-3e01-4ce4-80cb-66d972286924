<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\Attendance;
use App\Models\OfficeTime;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Carbon;
use Exception;

class EnhancedAttendanceApiController extends Controller
{
    /**
     * Get today's attendance status
     */
    public function getTodayAttendance(): JsonResponse
    {
        try {
            $userId = Auth::id();
            $today = now()->toDateString();

            $attendance = Attendance::where('user_id', $userId)
                ->where('attendance_date', $today)
                ->first();

            $user = Auth::user();
            $officeTime = OfficeTime::find($user->office_time_id);

            $data = [
                'has_checked_in' => $attendance && $attendance->check_in_at,
                'has_checked_out' => $attendance && $attendance->check_out_at,
                'check_in_time' => $attendance?->check_in_at,
                'check_out_time' => $attendance?->check_out_at,
                'attendance_date' => $today,
                'working_hours' => $this->calculateWorkingHours($attendance),
                'status' => $this->getAttendanceStatus($attendance, $officeTime),
                'office_hours' => $officeTime ? [
                    'opening_time' => $officeTime->opening_time,
                    'closing_time' => $officeTime->closing_time,
                    'shift_name' => $officeTime->shift
                ] : null,
                'late_arrival' => $this->calculateLateArrival($attendance, $officeTime),
                'early_departure' => $this->calculateEarlyDeparture($attendance, $officeTime),
                'overtime' => $this->calculateOvertime($attendance, $officeTime)
            ];

            return AppHelper::sendSuccessResponse('تم جلب بيانات الحضور اليوم بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get attendance history
     */
    public function getAttendanceHistory(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $month = $request->get('month', now()->month);
            $year = $request->get('year', now()->year);

            $attendances = Attendance::where('user_id', $userId)
                ->whereYear('attendance_date', $year)
                ->whereMonth('attendance_date', $month)
                ->orderBy('attendance_date', 'desc')
                ->get();

            $user = Auth::user();
            $officeTime = OfficeTime::find($user->office_time_id);

            $data = $attendances->map(function ($attendance) use ($officeTime) {
                return [
                    'id' => $attendance->id,
                    'date' => $attendance->attendance_date,
                    'day_name' => Carbon::parse($attendance->attendance_date)->locale('ar')->dayName,
                    'check_in_time' => $attendance->check_in_at,
                    'check_out_time' => $attendance->check_out_at,
                    'working_hours' => $this->calculateWorkingHours($attendance),
                    'status' => $this->getAttendanceStatus($attendance, $officeTime),
                    'late_arrival' => $this->calculateLateArrival($attendance, $officeTime),
                    'early_departure' => $this->calculateEarlyDeparture($attendance, $officeTime),
                    'overtime' => $this->calculateOvertime($attendance, $officeTime),
                    'note' => $attendance->note,
                    'location' => [
                        'check_in_lat' => $attendance->check_in_latitude,
                        'check_in_lng' => $attendance->check_in_longitude,
                        'check_out_lat' => $attendance->check_out_latitude,
                        'check_out_lng' => $attendance->check_out_longitude
                    ]
                ];
            });

            // إحصائيات الشهر
            $stats = $this->calculateMonthlyStats($attendances, $officeTime);

            return AppHelper::sendSuccessResponse('تم جلب تاريخ الحضور بنجاح', [
                'attendances' => $data,
                'monthly_stats' => $stats
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Check in
     */
    public function checkIn(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'latitude' => 'nullable|numeric',
                'longitude' => 'nullable|numeric',
                'note' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $userId = Auth::id();
            $today = now()->toDateString();
            $currentTime = now()->format('H:i:s');

            // التحقق من وجود تسجيل دخول اليوم
            $existingAttendance = Attendance::where('user_id', $userId)
                ->where('attendance_date', $today)
                ->first();

            if ($existingAttendance && $existingAttendance->check_in_at) {
                return AppHelper::sendErrorResponse('تم تسجيل الدخول مسبقاً اليوم', 422);
            }

            $user = Auth::user();

            if ($existingAttendance) {
                // تحديث السجل الموجود
                $existingAttendance->update([
                    'check_in_at' => $currentTime,
                    'check_in_latitude' => $request->latitude,
                    'check_in_longitude' => $request->longitude,
                    'note' => $request->note
                ]);
                $attendance = $existingAttendance;
            } else {
                // إنشاء سجل جديد
                $attendance = Attendance::create([
                    'user_id' => $userId,
                    'company_id' => $user->company_id,
                    'attendance_date' => $today,
                    'check_in_at' => $currentTime,
                    'check_in_latitude' => $request->latitude,
                    'check_in_longitude' => $request->longitude,
                    'note' => $request->note,
                    'created_by' => $userId
                ]);
            }

            $officeTime = OfficeTime::find($user->office_time_id);
            $lateArrival = $this->calculateLateArrival($attendance, $officeTime);

            return AppHelper::sendSuccessResponse('تم تسجيل الدخول بنجاح', [
                'check_in_time' => $attendance->check_in_at,
                'attendance_date' => $attendance->attendance_date,
                'late_arrival' => $lateArrival,
                'status' => $lateArrival > 0 ? 'متأخر' : 'في الوقت المحدد'
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Check out
     */
    public function checkOut(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'latitude' => 'nullable|numeric',
                'longitude' => 'nullable|numeric',
                'note' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $userId = Auth::id();
            $today = now()->toDateString();
            $currentTime = now()->format('H:i:s');

            $attendance = Attendance::where('user_id', $userId)
                ->where('attendance_date', $today)
                ->first();

            if (!$attendance || !$attendance->check_in_at) {
                return AppHelper::sendErrorResponse('يجب تسجيل الدخول أولاً', 422);
            }

            if ($attendance->check_out_at) {
                return AppHelper::sendErrorResponse('تم تسجيل الخروج مسبقاً اليوم', 422);
            }

            $attendance->update([
                'check_out_at' => $currentTime,
                'check_out_latitude' => $request->latitude,
                'check_out_longitude' => $request->longitude,
                'note' => $request->note ? $attendance->note . ' | ' . $request->note : $attendance->note
            ]);

            $user = Auth::user();
            $officeTime = OfficeTime::find($user->office_time_id);
            $workingHours = $this->calculateWorkingHours($attendance);
            $earlyDeparture = $this->calculateEarlyDeparture($attendance, $officeTime);
            $overtime = $this->calculateOvertime($attendance, $officeTime);

            return AppHelper::sendSuccessResponse('تم تسجيل الخروج بنجاح', [
                'check_out_time' => $attendance->check_out_at,
                'working_hours' => $workingHours,
                'early_departure' => $earlyDeparture,
                'overtime' => $overtime,
                'status' => $this->getAttendanceStatus($attendance, $officeTime)
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get attendance summary
     */
    public function getAttendanceSummary(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $month = $request->get('month', now()->month);
            $year = $request->get('year', now()->year);

            $attendances = Attendance::where('user_id', $userId)
                ->whereYear('attendance_date', $year)
                ->whereMonth('attendance_date', $month)
                ->get();

            $user = Auth::user();
            $officeTime = OfficeTime::find($user->office_time_id);

            $totalDays = $attendances->count();
            $presentDays = $attendances->where('check_in_at', '!=', null)->count();
            $absentDays = $totalDays > 0 ? $totalDays - $presentDays : 0;
            
            $lateDays = 0;
            $totalWorkingHours = 0;
            $totalOvertime = 0;
            $totalLateMinutes = 0;

            foreach ($attendances as $attendance) {
                if ($attendance->check_in_at) {
                    $lateMinutes = $this->calculateLateArrival($attendance, $officeTime);
                    if ($lateMinutes > 0) {
                        $lateDays++;
                        $totalLateMinutes += $lateMinutes;
                    }
                    
                    $workingHours = $this->calculateWorkingHours($attendance);
                    $totalWorkingHours += $workingHours;
                    
                    $overtime = $this->calculateOvertime($attendance, $officeTime);
                    $totalOvertime += $overtime;
                }
            }

            $data = [
                'month' => $month,
                'year' => $year,
                'total_days' => $totalDays,
                'present_days' => $presentDays,
                'absent_days' => $absentDays,
                'late_days' => $lateDays,
                'attendance_rate' => $totalDays > 0 ? round(($presentDays / $totalDays) * 100, 2) : 0,
                'total_working_hours' => round($totalWorkingHours, 2),
                'total_overtime_hours' => round($totalOvertime / 60, 2),
                'average_daily_hours' => $presentDays > 0 ? round($totalWorkingHours / $presentDays, 2) : 0,
                'total_late_minutes' => $totalLateMinutes,
                'average_late_minutes' => $lateDays > 0 ? round($totalLateMinutes / $lateDays, 2) : 0
            ];

            return AppHelper::sendSuccessResponse('تم جلب ملخص الحضور بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    // Helper methods
    private function calculateWorkingHours($attendance): float
    {
        if (!$attendance || !$attendance->check_in_at || !$attendance->check_out_at) {
            return 0;
        }

        $checkIn = Carbon::parse($attendance->attendance_date . ' ' . $attendance->check_in_at);
        $checkOut = Carbon::parse($attendance->attendance_date . ' ' . $attendance->check_out_at);

        return $checkOut->diffInMinutes($checkIn) / 60;
    }

    private function calculateLateArrival($attendance, $officeTime): int
    {
        if (!$attendance || !$attendance->check_in_at || !$officeTime) {
            return 0;
        }

        $checkInTime = Carbon::parse($attendance->check_in_at);
        $openingTime = Carbon::parse($officeTime->opening_time);

        return $checkInTime->gt($openingTime) ? $checkInTime->diffInMinutes($openingTime) : 0;
    }

    private function calculateEarlyDeparture($attendance, $officeTime): int
    {
        if (!$attendance || !$attendance->check_out_at || !$officeTime) {
            return 0;
        }

        $checkOutTime = Carbon::parse($attendance->check_out_at);
        $closingTime = Carbon::parse($officeTime->closing_time);

        return $checkOutTime->lt($closingTime) ? $closingTime->diffInMinutes($checkOutTime) : 0;
    }

    private function calculateOvertime($attendance, $officeTime): int
    {
        if (!$attendance || !$attendance->check_out_at || !$officeTime) {
            return 0;
        }

        $checkOutTime = Carbon::parse($attendance->check_out_at);
        $closingTime = Carbon::parse($officeTime->closing_time);

        return $checkOutTime->gt($closingTime) ? $checkOutTime->diffInMinutes($closingTime) : 0;
    }

    private function getAttendanceStatus($attendance, $officeTime): string
    {
        if (!$attendance || !$attendance->check_in_at) {
            return 'غائب';
        }

        if (!$attendance->check_out_at) {
            return 'حاضر';
        }

        $lateArrival = $this->calculateLateArrival($attendance, $officeTime);
        $earlyDeparture = $this->calculateEarlyDeparture($attendance, $officeTime);

        if ($lateArrival > 0 && $earlyDeparture > 0) {
            return 'متأخر ومبكر في المغادرة';
        } elseif ($lateArrival > 0) {
            return 'متأخر';
        } elseif ($earlyDeparture > 0) {
            return 'مبكر في المغادرة';
        }

        return 'حضور منتظم';
    }

    private function calculateMonthlyStats($attendances, $officeTime): array
    {
        $totalDays = $attendances->count();
        $presentDays = $attendances->where('check_in_at', '!=', null)->count();
        $completeDays = $attendances->where('check_out_at', '!=', null)->count();

        return [
            'total_days' => $totalDays,
            'present_days' => $presentDays,
            'complete_days' => $completeDays,
            'absent_days' => $totalDays - $presentDays,
            'incomplete_days' => $presentDays - $completeDays,
            'attendance_rate' => $totalDays > 0 ? round(($presentDays / $totalDays) * 100, 2) : 0
        ];
    }
}
