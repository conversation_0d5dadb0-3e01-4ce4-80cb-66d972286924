<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\InsuranceInstitution;
use App\Models\UserDependent;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Exception;

class InsuranceApiController extends Controller
{
    /**
     * Get employee insurance details
     */
    public function getInsuranceDetails(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $insuranceInstitution = null;
            if ($user->insurance_institution_id) {
                $insuranceInstitution = InsuranceInstitution::find($user->insurance_institution_id);
            }

            $data = [
                'id_number' => $user->id_number,
                'insurance_type' => $user->insurance_type,
                'medical_insurance_category' => $user->medical_insurance_category,
                'insurance_job_title' => $user->insurance_job_title,
                'insurance_start_date' => $user->insurance_start_date?->format('Y-m-d'),
                'insurance_salary' => $user->insurance_salary,
                'formatted_insurance_salary' => $user->insurance_salary ? number_format($user->insurance_salary, 2) . ' ريال' : null,
                'insurance_institution' => $insuranceInstitution ? [
                    'id' => $insuranceInstitution->id,
                    'name' => $insuranceInstitution->name,
                    'code' => $insuranceInstitution->code,
                    'type' => $insuranceInstitution->type,
                    'contact_info' => $insuranceInstitution->contact_info
                ] : null,
                'insurance_labels' => [
                    'insurance_type' => $this->getInsuranceTypeLabel($user->insurance_type),
                    'medical_insurance_category' => $this->getMedicalInsuranceCategoryLabel($user->medical_insurance_category)
                ]
            ];

            return AppHelper::sendSuccessResponse('تم جلب بيانات التأمين بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get employee dependents
     */
    public function getDependents(): JsonResponse
    {
        try {
            $userId = Auth::id();

            $dependents = UserDependent::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->get();

            $data = $dependents->map(function ($dependent) {
                return [
                    'id' => $dependent->id,
                    'name' => $dependent->name,
                    'relationship' => $dependent->relationship,
                    'relationship_label' => $this->getRelationshipLabel($dependent->relationship),
                    'date_of_birth' => $dependent->date_of_birth?->format('Y-m-d'),
                    'age' => $dependent->date_of_birth ? now()->diffInYears($dependent->date_of_birth) : null,
                    'id_number' => $dependent->id_number,
                    'is_insured' => $dependent->is_insured,
                    'insurance_start_date' => $dependent->insurance_start_date?->format('Y-m-d'),
                    'insurance_end_date' => $dependent->insurance_end_date?->format('Y-m-d'),
                    'is_active' => $dependent->is_active,
                    'notes' => $dependent->notes,
                    'created_at' => $dependent->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب بيانات المعالين بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Add new dependent
     */
    public function addDependent(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'relationship' => 'required|in:spouse,child,parent,sibling,other',
                'date_of_birth' => 'required|date|before:today',
                'id_number' => 'nullable|string|max:20',
                'is_insured' => 'boolean',
                'insurance_start_date' => 'nullable|date',
                'insurance_end_date' => 'nullable|date|after:insurance_start_date',
                'notes' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $dependent = UserDependent::create([
                'user_id' => Auth::id(),
                'name' => $request->name,
                'relationship' => $request->relationship,
                'date_of_birth' => $request->date_of_birth,
                'id_number' => $request->id_number,
                'is_insured' => $request->boolean('is_insured'),
                'insurance_start_date' => $request->insurance_start_date,
                'insurance_end_date' => $request->insurance_end_date,
                'notes' => $request->notes
            ]);

            return AppHelper::sendSuccessResponse('تم إضافة المعال بنجاح', [
                'id' => $dependent->id,
                'name' => $dependent->name,
                'relationship' => $dependent->relationship,
                'relationship_label' => $this->getRelationshipLabel($dependent->relationship),
                'is_insured' => $dependent->is_insured
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Update dependent
     */
    public function updateDependent(Request $request, $id): JsonResponse
    {
        try {
            $dependent = UserDependent::where('user_id', Auth::id())
                ->where('id', $id)
                ->first();

            if (!$dependent) {
                return AppHelper::sendErrorResponse('لم يتم العثور على المعال', 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'relationship' => 'required|in:spouse,child,parent,sibling,other',
                'date_of_birth' => 'required|date|before:today',
                'id_number' => 'nullable|string|max:20',
                'is_insured' => 'boolean',
                'insurance_start_date' => 'nullable|date',
                'insurance_end_date' => 'nullable|date|after:insurance_start_date',
                'notes' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $dependent->update([
                'name' => $request->name,
                'relationship' => $request->relationship,
                'date_of_birth' => $request->date_of_birth,
                'id_number' => $request->id_number,
                'is_insured' => $request->boolean('is_insured'),
                'insurance_start_date' => $request->insurance_start_date,
                'insurance_end_date' => $request->insurance_end_date,
                'notes' => $request->notes
            ]);

            return AppHelper::sendSuccessResponse('تم تحديث بيانات المعال بنجاح', [
                'id' => $dependent->id,
                'name' => $dependent->name,
                'relationship' => $dependent->relationship,
                'relationship_label' => $this->getRelationshipLabel($dependent->relationship),
                'is_insured' => $dependent->is_insured
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Delete dependent
     */
    public function deleteDependent($id): JsonResponse
    {
        try {
            $dependent = UserDependent::where('user_id', Auth::id())
                ->where('id', $id)
                ->first();

            if (!$dependent) {
                return AppHelper::sendErrorResponse('لم يتم العثور على المعال', 404);
            }

            $dependent->delete();

            return AppHelper::sendSuccessResponse('تم حذف المعال بنجاح');

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get insurance institutions list
     */
    public function getInsuranceInstitutions(): JsonResponse
    {
        try {
            $institutions = InsuranceInstitution::where('is_active', true)
                ->orderBy('name')
                ->get(['id', 'name', 'code', 'type']);

            return AppHelper::sendSuccessResponse('تم جلب قائمة مؤسسات التأمين بنجاح', $institutions);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get insurance summary
     */
    public function getInsuranceSummary(): JsonResponse
    {
        try {
            $user = Auth::user();
            $dependentsCount = UserDependent::where('user_id', $user->id)->count();
            $insuredDependentsCount = UserDependent::where('user_id', $user->id)
                ->where('is_insured', true)
                ->count();

            $data = [
                'employee_insured' => !empty($user->insurance_type),
                'insurance_type' => $user->insurance_type,
                'insurance_start_date' => $user->insurance_start_date?->format('Y-m-d'),
                'total_dependents' => $dependentsCount,
                'insured_dependents' => $insuredDependentsCount,
                'uninsured_dependents' => $dependentsCount - $insuredDependentsCount,
                'insurance_institution_name' => $user->insuranceInstitution?->name
            ];

            return AppHelper::sendSuccessResponse('تم جلب ملخص التأمين بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    // Helper methods
    private function getInsuranceTypeLabel($type): ?string
    {
        $types = [
            'social' => 'تأمين اجتماعي',
            'medical' => 'تأمين طبي'
        ];

        return $types[$type] ?? null;
    }

    private function getMedicalInsuranceCategoryLabel($category): ?string
    {
        $categories = [
            'public' => 'حكومي',
            'private' => 'خاص'
        ];

        return $categories[$category] ?? null;
    }

    private function getRelationshipLabel($relationship): ?string
    {
        $relationships = [
            'spouse' => 'زوج/زوجة',
            'child' => 'ابن/ابنة',
            'parent' => 'والد/والدة',
            'sibling' => 'أخ/أخت',
            'other' => 'أخرى'
        ];

        return $relationships[$relationship] ?? null;
    }
}
