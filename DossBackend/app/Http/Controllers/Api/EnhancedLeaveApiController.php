<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\LeaveRequestMaster;
use App\Models\LeaveType;
use App\Models\Holiday;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Carbon;
use Exception;

class EnhancedLeaveApiController extends Controller
{
    /**
     * Get employee leave balance
     */
    public function getLeaveBalance(): JsonResponse
    {
        try {
            $userId = Auth::id();
            $user = Auth::user();
            $currentYear = now()->year;

            // جلب أنواع الإجازات المتاحة
            $leaveTypes = LeaveType::where('company_id', $user->company_id)
                ->where('is_active', 1)
                ->get();

            $leaveBalance = [];

            foreach ($leaveTypes as $leaveType) {
                // حساب الإجازات المستخدمة هذا العام
                $usedLeaves = LeaveRequestMaster::where('user_id', $userId)
                    ->where('leave_type_id', $leaveType->id)
                    ->where('status', 'approved')
                    ->whereYear('leave_from', $currentYear)
                    ->sum('no_of_days');

                // الرصيد المتاح
                $allocatedLeaves = $leaveType->leave_allocated ?? 0;
                $remainingLeaves = $allocatedLeaves - $usedLeaves;

                $leaveBalance[] = [
                    'leave_type_id' => $leaveType->id,
                    'leave_type_name' => $leaveType->name,
                    'allocated_days' => $allocatedLeaves,
                    'used_days' => $usedLeaves,
                    'remaining_days' => max(0, $remainingLeaves),
                    'usage_percentage' => $allocatedLeaves > 0 ? round(($usedLeaves / $allocatedLeaves) * 100, 2) : 0
                ];
            }

            return AppHelper::sendSuccessResponse('تم جلب رصيد الإجازات بنجاح', $leaveBalance);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get leave requests history
     */
    public function getLeaveHistory(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $status = $request->get('status');
            $year = $request->get('year', now()->year);

            $query = LeaveRequestMaster::where('user_id', $userId)
                ->with(['leaveType'])
                ->whereYear('leave_from', $year);

            if ($status) {
                $query->where('status', $status);
            }

            $leaveRequests = $query->orderBy('created_at', 'desc')->get();

            $data = $leaveRequests->map(function ($leave) {
                return [
                    'id' => $leave->id,
                    'leave_type' => $leave->leaveType?->name,
                    'leave_from' => $leave->leave_from->format('Y-m-d'),
                    'leave_to' => $leave->leave_to->format('Y-m-d'),
                    'no_of_days' => $leave->no_of_days,
                    'reason' => $leave->reason,
                    'status' => $leave->status,
                    'status_label' => $this->getStatusLabel($leave->status),
                    'leave_requested_date' => $leave->leave_requested_date->format('Y-m-d'),
                    'approved_date' => $leave->approved_date?->format('Y-m-d'),
                    'rejected_date' => $leave->rejected_date?->format('Y-m-d'),
                    'admin_remark' => $leave->admin_remark,
                    'early_exit' => $leave->early_exit,
                    'created_at' => $leave->created_at->format('Y-m-d H:i:s')
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب تاريخ الإجازات بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Submit leave request
     */
    public function submitLeaveRequest(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'leave_type_id' => 'required|exists:leave_types,id',
                'leave_from' => 'required|date|after_or_equal:today',
                'leave_to' => 'required|date|after_or_equal:leave_from',
                'reason' => 'required|string|max:500',
                'early_exit' => 'boolean'
            ]);

            if ($validator->fails()) {
                return AppHelper::sendErrorResponse('بيانات غير صحيحة', 422, $validator->errors());
            }

            $userId = Auth::id();
            $user = Auth::user();

            // التحقق من نوع الإجازة
            $leaveType = LeaveType::where('id', $request->leave_type_id)
                ->where('company_id', $user->company_id)
                ->where('is_active', 1)
                ->first();

            if (!$leaveType) {
                return AppHelper::sendErrorResponse('نوع الإجازة غير صحيح', 422);
            }

            // حساب عدد الأيام
            $leaveFrom = Carbon::parse($request->leave_from);
            $leaveTo = Carbon::parse($request->leave_to);
            $numberOfDays = $this->calculateLeaveDays($leaveFrom, $leaveTo);

            // التحقق من الرصيد المتاح
            if ($leaveType->leave_allocated) {
                $currentYear = now()->year;
                $usedLeaves = LeaveRequestMaster::where('user_id', $userId)
                    ->where('leave_type_id', $leaveType->id)
                    ->where('status', 'approved')
                    ->whereYear('leave_from', $currentYear)
                    ->sum('no_of_days');

                $remainingLeaves = $leaveType->leave_allocated - $usedLeaves;

                if ($numberOfDays > $remainingLeaves) {
                    return AppHelper::sendErrorResponse(
                        "الرصيد المتاح غير كافي. الرصيد المتبقي: {$remainingLeaves} يوم",
                        422
                    );
                }
            }

            // التحقق من تداخل الإجازات
            $overlappingLeave = LeaveRequestMaster::where('user_id', $userId)
                ->where('status', '!=', 'rejected')
                ->where(function ($query) use ($leaveFrom, $leaveTo) {
                    $query->whereBetween('leave_from', [$leaveFrom, $leaveTo])
                        ->orWhereBetween('leave_to', [$leaveFrom, $leaveTo])
                        ->orWhere(function ($q) use ($leaveFrom, $leaveTo) {
                            $q->where('leave_from', '<=', $leaveFrom)
                              ->where('leave_to', '>=', $leaveTo);
                        });
                })
                ->exists();

            if ($overlappingLeave) {
                return AppHelper::sendErrorResponse('يوجد تداخل مع إجازة أخرى في نفس الفترة', 422);
            }

            // إنشاء طلب الإجازة
            $leaveRequest = LeaveRequestMaster::create([
                'user_id' => $userId,
                'leave_type_id' => $request->leave_type_id,
                'leave_from' => $leaveFrom,
                'leave_to' => $leaveTo,
                'no_of_days' => $numberOfDays,
                'reason' => $request->reason,
                'early_exit' => $request->boolean('early_exit'),
                'leave_requested_date' => now(),
                'status' => 'pending'
            ]);

            return AppHelper::sendSuccessResponse('تم تقديم طلب الإجازة بنجاح', [
                'id' => $leaveRequest->id,
                'leave_type' => $leaveType->name,
                'leave_from' => $leaveRequest->leave_from->format('Y-m-d'),
                'leave_to' => $leaveRequest->leave_to->format('Y-m-d'),
                'no_of_days' => $leaveRequest->no_of_days,
                'status' => $leaveRequest->status,
                'status_label' => $this->getStatusLabel($leaveRequest->status)
            ]);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Cancel leave request
     */
    public function cancelLeaveRequest($id): JsonResponse
    {
        try {
            $userId = Auth::id();

            $leaveRequest = LeaveRequestMaster::where('user_id', $userId)
                ->where('id', $id)
                ->where('status', 'pending')
                ->first();

            if (!$leaveRequest) {
                return AppHelper::sendErrorResponse('لم يتم العثور على طلب الإجازة أو لا يمكن إلغاؤه', 404);
            }

            $leaveRequest->update(['status' => 'cancelled']);

            return AppHelper::sendSuccessResponse('تم إلغاء طلب الإجازة بنجاح');

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get leave types
     */
    public function getLeaveTypes(): JsonResponse
    {
        try {
            $user = Auth::user();

            $leaveTypes = LeaveType::where('company_id', $user->company_id)
                ->where('is_active', 1)
                ->get(['id', 'name', 'leave_allocated', 'early_exit']);

            return AppHelper::sendSuccessResponse('تم جلب أنواع الإجازات بنجاح', $leaveTypes);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get holidays
     */
    public function getHolidays(Request $request): JsonResponse
    {
        try {
            $year = $request->get('year', now()->year);

            $holidays = Holiday::whereYear('event_date', $year)
                ->where('is_active', 1)
                ->orderBy('event_date')
                ->get(['id', 'event', 'event_date', 'note']);

            $data = $holidays->map(function ($holiday) {
                return [
                    'id' => $holiday->id,
                    'event' => $holiday->event,
                    'event_date' => $holiday->event_date->format('Y-m-d'),
                    'day_name' => $holiday->event_date->locale('ar')->dayName,
                    'note' => $holiday->note
                ];
            });

            return AppHelper::sendSuccessResponse('تم جلب الإجازات الرسمية بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Get leave summary
     */
    public function getLeaveSummary(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $year = $request->get('year', now()->year);

            $totalRequests = LeaveRequestMaster::where('user_id', $userId)
                ->whereYear('leave_from', $year)
                ->count();

            $approvedRequests = LeaveRequestMaster::where('user_id', $userId)
                ->where('status', 'approved')
                ->whereYear('leave_from', $year)
                ->count();

            $pendingRequests = LeaveRequestMaster::where('user_id', $userId)
                ->where('status', 'pending')
                ->whereYear('leave_from', $year)
                ->count();

            $rejectedRequests = LeaveRequestMaster::where('user_id', $userId)
                ->where('status', 'rejected')
                ->whereYear('leave_from', $year)
                ->count();

            $totalLeaveDays = LeaveRequestMaster::where('user_id', $userId)
                ->where('status', 'approved')
                ->whereYear('leave_from', $year)
                ->sum('no_of_days');

            $data = [
                'year' => $year,
                'total_requests' => $totalRequests,
                'approved_requests' => $approvedRequests,
                'pending_requests' => $pendingRequests,
                'rejected_requests' => $rejectedRequests,
                'total_leave_days' => $totalLeaveDays,
                'approval_rate' => $totalRequests > 0 ? round(($approvedRequests / $totalRequests) * 100, 2) : 0
            ];

            return AppHelper::sendSuccessResponse('تم جلب ملخص الإجازات بنجاح', $data);

        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    // Helper methods
    private function calculateLeaveDays(Carbon $from, Carbon $to): int
    {
        $days = 0;
        $current = $from->copy();

        while ($current->lte($to)) {
            // تجاهل أيام الجمعة والسبت (عطلة نهاية الأسبوع)
            if (!$current->isWeekend()) {
                // التحقق من أنه ليس عطلة رسمية
                $isHoliday = Holiday::where('event_date', $current->toDateString())
                    ->where('is_active', 1)
                    ->exists();

                if (!$isHoliday) {
                    $days++;
                }
            }
            $current->addDay();
        }

        return $days;
    }

    private function getStatusLabel($status): string
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'approved' => 'معتمد',
            'rejected' => 'مرفوض',
            'cancelled' => 'ملغي'
        ];

        return $statuses[$status] ?? $status;
    }
}
