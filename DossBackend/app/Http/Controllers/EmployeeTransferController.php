<?php

namespace App\Http\Controllers;

use App\Models\EmployeeTransfer;
use App\Models\User;
use App\Models\Branch;
use App\Models\Department;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeeTransferController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of transfers
     */
    public function index(Request $request)
    {
        $query = EmployeeTransfer::with(['user', 'fromBranch', 'toBranch', 'fromDepartment', 'toDepartment', 'fromPost', 'toPost', 'approvedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('transfer_type')) {
            $query->where('transfer_type', $request->transfer_type);
        }

        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        if ($request->filled('date_from')) {
            $query->where('effective_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('effective_date', '<=', $request->date_to);
        }

        $transfers = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.transfers.index', compact('transfers', 'users'));
    }

    /**
     * Show the form for creating a new transfer
     */
    public function create()
    {
        $employees = User::with(['branch', 'department', 'post'])->get();
        $branches = Branch::select('id', 'name')->get();
        $departments = Department::select('id', 'name')->get();
        $posts = Post::select('id', 'name')->get();

        return view('employee-affairs.transfers.create', compact('employees', 'branches', 'departments', 'posts'));
    }

    /**
     * Store a newly created transfer
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'transfer_type' => 'required|in:branch,department,position,temporary,permanent',
            'from_branch_id' => 'nullable|exists:branches,id',
            'to_branch_id' => 'nullable|exists:branches,id',
            'from_department_id' => 'nullable|exists:departments,id',
            'to_department_id' => 'nullable|exists:departments,id',
            'from_post_id' => 'nullable|exists:posts,id',
            'to_post_id' => 'nullable|exists:posts,id',
            'reason' => 'required|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after:effective_date',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Validate transfer logic
        $user = User::findOrFail($request->user_id);
        $validationResult = $this->validateTransferData($request, $user);
        
        if (!$validationResult['valid']) {
            return redirect()->back()
                ->withErrors(['transfer' => $validationResult['message']])
                ->withInput();
        }

        EmployeeTransfer::create([
            'user_id' => $request->user_id,
            'transfer_type' => $request->transfer_type,
            'from_branch_id' => $request->from_branch_id,
            'to_branch_id' => $request->to_branch_id,
            'from_department_id' => $request->from_department_id,
            'to_department_id' => $request->to_department_id,
            'from_post_id' => $request->from_post_id,
            'to_post_id' => $request->to_post_id,
            'reason' => $request->reason,
            'approval_status' => 'pending',
            'effective_date' => $request->effective_date,
            'end_date' => $request->end_date,
            'notes' => $request->notes,
            'created_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-transfers.index')
            ->with('success', 'تم إنشاء طلب النقل بنجاح وهو في انتظار الموافقة');
    }

    /**
     * Display the specified transfer
     */
    public function show(EmployeeTransfer $employeeTransfer)
    {
        $employeeTransfer->load(['user', 'fromBranch', 'toBranch', 'fromDepartment', 'toDepartment', 'fromPost', 'toPost', 'approvedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.transfers.show', compact('employeeTransfer'));
    }

    /**
     * Show the form for editing the specified transfer
     */
    public function edit(EmployeeTransfer $employeeTransfer)
    {
        if (!$employeeTransfer->canBeApproved()) {
            return redirect()->route('admin.employee-transfers.index')
                ->with('error', 'لا يمكن تعديل هذا النقل');
        }

        $users = User::with(['branch', 'department', 'post'])->get();
        $branches = Branch::select('id', 'name')->get();
        $departments = Department::select('id', 'name')->get();
        $posts = Post::select('id', 'name')->get();

        return view('employee-affairs.transfers.edit', compact('employeeTransfer', 'users', 'branches', 'departments', 'posts'));
    }

    /**
     * Update the specified transfer
     */
    public function update(Request $request, EmployeeTransfer $employeeTransfer)
    {
        if (!$employeeTransfer->canBeApproved()) {
            return redirect()->route('admin.employee-transfers.index')
                ->with('error', 'لا يمكن تعديل هذا النقل');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'transfer_type' => 'required|in:branch,department,position,temporary,permanent',
            'from_branch_id' => 'nullable|exists:branches,id',
            'to_branch_id' => 'nullable|exists:branches,id',
            'from_department_id' => 'nullable|exists:departments,id',
            'to_department_id' => 'nullable|exists:departments,id',
            'from_post_id' => 'nullable|exists:posts,id',
            'to_post_id' => 'nullable|exists:posts,id',
            'reason' => 'required|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after:effective_date',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Validate transfer logic
        $user = User::findOrFail($request->user_id);
        $validationResult = $this->validateTransferData($request, $user);
        
        if (!$validationResult['valid']) {
            return redirect()->back()
                ->withErrors(['transfer' => $validationResult['message']])
                ->withInput();
        }

        $employeeTransfer->update([
            'user_id' => $request->user_id,
            'transfer_type' => $request->transfer_type,
            'from_branch_id' => $request->from_branch_id,
            'to_branch_id' => $request->to_branch_id,
            'from_department_id' => $request->from_department_id,
            'to_department_id' => $request->to_department_id,
            'from_post_id' => $request->from_post_id,
            'to_post_id' => $request->to_post_id,
            'reason' => $request->reason,
            'effective_date' => $request->effective_date,
            'end_date' => $request->end_date,
            'notes' => $request->notes,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-transfers.index')
            ->with('success', 'تم تحديث طلب النقل بنجاح');
    }

    /**
     * Approve the specified transfer
     */
    public function approve(EmployeeTransfer $employeeTransfer)
    {
        if (!$employeeTransfer->canBeApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن الموافقة على هذا النقل');
        }

        $employeeTransfer->approve();

        return redirect()->back()
            ->with('success', 'تم الموافقة على النقل بنجاح');
    }

    /**
     * Reject the specified transfer
     */
    public function reject(EmployeeTransfer $employeeTransfer)
    {
        if (!$employeeTransfer->canBeRejected()) {
            return redirect()->back()
                ->with('error', 'لا يمكن رفض هذا النقل');
        }

        $employeeTransfer->reject();

        return redirect()->back()
            ->with('success', 'تم رفض النقل');
    }

    /**
     * Execute the specified transfer
     */
    public function execute(EmployeeTransfer $employeeTransfer)
    {
        if (!$employeeTransfer->isApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن تنفيذ نقل غير معتمد');
        }

        if ($employeeTransfer->executeTransfer()) {
            return redirect()->back()
                ->with('success', 'تم تنفيذ النقل بنجاح');
        }

        return redirect()->back()
            ->with('error', 'فشل في تنفيذ النقل');
    }

    /**
     * Remove the specified transfer
     */
    public function destroy(EmployeeTransfer $employeeTransfer)
    {
        if (!$employeeTransfer->isPending()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذا النقل');
        }

        $employeeTransfer->delete();

        return redirect()->route('admin.employee-transfers.index')
            ->with('success', 'تم حذف طلب النقل بنجاح');
    }

    /**
     * Get transfers for specific user (AJAX)
     */
    public function getUserTransfers($userId)
    {
        $transfers = EmployeeTransfer::where('user_id', $userId)
            ->with(['fromBranch', 'toBranch', 'fromDepartment', 'toDepartment', 'fromPost', 'toPost', 'approvedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $transfers
        ]);
    }

    /**
     * Get transfer statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeeTransfer::count(),
            'pending' => EmployeeTransfer::pending()->count(),
            'approved' => EmployeeTransfer::approved()->count(),
            'rejected' => EmployeeTransfer::rejected()->count(),
            'active' => EmployeeTransfer::active()->count(),
            'temporary' => EmployeeTransfer::temporary()->count(),
            'by_type' => EmployeeTransfer::approved()
                ->selectRaw('transfer_type, COUNT(*) as count')
                ->groupBy('transfer_type')
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Validate transfer data
     */
    private function validateTransferData(Request $request, User $user): array
    {
        $transferType = $request->transfer_type;

        // Check if there's a change in the transfer
        $hasChange = false;

        if (in_array($transferType, ['branch', 'permanent', 'temporary'])) {
            if ($request->to_branch_id && $request->to_branch_id != $user->branch_id) {
                $hasChange = true;
            }
        }

        if (in_array($transferType, ['department', 'permanent', 'temporary'])) {
            if ($request->to_department_id && $request->to_department_id != $user->department_id) {
                $hasChange = true;
            }
        }

        if (in_array($transferType, ['position', 'permanent', 'temporary'])) {
            if ($request->to_post_id && $request->to_post_id != $user->post_id) {
                $hasChange = true;
            }
        }

        if (!$hasChange) {
            return [
                'valid' => false,
                'message' => 'يجب أن يكون هناك تغيير في الفرع أو القسم أو المنصب'
            ];
        }

        // Validate temporary transfer has end date
        if ($transferType === 'temporary' && !$request->end_date) {
            return [
                'valid' => false,
                'message' => 'النقل المؤقت يتطلب تاريخ انتهاء'
            ];
        }

        return ['valid' => true];
    }
}
