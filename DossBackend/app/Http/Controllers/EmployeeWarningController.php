<?php

namespace App\Http\Controllers;

use App\Models\EmployeeWarning;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeeWarningController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of warnings
     */
    public function index(Request $request)
    {
        $query = EmployeeWarning::with(['user', 'issuedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('warning_type')) {
            $query->where('warning_type', $request->warning_type);
        }

        if ($request->filled('severity_level')) {
            $query->where('severity_level', $request->severity_level);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->where('warning_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('warning_date', '<=', $request->date_to);
        }

        $warnings = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.warnings.index', compact('warnings', 'users'));
    }

    /**
     * Show the form for creating a new warning
     */
    public function create()
    {
        $employees = User::select('id', 'name', 'id_number')->get();
        return view('employee-affairs.warnings.create', compact('employees'));
    }

    /**
     * Store a newly created warning
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'warning_type' => 'required|in:verbal,written,final,suspension',
            'severity_level' => 'required|integer|min:1|max:5',
            'violation_type' => 'required|string|max:200',
            'description' => 'required|string|max:2000',
            'incident_date' => 'required|date|before_or_equal:today',
            'warning_date' => 'required|date|after_or_equal:incident_date',
            'improvement_plan' => 'nullable|string|max:1000',
            'follow_up_date' => 'nullable|date|after:warning_date',
            'escalation_level' => 'required|integer|min:1|max:5',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Calculate expiry date based on warning type
        $expiryDate = $this->calculateExpiryDate($request->warning_date, $request->warning_type);

        EmployeeWarning::create([
            'user_id' => $request->user_id,
            'warning_type' => $request->warning_type,
            'severity_level' => $request->severity_level,
            'violation_type' => $request->violation_type,
            'description' => $request->description,
            'incident_date' => $request->incident_date,
            'warning_date' => $request->warning_date,
            'expiry_date' => $expiryDate,
            'improvement_plan' => $request->improvement_plan,
            'follow_up_date' => $request->follow_up_date,
            'escalation_level' => $request->escalation_level,
            'status' => 'active',
            'notes' => $request->notes,
            'issued_by' => Auth::id(),
            'created_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-warnings.index')
            ->with('success', 'تم إصدار الإنذار بنجاح');
    }

    /**
     * Display the specified warning
     */
    public function show(EmployeeWarning $employeeWarning)
    {
        $employeeWarning->load(['user', 'issuedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.warnings.show', compact('employeeWarning'));
    }

    /**
     * Show the form for editing the specified warning
     */
    public function edit(EmployeeWarning $employeeWarning)
    {
        if (!$employeeWarning->canBeEdited()) {
            return redirect()->route('admin.employee-warnings.index')
                ->with('error', 'لا يمكن تعديل هذا الإنذار');
        }

        $users = User::select('id', 'name')->get();
        return view('employee-affairs.warnings.edit', compact('employeeWarning', 'users'));
    }

    /**
     * Update the specified warning
     */
    public function update(Request $request, EmployeeWarning $employeeWarning)
    {
        if (!$employeeWarning->canBeEdited()) {
            return redirect()->route('admin.employee-warnings.index')
                ->with('error', 'لا يمكن تعديل هذا الإنذار');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'warning_type' => 'required|in:verbal,written,final,suspension',
            'severity_level' => 'required|integer|min:1|max:5',
            'violation_type' => 'required|string|max:200',
            'description' => 'required|string|max:2000',
            'incident_date' => 'required|date|before_or_equal:today',
            'warning_date' => 'required|date|after_or_equal:incident_date',
            'improvement_plan' => 'nullable|string|max:1000',
            'follow_up_date' => 'nullable|date|after:warning_date',
            'escalation_level' => 'required|integer|min:1|max:5',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Calculate expiry date based on warning type
        $expiryDate = $this->calculateExpiryDate($request->warning_date, $request->warning_type);

        $employeeWarning->update([
            'user_id' => $request->user_id,
            'warning_type' => $request->warning_type,
            'severity_level' => $request->severity_level,
            'violation_type' => $request->violation_type,
            'description' => $request->description,
            'incident_date' => $request->incident_date,
            'warning_date' => $request->warning_date,
            'expiry_date' => $expiryDate,
            'improvement_plan' => $request->improvement_plan,
            'follow_up_date' => $request->follow_up_date,
            'escalation_level' => $request->escalation_level,
            'notes' => $request->notes,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-warnings.index')
            ->with('success', 'تم تحديث الإنذار بنجاح');
    }

    /**
     * Acknowledge the specified warning
     */
    public function acknowledge(Request $request, EmployeeWarning $employeeWarning)
    {
        if (!$employeeWarning->canBeAcknowledged()) {
            return redirect()->back()
                ->with('error', 'لا يمكن إقرار هذا الإنذار');
        }

        $validator = Validator::make($request->all(), [
            'employee_response' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeWarning->update([
            'acknowledged' => true,
            'acknowledged_at' => now(),
            'employee_response' => $request->employee_response,
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم إقرار الإنذار بنجاح');
    }

    /**
     * Follow up on the specified warning
     */
    public function followUp(Request $request, EmployeeWarning $employeeWarning)
    {
        $validator = Validator::make($request->all(), [
            'follow_up_notes' => 'required|string|max:1000',
            'improvement_status' => 'required|in:improved,no_change,deteriorated',
            'next_follow_up_date' => 'nullable|date|after:today'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeWarning->update([
            'follow_up_notes' => $request->follow_up_notes,
            'improvement_status' => $request->improvement_status,
            'last_follow_up_date' => now(),
            'next_follow_up_date' => $request->next_follow_up_date,
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم تسجيل المتابعة بنجاح');
    }

    /**
     * Resolve the specified warning
     */
    public function resolve(EmployeeWarning $employeeWarning)
    {
        if (!$employeeWarning->canBeResolved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حل هذا الإنذار');
        }

        $employeeWarning->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم حل الإنذار بنجاح');
    }

    /**
     * Escalate the specified warning
     */
    public function escalate(Request $request, EmployeeWarning $employeeWarning)
    {
        if (!$employeeWarning->canBeEscalated()) {
            return redirect()->back()
                ->with('error', 'لا يمكن تصعيد هذا الإنذار');
        }

        $validator = Validator::make($request->all(), [
            'escalation_reason' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $newEscalationLevel = min($employeeWarning->escalation_level + 1, 5);

        $employeeWarning->update([
            'escalation_level' => $newEscalationLevel,
            'escalation_reason' => $request->escalation_reason,
            'escalated_at' => now(),
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم تصعيد الإنذار بنجاح');
    }

    /**
     * Remove the specified warning
     */
    public function destroy(EmployeeWarning $employeeWarning)
    {
        if (!$employeeWarning->canBeDeleted()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذا الإنذار');
        }

        $employeeWarning->delete();

        return redirect()->route('admin.employee-warnings.index')
            ->with('success', 'تم حذف الإنذار بنجاح');
    }

    /**
     * Get warnings for specific user (AJAX)
     */
    public function getUserWarnings($userId)
    {
        $warnings = EmployeeWarning::where('user_id', $userId)
            ->with(['issuedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $warnings
        ]);
    }

    /**
     * Get warning statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeeWarning::count(),
            'active' => EmployeeWarning::active()->count(),
            'resolved' => EmployeeWarning::resolved()->count(),
            'expired' => EmployeeWarning::expired()->count(),
            'this_month' => EmployeeWarning::thisMonth()->count(),
            'pending_acknowledgment' => EmployeeWarning::active()
                ->where('acknowledged', false)
                ->count(),
            'pending_follow_up' => EmployeeWarning::active()
                ->whereNotNull('follow_up_date')
                ->where('follow_up_date', '<=', now())
                ->count(),
            'by_type' => EmployeeWarning::selectRaw('warning_type, COUNT(*) as count')
                ->groupBy('warning_type')
                ->get(),
            'by_severity' => EmployeeWarning::selectRaw('severity_level, COUNT(*) as count')
                ->groupBy('severity_level')
                ->get(),
            'by_escalation' => EmployeeWarning::selectRaw('escalation_level, COUNT(*) as count')
                ->groupBy('escalation_level')
                ->get(),
            'acknowledgment_rate' => EmployeeWarning::count() > 0 ? 
                (EmployeeWarning::where('acknowledged', true)->count() / EmployeeWarning::count()) * 100 : 0,
            'resolution_rate' => EmployeeWarning::count() > 0 ? 
                (EmployeeWarning::resolved()->count() / EmployeeWarning::count()) * 100 : 0
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Calculate expiry date based on warning type
     */
    private function calculateExpiryDate($warningDate, $warningType): string
    {
        $date = \Carbon\Carbon::parse($warningDate);

        switch ($warningType) {
            case 'verbal':
                return $date->addMonths(3)->toDateString();
            case 'written':
                return $date->addMonths(6)->toDateString();
            case 'final':
                return $date->addYear()->toDateString();
            case 'suspension':
                return $date->addMonths(12)->toDateString();
            default:
                return $date->addMonths(6)->toDateString();
        }
    }
}
