<?php

namespace App\Http\Controllers;

use App\Models\EmployeeSuggestion;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeeSuggestionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of suggestions
     */
    public function index(Request $request)
    {
        $query = EmployeeSuggestion::with(['user', 'reviewedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('suggestion_type')) {
            $query->where('suggestion_type', $request->suggestion_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        $suggestions = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.suggestions.index', compact('suggestions', 'users'));
    }

    /**
     * Show the form for creating a new suggestion
     */
    public function create()
    {
        $employees = User::select('id', 'name', 'id_number')->get();
        return view('employee-affairs.suggestions.create', compact('employees'));
    }

    /**
     * Store a newly created suggestion
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'suggestion_type' => 'required|in:process_improvement,cost_reduction,innovation,safety,customer_service,other',
            'title' => 'required|string|max:200',
            'description' => 'required|string|max:2000',
            'current_situation' => 'required|string|max:1000',
            'proposed_solution' => 'required|string|max:1000',
            'expected_benefits' => 'required|string|max:1000',
            'implementation_cost' => 'nullable|numeric|min:0',
            'expected_savings' => 'nullable|numeric|min:0',
            'priority' => 'required|in:low,medium,high,urgent',
            'attachments' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        EmployeeSuggestion::create([
            'user_id' => $request->user_id,
            'suggestion_type' => $request->suggestion_type,
            'title' => $request->title,
            'description' => $request->description,
            'current_situation' => $request->current_situation,
            'proposed_solution' => $request->proposed_solution,
            'expected_benefits' => $request->expected_benefits,
            'implementation_cost' => $request->implementation_cost,
            'expected_savings' => $request->expected_savings,
            'priority' => $request->priority,
            'status' => 'submitted',
            'attachments' => $request->attachments,
            'created_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-suggestions.index')
            ->with('success', 'تم تقديم الاقتراح بنجاح وهو قيد المراجعة');
    }

    /**
     * Display the specified suggestion
     */
    public function show(EmployeeSuggestion $employeeSuggestion)
    {
        $employeeSuggestion->load(['user', 'reviewedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.suggestions.show', compact('employeeSuggestion'));
    }

    /**
     * Show the form for editing the specified suggestion
     */
    public function edit(EmployeeSuggestion $employeeSuggestion)
    {
        if (!$employeeSuggestion->canBeEdited()) {
            return redirect()->route('admin.employee-suggestions.index')
                ->with('error', 'لا يمكن تعديل هذا الاقتراح');
        }

        $users = User::select('id', 'name')->get();
        return view('employee-affairs.suggestions.edit', compact('employeeSuggestion', 'users'));
    }

    /**
     * Update the specified suggestion
     */
    public function update(Request $request, EmployeeSuggestion $employeeSuggestion)
    {
        if (!$employeeSuggestion->canBeEdited()) {
            return redirect()->route('admin.employee-suggestions.index')
                ->with('error', 'لا يمكن تعديل هذا الاقتراح');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'suggestion_type' => 'required|in:process_improvement,cost_reduction,innovation,safety,customer_service,other',
            'title' => 'required|string|max:200',
            'description' => 'required|string|max:2000',
            'current_situation' => 'required|string|max:1000',
            'proposed_solution' => 'required|string|max:1000',
            'expected_benefits' => 'required|string|max:1000',
            'implementation_cost' => 'nullable|numeric|min:0',
            'expected_savings' => 'nullable|numeric|min:0',
            'priority' => 'required|in:low,medium,high,urgent',
            'attachments' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeSuggestion->update([
            'user_id' => $request->user_id,
            'suggestion_type' => $request->suggestion_type,
            'title' => $request->title,
            'description' => $request->description,
            'current_situation' => $request->current_situation,
            'proposed_solution' => $request->proposed_solution,
            'expected_benefits' => $request->expected_benefits,
            'implementation_cost' => $request->implementation_cost,
            'expected_savings' => $request->expected_savings,
            'priority' => $request->priority,
            'attachments' => $request->attachments,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-suggestions.index')
            ->with('success', 'تم تحديث الاقتراح بنجاح');
    }

    /**
     * Review the specified suggestion
     */
    public function review(Request $request, EmployeeSuggestion $employeeSuggestion)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:under_review,approved,rejected,implemented',
            'review_notes' => 'required|string|max:1000',
            'implementation_timeline' => 'nullable|string|max:500',
            'reward_amount' => 'nullable|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeSuggestion->update([
            'status' => $request->status,
            'review_notes' => $request->review_notes,
            'implementation_timeline' => $request->implementation_timeline,
            'reward_amount' => $request->reward_amount,
            'reviewed_at' => now(),
            'reviewed_by' => Auth::id(),
            'updated_by' => Auth::id()
        ]);

        $statusMessages = [
            'under_review' => 'تم وضع الاقتراح قيد المراجعة',
            'approved' => 'تم الموافقة على الاقتراح',
            'rejected' => 'تم رفض الاقتراح',
            'implemented' => 'تم تنفيذ الاقتراح'
        ];

        return redirect()->back()
            ->with('success', $statusMessages[$request->status]);
    }

    /**
     * Mark suggestion as implemented
     */
    public function implement(Request $request, EmployeeSuggestion $employeeSuggestion)
    {
        if (!$employeeSuggestion->isApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن تنفيذ اقتراح غير معتمد');
        }

        $validator = Validator::make($request->all(), [
            'implementation_notes' => 'required|string|max:1000',
            'actual_cost' => 'nullable|numeric|min:0',
            'actual_savings' => 'nullable|numeric|min:0',
            'reward_amount' => 'nullable|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeSuggestion->update([
            'status' => 'implemented',
            'implementation_notes' => $request->implementation_notes,
            'actual_cost' => $request->actual_cost,
            'actual_savings' => $request->actual_savings,
            'reward_amount' => $request->reward_amount,
            'implemented_at' => now(),
            'updated_by' => Auth::id()
        ]);

        return redirect()->back()
            ->with('success', 'تم تنفيذ الاقتراح بنجاح');
    }

    /**
     * Remove the specified suggestion
     */
    public function destroy(EmployeeSuggestion $employeeSuggestion)
    {
        if (!$employeeSuggestion->canBeDeleted()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذا الاقتراح');
        }

        $employeeSuggestion->delete();

        return redirect()->route('admin.employee-suggestions.index')
            ->with('success', 'تم حذف الاقتراح بنجاح');
    }

    /**
     * Get suggestions for specific user (AJAX)
     */
    public function getUserSuggestions($userId)
    {
        $suggestions = EmployeeSuggestion::where('user_id', $userId)
            ->with(['reviewedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $suggestions
        ]);
    }

    /**
     * Get suggestion statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeeSuggestion::count(),
            'submitted' => EmployeeSuggestion::submitted()->count(),
            'under_review' => EmployeeSuggestion::underReview()->count(),
            'approved' => EmployeeSuggestion::approved()->count(),
            'rejected' => EmployeeSuggestion::rejected()->count(),
            'implemented' => EmployeeSuggestion::implemented()->count(),
            'this_month' => EmployeeSuggestion::thisMonth()->count(),
            'total_rewards' => EmployeeSuggestion::implemented()->sum('reward_amount'),
            'total_savings' => EmployeeSuggestion::implemented()->sum('actual_savings'),
            'by_type' => EmployeeSuggestion::selectRaw('suggestion_type, COUNT(*) as count')
                ->groupBy('suggestion_type')
                ->get(),
            'by_priority' => EmployeeSuggestion::selectRaw('priority, COUNT(*) as count')
                ->groupBy('priority')
                ->get(),
            'implementation_rate' => EmployeeSuggestion::approved()->count() > 0 ? 
                (EmployeeSuggestion::implemented()->count() / EmployeeSuggestion::approved()->count()) * 100 : 0
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
