<?php

namespace App\Http\Controllers;

use App\Models\EmployeeBonus;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EmployeeBonusController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of bonuses
     */
    public function index(Request $request)
    {
        $query = EmployeeBonus::with(['user', 'approvedBy', 'createdBy']);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('bonus_type')) {
            $query->where('bonus_type', $request->bonus_type);
        }

        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        if ($request->filled('date_from')) {
            $query->where('effective_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('effective_date', '<=', $request->date_to);
        }

        $bonuses = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::select('id', 'name')->get();

        return view('employee-affairs.bonuses.index', compact('bonuses', 'users'));
    }

    /**
     * Show the form for creating a new bonus
     */
    public function create()
    {
        $employees = User::select('id', 'name', 'id_number')->get();
        return view('employee-affairs.bonuses.create', compact('employees'));
    }

    /**
     * Store a newly created bonus
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'bonus_type' => 'required|in:performance,annual,special,achievement,holiday,overtime',
            'bonus_category' => 'required|in:financial,in_kind,promotion,recognition',
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string|max:500',
            'reason' => 'nullable|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $bonus = EmployeeBonus::create([
            'user_id' => $request->user_id,
            'bonus_type' => $request->bonus_type,
            'bonus_category' => $request->bonus_category,
            'amount' => $request->amount,
            'description' => $request->description,
            'reason' => $request->reason,
            'approval_status' => 'pending',
            'effective_date' => $request->effective_date,
            'notes' => $request->notes,
            'created_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-bonuses.index')
            ->with('success', 'تم إنشاء المكافأة بنجاح وهي في انتظار الموافقة');
    }

    /**
     * Display the specified bonus
     */
    public function show(EmployeeBonus $employeeBonus)
    {
        $employeeBonus->load(['user', 'approvedBy', 'createdBy', 'updatedBy']);
        return view('employee-affairs.bonuses.show', compact('employeeBonus'));
    }

    /**
     * Show the form for editing the specified bonus
     */
    public function edit(EmployeeBonus $employeeBonus)
    {
        if (!$employeeBonus->canBeApproved()) {
            return redirect()->route('admin.employee-bonuses.index')
                ->with('error', 'لا يمكن تعديل هذه المكافأة');
        }

        $users = User::select('id', 'name')->get();
        return view('employee-affairs.bonuses.edit', compact('employeeBonus', 'users'));
    }

    /**
     * Update the specified bonus
     */
    public function update(Request $request, EmployeeBonus $employeeBonus)
    {
        if (!$employeeBonus->canBeApproved()) {
            return redirect()->route('admin.employee-bonuses.index')
                ->with('error', 'لا يمكن تعديل هذه المكافأة');
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'bonus_type' => 'required|in:performance,annual,special,achievement,holiday,overtime',
            'bonus_category' => 'required|in:financial,in_kind,promotion,recognition',
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string|max:500',
            'reason' => 'nullable|string|max:1000',
            'effective_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $employeeBonus->update([
            'user_id' => $request->user_id,
            'bonus_type' => $request->bonus_type,
            'bonus_category' => $request->bonus_category,
            'amount' => $request->amount,
            'description' => $request->description,
            'reason' => $request->reason,
            'effective_date' => $request->effective_date,
            'notes' => $request->notes,
            'updated_by' => Auth::id()
        ]);

        return redirect()->route('admin.employee-bonuses.index')
            ->with('success', 'تم تحديث المكافأة بنجاح');
    }

    /**
     * Approve the specified bonus
     */
    public function approve(EmployeeBonus $employeeBonus)
    {
        if (!$employeeBonus->canBeApproved()) {
            return redirect()->back()
                ->with('error', 'لا يمكن الموافقة على هذه المكافأة');
        }

        $employeeBonus->approve();

        return redirect()->back()
            ->with('success', 'تم الموافقة على المكافأة بنجاح');
    }

    /**
     * Reject the specified bonus
     */
    public function reject(EmployeeBonus $employeeBonus)
    {
        if (!$employeeBonus->canBeRejected()) {
            return redirect()->back()
                ->with('error', 'لا يمكن رفض هذه المكافأة');
        }

        $employeeBonus->reject();

        return redirect()->back()
            ->with('success', 'تم رفض المكافأة');
    }

    /**
     * Remove the specified bonus
     */
    public function destroy(EmployeeBonus $employeeBonus)
    {
        if (!$employeeBonus->isPending()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذه المكافأة');
        }

        $employeeBonus->delete();

        return redirect()->route('admin.employee-bonuses.index')
            ->with('success', 'تم حذف المكافأة بنجاح');
    }

    /**
     * Get bonuses for specific user (AJAX)
     */
    public function getUserBonuses($userId)
    {
        $bonuses = EmployeeBonus::where('user_id', $userId)
            ->with(['approvedBy', 'createdBy'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $bonuses
        ]);
    }

    /**
     * Get bonus statistics (AJAX)
     */
    public function getStats()
    {
        $stats = [
            'total' => EmployeeBonus::count(),
            'pending' => EmployeeBonus::pending()->count(),
            'approved' => EmployeeBonus::approved()->count(),
            'rejected' => EmployeeBonus::rejected()->count(),
            'this_month_total' => EmployeeBonus::thisMonth()->approved()->sum('amount'),
            'this_year_total' => EmployeeBonus::thisYear()->approved()->sum('amount'),
            'by_type' => EmployeeBonus::approved()
                ->selectRaw('bonus_type, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('bonus_type')
                ->get(),
            'by_category' => EmployeeBonus::approved()
                ->selectRaw('bonus_category, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('bonus_category')
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
