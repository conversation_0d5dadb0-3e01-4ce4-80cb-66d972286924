<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Requests\Employee\DocumentUploadRequest;
use App\Http\Requests\Employee\DocumentUpdateRequest;
use App\Http\Requests\Employee\DocumentVerificationRequest;
use App\Models\EmployeeDocument;
use App\Models\User;
use App\Services\Employee\EmployeeDocumentService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class EmployeeDocumentController extends Controller
{
    private EmployeeDocumentService $documentService;
    private string $view = 'admin.employee.documents.';

    public function __construct(EmployeeDocumentService $documentService)
    {
        $this->documentService = $documentService;
    }

    /**
     * Display employee documents
     */
    public function index(Request $request, int $userId)
    {
        $this->authorize('list_employee');
        
        try {
            $employee = User::with(['branch:id,name', 'department:id,dept_name', 'post:id,post_name'])
                ->findOrFail($userId);

            $filters = [
                'document_type' => $request->document_type,
                'is_verified' => $request->is_verified,
                'search' => $request->search
            ];

            $documents = $this->documentService->getEmployeeDocuments($userId, $filters);
            $stats = $this->documentService->getEmployeeDocumentStats($userId);
            $missingDocuments = $this->documentService->getMissingRequiredDocuments($userId);

            return view($this->view . 'index', compact(
                'employee', 
                'documents', 
                'stats', 
                'missingDocuments', 
                'filters'
            ));

        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    /**
     * Show upload form
     */
    public function create(int $userId)
    {
        $this->authorize('create_employee');
        
        try {
            $employee = User::select('id', 'name')->findOrFail($userId);
            $documentTypes = EmployeeDocument::DOCUMENT_TYPES;
            $missingDocuments = $this->documentService->getMissingRequiredDocuments($userId);

            return view($this->view . 'create', compact(
                'employee', 
                'documentTypes', 
                'missingDocuments'
            ));

        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    /**
     * Store uploaded document
     */
    public function store(DocumentUploadRequest $request)
    {
        $this->authorize('create_employee');
        
        try {
            $validatedData = $request->validated();
            $file = $request->file('document_file');

            $document = $this->documentService->uploadDocument($validatedData, $file);

            return redirect()
                ->route('admin.admin.employee.documents.index', $validatedData['user_id'])
                ->with('success', 'تم رفع المصوغة بنجاح');

        } catch (Exception $e) {
            return redirect()
                ->back()
                ->with('danger', $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show edit form
     */
    public function edit(int $documentId)
    {
        $this->authorize('edit_employee');
        
        try {
            $document = $this->documentService->getDocumentById($documentId);
            
            return view($this->view . 'edit', compact('document'));

        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    /**
     * Update document information
     */
    public function update(DocumentUpdateRequest $request, int $documentId)
    {
        $this->authorize('edit_employee');
        
        try {
            $validatedData = $request->validated();
            $document = $this->documentService->updateDocument($documentId, $validatedData);

            return redirect()
                ->route('admin.admin.employee.documents.index', $document->user_id)
                ->with('success', 'تم تحديث المصوغة بنجاح');

        } catch (Exception $e) {
            return redirect()
                ->back()
                ->with('danger', $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Verify document
     */
    public function verify(DocumentVerificationRequest $request, int $documentId)
    {
        $this->authorize('edit_employee');
        
        try {
            $validatedData = $request->validated();
            $document = $this->documentService->verifyDocument($documentId, $validatedData);

            $message = $validatedData['is_verified'] ? 'تم التحقق من المصوغة بنجاح' : 'تم إلغاء التحقق من المصوغة';

            return redirect()
                ->route('admin.admin.employee.documents.index', $document->user_id)
                ->with('success', $message);

        } catch (Exception $e) {
            return redirect()
                ->back()
                ->with('danger', $e->getMessage());
        }
    }

    /**
     * Download document
     */
    public function download(int $documentId)
    {
        $this->authorize('list_employee');
        
        try {
            $document = $this->documentService->getDocumentById($documentId);
            
            // Check if user can access this document
            if (!$this->documentService->canUserAccessDocument($documentId, auth()->id())) {
                abort(403, 'غير مصرح لك بالوصول لهذه المصوغة');
            }

            $filePath = public_path(EmployeeDocument::UPLOAD_PATH . $document->file_path);
            
            if (!file_exists($filePath)) {
                return redirect()->back()->with('danger', 'الملف غير موجود');
            }

            return Response::download($filePath, $document->original_name);

        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    /**
     * Delete document
     */
    public function destroy(int $documentId)
    {
        $this->authorize('delete_employee');
        
        try {
            $document = $this->documentService->getDocumentById($documentId);
            $userId = $document->user_id;
            
            $this->documentService->deleteDocument($documentId);

            return redirect()
                ->route('admin.admin.employee.documents.index', $userId)
                ->with('success', 'تم حذف المصوغة بنجاح');

        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    /**
     * Show document details
     */
    public function show(int $documentId)
    {
        $this->authorize('list_employee');
        
        try {
            $document = $this->documentService->getDocumentById($documentId);
            
            // Check if user can access this document
            if (!$this->documentService->canUserAccessDocument($documentId, auth()->id())) {
                abort(403, 'غير مصرح لك بالوصول لهذه المصوغة');
            }

            return view($this->view . 'show', compact('document'));

        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    /**
     * Get document types for AJAX
     */
    public function getDocumentTypes()
    {
        return response()->json([
            'document_types' => EmployeeDocument::DOCUMENT_TYPES,
            'required_types' => EmployeeDocument::REQUIRED_DOCUMENTS
        ]);
    }
}
