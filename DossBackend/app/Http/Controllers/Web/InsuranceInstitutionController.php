<?php

namespace App\Http\Controllers\Web;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Requests\InsuranceInstitution\InsuranceInstitutionRequest;
use App\Services\InsuranceInstitution\InsuranceInstitutionService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InsuranceInstitutionController extends Controller
{
    private $view = 'admin.InsuranceInstitution.';

    private InsuranceInstitutionService $service;

    public function __construct(InsuranceInstitutionService $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        $this->authorize('list_holiday');
        try {
            $list = $this->service->getAllLists();
            return view($this->view . 'index', compact('list'));
        } catch (Exception $exception) {
            dd($exception);
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    public function create(): Factory|View|RedirectResponse|Application
    {
        $this->authorize('create_holiday');
        try {
            return view($this->view . 'create');
        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    public function store(InsuranceInstitutionRequest $request): RedirectResponse
    {
        $this->authorize('create_holiday');
        try {
            $validatedData = $request->validated();
            DB::beginTransaction();
            $this->service->store($validatedData);
            DB::commit();
            return redirect()->route('admin.insurance_institution.index')->with('success', 'New Insurance Institution Detail Added Successfully');
        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('danger', $e->getMessage())
                ->withInput();
        }
    }

    public function show($id): JsonResponse
    {
        try {
            $this->authorize('show_holiday');
            $detail = $this->service->findDetailById($id);
            return response()->json([
                'data' => $detail,
            ]);
        } catch (Exception $exception) {
            return AppHelper::sendErrorResponse($exception->getMessage(), $exception->getCode());
        }
    }

    public function edit($id): Factory|View|RedirectResponse|Application
    {
        $this->authorize('edit_holiday');
        try {
            $detail = $this->service->findDetailById($id);
            return view($this->view . 'edit', compact('detail'));
        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

    public function update(InsuranceInstitutionRequest $request, $id): RedirectResponse
    {
        $this->authorize('edit_holiday');
        try {
            $validatedData = $request->validated();
            $this->service->update($validatedData, $id);
            return redirect()->route('admin.insurance_institution.index')->with('success', 'Insurance Institution Detail Updated Successfully');
        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage())
                ->withInput();
        }
    }

    public function delete($id): RedirectResponse
    {
        $this->authorize('delete_holiday');
        try {
            $this->service->delete($id);
            return redirect()->back()->with('success', 'Insurance Institution Removed Successfully');
        } catch (Exception $exception) {
            return redirect()->back()->with('danger', $exception->getMessage());
        }
    }

}
