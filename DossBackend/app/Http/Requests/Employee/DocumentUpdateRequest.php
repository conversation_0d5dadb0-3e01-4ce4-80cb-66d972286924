<?php

namespace App\Http\Requests\Employee;

use Illuminate\Foundation\Http\FormRequest;

class DocumentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'document_name' => [
                'required',
                'string',
                'max:255',
                'min:3'
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000'
            ]
        ];
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'document_name.required' => 'اسم المصوغة مطلوب',
            'document_name.min' => 'اسم المصوغة يجب أن يكون 3 أحرف على الأقل',
            'document_name.max' => 'اسم المصوغة لا يجب أن يتجاوز 255 حرف',
            'description.max' => 'الوصف لا يجب أن يتجاوز 1000 حرف'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'document_name' => 'اسم المصوغة',
            'description' => 'الوصف'
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Clean document name
        if ($this->has('document_name')) {
            $this->merge([
                'document_name' => trim($this->document_name)
            ]);
        }

        // Clean description
        if ($this->has('description')) {
            $this->merge([
                'description' => trim($this->description)
            ]);
        }
    }
}
