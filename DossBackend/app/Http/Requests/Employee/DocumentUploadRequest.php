<?php

namespace App\Http\Requests\Employee;

use App\Models\EmployeeDocument;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DocumentUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'user_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')
            ],
            'document_type' => [
                'required',
                'string',
                Rule::in(array_keys(EmployeeDocument::DOCUMENT_TYPES))
            ],
            'document_name' => [
                'required',
                'string',
                'max:255',
                'min:3'
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'document_file' => [
                'required',
                'file',
                'mimes:' . implode(',', EmployeeDocument::ALLOWED_EXTENSIONS),
                'max:' . EmployeeDocument::MAX_FILE_SIZE
            ]
        ];
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'user_id.required' => 'معرف الموظف مطلوب',
            'user_id.exists' => 'الموظف المحدد غير موجود',
            
            'document_type.required' => 'نوع المصوغة مطلوب',
            'document_type.in' => 'نوع المصوغة غير صحيح',
            
            'document_name.required' => 'اسم المصوغة مطلوب',
            'document_name.min' => 'اسم المصوغة يجب أن يكون 3 أحرف على الأقل',
            'document_name.max' => 'اسم المصوغة لا يجب أن يتجاوز 255 حرف',
            
            'description.max' => 'الوصف لا يجب أن يتجاوز 1000 حرف',
            
            'document_file.required' => 'ملف المصوغة مطلوب',
            'document_file.file' => 'يجب أن يكون المرفق ملف صحيح',
            'document_file.mimes' => 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', EmployeeDocument::ALLOWED_EXTENSIONS),
            'document_file.max' => 'حجم الملف كبير جداً. الحد الأقصى ' . EmployeeDocument::MAX_FILE_SIZE . ' KB'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'user_id' => 'الموظف',
            'document_type' => 'نوع المصوغة',
            'document_name' => 'اسم المصوغة',
            'description' => 'الوصف',
            'document_file' => 'ملف المصوغة'
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Clean document name
        if ($this->has('document_name')) {
            $this->merge([
                'document_name' => trim($this->document_name)
            ]);
        }

        // Clean description
        if ($this->has('description')) {
            $this->merge([
                'description' => trim($this->description)
            ]);
        }
    }
}
