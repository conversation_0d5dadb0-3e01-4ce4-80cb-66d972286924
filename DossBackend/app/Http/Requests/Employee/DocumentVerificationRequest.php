<?php

namespace App\Http\Requests\Employee;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DocumentVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'is_verified' => [
                'required',
                'boolean'
            ],
            'verification_notes' => [
                'nullable',
                'string',
                'max:1000'
            ]
        ];
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'is_verified.required' => 'حالة التحقق مطلوبة',
            'is_verified.boolean' => 'حالة التحقق يجب أن تكون صحيح أو خطأ',
            'verification_notes.max' => 'ملاحظات التحقق لا يجب أن تتجاوز 1000 حرف'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'is_verified' => 'حالة التحقق',
            'verification_notes' => 'ملاحظات التحقق'
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Clean verification notes
        if ($this->has('verification_notes')) {
            $this->merge([
                'verification_notes' => trim($this->verification_notes)
            ]);
        }

        // Convert string boolean to actual boolean
        if ($this->has('is_verified')) {
            $value = $this->is_verified;
            if (is_string($value)) {
                $this->merge([
                    'is_verified' => filter_var($value, FILTER_VALIDATE_BOOLEAN)
                ]);
            }
        }
    }
}
