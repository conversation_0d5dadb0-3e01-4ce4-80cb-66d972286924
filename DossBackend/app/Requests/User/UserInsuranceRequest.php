<?php

namespace App\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UserInsuranceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // Insurance
            'insurance_type' => 'nullable|in:social,medical',
            'medical_insurance_category' => 'nullable|required_if:insurance_type,medical|in:public,private',
            'insurance_institution_id' => 'nullable|exists:insurance_institutions,id',
            'insurance_job_title' => 'nullable|string|max:100',
            'insurance_start_date' => 'nullable|date',
            'insurance_salary' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'insurance_type.in' => 'Insurance type must be either social or medical.',
            'medical_insurance_category.required_if' => 'Medical insurance category is required when insurance type is medical.',
            'medical_insurance_category.in' => 'Medical insurance category must be either public or private.',
            'insurance_institution_id.exists' => 'Selected insurance institution does not exist.',
            'insurance_job_title.max' => 'Insurance job title cannot exceed 100 characters.',
            'insurance_start_date.date' => 'Insurance start date must be a valid date.',
            'insurance_salary.numeric' => 'Insurance salary must be a number.',
            'insurance_salary.min' => 'Insurance salary cannot be negative.',
        ];
    }
}
