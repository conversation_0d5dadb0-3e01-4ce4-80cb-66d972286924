<?php

namespace App\Requests\InsuranceInstitution;


use App\Helpers\AppHelper;
use Illuminate\Foundation\Http\FormRequest;

class InsuranceInstitutionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        $rules = [
            'name' => 'required|string',
            'insurance_number' => 'required|string|max:500',
        ];
        return $rules;
    }

}














