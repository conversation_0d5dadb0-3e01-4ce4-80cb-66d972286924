<?php

namespace App\Repositories;

use App\Models\InsuranceInstitution;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class InsuranceInstitutionRepository
{
    public function getAllLists($filterParameters, $select = ['*'], $with = []): LengthAwarePaginator
    {
        $lists = InsuranceInstitution::with($with)->select($select);
        return $lists
            ->orderBy('created_at', 'DESC')
            ->paginate(InsuranceInstitution::RECORDS_PER_PAGE);
    }

    public function store($validatedData)
    {
        return InsuranceInstitution::create($validatedData)->fresh();
    }

    public function findDetailById($id, $select = ['*'])
    {
        return InsuranceInstitution::select($select)->where('id', $id)->first();
    }

    public function delete($details)
    {
        return $details->delete();
    }

    public function update($details, $validatedData)
    {
        return $details->update($validatedData);
    }

    public function toggleStatus($details)
    {
        return $details->update([
            'is_active' => !$details->is_active,
        ]);
    }

}
