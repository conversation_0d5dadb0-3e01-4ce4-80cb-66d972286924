<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeSuggestion extends Model
{
    use HasFactory;

    protected $table = 'employee_suggestions';

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'category',
        'priority',
        'status',
        'expected_benefit',
        'implementation_cost',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
        'implemented_by',
        'implemented_at',
        'implementation_notes',
        'reward_amount',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'implementation_cost' => 'decimal:2',
        'reward_amount' => 'decimal:2',
        'reviewed_at' => 'datetime',
        'implemented_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const CATEGORIES = [
        'process_improvement' => 'تحسين العمليات',
        'cost_reduction' => 'تقليل التكاليف',
        'innovation' => 'ابتكار',
        'safety' => 'السلامة',
        'customer_service' => 'خدمة العملاء',
        'technology' => 'التكنولوجيا',
        'other' => 'أخرى'
    ];

    const PRIORITIES = [
        'low' => 'منخفضة',
        'medium' => 'متوسطة',
        'high' => 'عالية',
        'urgent' => 'عاجلة'
    ];

    const STATUSES = [
        'submitted' => 'مقدم',
        'under_review' => 'قيد المراجعة',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض',
        'implemented' => 'منفذ',
        'on_hold' => 'معلق'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function implementedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'implemented_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getCategoryLabelAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? $this->category;
    }

    public function getPriorityLabelAttribute(): string
    {
        return self::PRIORITIES[$this->priority] ?? $this->priority;
    }

    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getFormattedImplementationCostAttribute(): string
    {
        return $this->implementation_cost ? number_format($this->implementation_cost, 2) . ' ريال' : '';
    }

    public function getFormattedRewardAmountAttribute(): string
    {
        return $this->reward_amount ? number_format($this->reward_amount, 2) . ' ريال' : '';
    }

    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'submitted' => 'badge-info',
            'under_review' => 'badge-warning',
            'approved' => 'badge-success',
            'rejected' => 'badge-danger',
            'implemented' => 'badge-primary',
            'on_hold' => 'badge-secondary',
            default => 'badge-light'
        };
    }

    public function getPriorityBadgeClassAttribute(): string
    {
        return match($this->priority) {
            'low' => 'badge-light',
            'medium' => 'badge-info',
            'high' => 'badge-warning',
            'urgent' => 'badge-danger',
            default => 'badge-light'
        };
    }

    // Scopes
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeUnderReview($query)
    {
        return $query->where('status', 'under_review');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeImplemented($query)
    {
        return $query->where('status', 'implemented');
    }

    public function scopeOnHold($query)
    {
        return $query->where('status', 'on_hold');
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    public function scopePendingReview($query)
    {
        return $query->whereIn('status', ['submitted', 'under_review']);
    }

    // Helper methods
    public function canBeReviewed(): bool
    {
        return in_array($this->status, ['submitted', 'under_review']);
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'under_review';
    }

    public function canBeRejected(): bool
    {
        return $this->status === 'under_review';
    }

    public function canBeImplemented(): bool
    {
        return $this->status === 'approved';
    }

    public function isSubmitted(): bool
    {
        return $this->status === 'submitted';
    }

    public function isUnderReview(): bool
    {
        return $this->status === 'under_review';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    public function isImplemented(): bool
    {
        return $this->status === 'implemented';
    }

    public function isOnHold(): bool
    {
        return $this->status === 'on_hold';
    }

    public function startReview($reviewedBy = null): bool
    {
        if (!$this->canBeReviewed()) {
            return false;
        }

        $this->status = 'under_review';
        $this->reviewed_by = $reviewedBy ?? Auth::id();
        $this->reviewed_at = now();
        
        return $this->save();
    }

    public function approve($reviewNotes = null, $reviewedBy = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->status = 'approved';
        $this->reviewed_by = $reviewedBy ?? Auth::id();
        $this->reviewed_at = now();
        if ($reviewNotes) {
            $this->review_notes = $reviewNotes;
        }
        
        return $this->save();
    }

    public function reject($reviewNotes = null, $reviewedBy = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->status = 'rejected';
        $this->reviewed_by = $reviewedBy ?? Auth::id();
        $this->reviewed_at = now();
        if ($reviewNotes) {
            $this->review_notes = $reviewNotes;
        }
        
        return $this->save();
    }

    public function implement($implementationNotes = null, $rewardAmount = null, $implementedBy = null): bool
    {
        if (!$this->canBeImplemented()) {
            return false;
        }

        $this->status = 'implemented';
        $this->implemented_by = $implementedBy ?? Auth::id();
        $this->implemented_at = now();
        if ($implementationNotes) {
            $this->implementation_notes = $implementationNotes;
        }
        if ($rewardAmount) {
            $this->reward_amount = $rewardAmount;
        }
        
        return $this->save();
    }

    public function putOnHold(): bool
    {
        $this->status = 'on_hold';
        return $this->save();
    }
}
