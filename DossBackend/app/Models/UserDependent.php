<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserDependent extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'user_id',
        'name',
        'relation',
        'birth_date',
        'mobile',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
