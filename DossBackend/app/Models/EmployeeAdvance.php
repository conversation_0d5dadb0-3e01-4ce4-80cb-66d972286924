<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;

class EmployeeAdvance extends Model
{
    use HasFactory;

    protected $table = 'employee_advances';

    protected $fillable = [
        'user_id',
        'amount',
        'reason',
        'status',
        'request_date',
        'approved_date',
        'payment_date',
        'installments',
        'installment_amount',
        'paid_amount',
        'remaining_amount',
        'approved_by',
        'approval_notes',
        'rejection_reason',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'installment_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'request_date' => 'date',
        'approved_date' => 'date',
        'payment_date' => 'date',
        'installments' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const STATUSES = [
        'pending' => 'في الانتظار',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض',
        'paid' => 'مدفوع',
        'completed' => 'مكتمل'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
            $model->remaining_amount = $model->amount;
            // حساب قيمة القسط
            if ($model->installments > 0) {
                $model->installment_amount = $model->amount / $model->installments;
            }
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function installments(): HasMany
    {
        return $this->hasMany(AdvanceInstallment::class, 'advance_id');
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ريال';
    }

    public function getFormattedRemainingAmountAttribute(): string
    {
        return number_format($this->remaining_amount, 2) . ' ريال';
    }

    public function getFormattedPaidAmountAttribute(): string
    {
        return number_format($this->paid_amount, 2) . ' ريال';
    }

    public function getFormattedInstallmentAmountAttribute(): string
    {
        return number_format($this->installment_amount, 2) . ' ريال';
    }

    public function getPaymentProgressAttribute(): float
    {
        if ($this->amount == 0) return 0;
        return ($this->paid_amount / $this->amount) * 100;
    }

    // Scopes
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['approved', 'paid']);
    }

    // Helper methods
    public function approve($approvedBy = null, $notes = null): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'approved';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_date = now()->toDateString();
        $this->approval_notes = $notes;
        
        // إنشاء الأقساط
        $this->createInstallments();
        
        return $this->save();
    }

    public function reject($approvedBy = null, $reason = null): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'rejected';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_date = now()->toDateString();
        $this->rejection_reason = $reason;
        
        return $this->save();
    }

    public function markAsPaid($paymentDate = null): bool
    {
        if ($this->status !== 'approved') {
            return false;
        }

        $this->status = 'paid';
        $this->payment_date = $paymentDate ?? now()->toDateString();
        
        return $this->save();
    }

    public function payInstallment($amount): bool
    {
        if ($this->remaining_amount <= 0) {
            return false;
        }

        $this->paid_amount += $amount;
        $this->remaining_amount -= $amount;

        if ($this->remaining_amount <= 0) {
            $this->status = 'completed';
            $this->remaining_amount = 0;
        }

        return $this->save();
    }

    public function createInstallments(): void
    {
        if ($this->installments <= 0) return;

        $installmentAmount = $this->amount / $this->installments;
        $startDate = $this->approved_date ?? now();

        for ($i = 1; $i <= $this->installments; $i++) {
            AdvanceInstallment::create([
                'advance_id' => $this->id,
                'installment_number' => $i,
                'amount' => $installmentAmount,
                'due_date' => $startDate->copy()->addMonths($i - 1)->endOfMonth()
            ]);
        }
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->status === 'pending';
    }

    public function canBePaid(): bool
    {
        return $this->status === 'approved';
    }
}
