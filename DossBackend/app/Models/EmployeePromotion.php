<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeePromotion extends Model
{
    use HasFactory;

    protected $table = 'employee_promotions';

    protected $fillable = [
        'user_id',
        'promotion_type',
        'from_post_id',
        'to_post_id',
        'old_salary',
        'new_salary',
        'salary_increase',
        'old_grade',
        'new_grade',
        'reason',
        'effective_date',
        'approval_status',
        'approved_by',
        'approved_at',
        'performance_rating',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'old_salary' => 'decimal:2',
        'new_salary' => 'decimal:2',
        'salary_increase' => 'decimal:2',
        'effective_date' => 'date',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const PROMOTION_TYPES = [
        'position' => 'ترقية منصب',
        'salary' => 'ترقية راتب',
        'grade' => 'ترقية درجة',
        'combined' => 'ترقية شاملة'
    ];

    const APPROVAL_STATUSES = [
        'pending' => 'في الانتظار',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض'
    ];

    const PERFORMANCE_RATINGS = [
        'excellent' => 'ممتاز',
        'very_good' => 'جيد جداً',
        'good' => 'جيد',
        'satisfactory' => 'مرضي',
        'needs_improvement' => 'يحتاج تحسين'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
            // Calculate salary increase if not set
            if (!$model->salary_increase && $model->old_salary && $model->new_salary) {
                $model->salary_increase = $model->new_salary - $model->old_salary;
            }
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
            // Recalculate salary increase if salaries changed
            if ($model->isDirty(['old_salary', 'new_salary']) && $model->old_salary && $model->new_salary) {
                $model->salary_increase = $model->new_salary - $model->old_salary;
            }
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function fromPost(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'from_post_id');
    }

    public function toPost(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'to_post_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getPromotionTypeLabelAttribute(): string
    {
        return self::PROMOTION_TYPES[$this->promotion_type] ?? $this->promotion_type;
    }

    public function getApprovalStatusLabelAttribute(): string
    {
        return self::APPROVAL_STATUSES[$this->approval_status] ?? $this->approval_status;
    }

    public function getPerformanceRatingLabelAttribute(): string
    {
        return $this->performance_rating ? (self::PERFORMANCE_RATINGS[$this->performance_rating] ?? $this->performance_rating) : '';
    }

    public function getFormattedOldSalaryAttribute(): string
    {
        return $this->old_salary ? number_format($this->old_salary, 2) . ' ريال' : '';
    }

    public function getFormattedNewSalaryAttribute(): string
    {
        return $this->new_salary ? number_format($this->new_salary, 2) . ' ريال' : '';
    }

    public function getFormattedSalaryIncreaseAttribute(): string
    {
        return $this->salary_increase ? number_format($this->salary_increase, 2) . ' ريال' : '';
    }

    public function getSalaryIncreasePercentageAttribute(): float
    {
        if (!$this->old_salary || $this->old_salary <= 0) return 0;
        return round(($this->salary_increase / $this->old_salary) * 100, 2);
    }

    // Scopes
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('promotion_type', $type);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByPerformanceRating($query, $rating)
    {
        return $query->where('performance_rating', $rating);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('effective_date', [$startDate, $endDate]);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('effective_date', now()->year);
    }

    // Helper methods
    public function canBeApproved(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    public function isPending(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    public function approve($approvedBy = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->approval_status = 'approved';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function reject($approvedBy = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->approval_status = 'rejected';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function executePromotion(): bool
    {
        if (!$this->isApproved() || $this->effective_date > now()->toDateString()) {
            return false;
        }

        $user = $this->user;
        
        // Update user's position if promotion includes position change
        if ($this->to_post_id) {
            $user->post_id = $this->to_post_id;
        }

        return $user->save();
    }
}
