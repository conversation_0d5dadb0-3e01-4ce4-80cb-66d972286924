<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeBonus extends Model
{
    use HasFactory;

    protected $table = 'employee_bonuses';

    protected $fillable = [
        'user_id',
        'bonus_type',
        'bonus_category',
        'amount',
        'description',
        'reason',
        'approval_status',
        'approved_by',
        'approved_at',
        'effective_date',
        'payment_date',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'effective_date' => 'date',
        'payment_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants for bonus types
    const BONUS_TYPES = [
        'performance' => 'مكافأة أداء',
        'annual' => 'مكافأة سنوية',
        'special' => 'مكافأة خاصة',
        'achievement' => 'مكافأة إنجاز',
        'holiday' => 'مكافأة عيد',
        'overtime' => 'مكافأة عمل إضافي'
    ];

    const BONUS_CATEGORIES = [
        'financial' => 'مالية',
        'in_kind' => 'عينية',
        'promotion' => 'ترقية',
        'recognition' => 'تقدير'
    ];

    const APPROVAL_STATUSES = [
        'pending' => 'في الانتظار',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getBonusTypeLabelAttribute(): string
    {
        return self::BONUS_TYPES[$this->bonus_type] ?? $this->bonus_type;
    }

    public function getBonusCategoryLabelAttribute(): string
    {
        return self::BONUS_CATEGORIES[$this->bonus_category] ?? $this->bonus_category;
    }

    public function getApprovalStatusLabelAttribute(): string
    {
        return self::APPROVAL_STATUSES[$this->approval_status] ?? $this->approval_status;
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ريال';
    }

    // Scopes
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('bonus_type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('bonus_category', $category);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('effective_date', [$startDate, $endDate]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('effective_date', now()->month)
                    ->whereYear('effective_date', now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('effective_date', now()->year);
    }

    // Helper methods
    public function canBeApproved(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    public function isPending(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    public function approve($approvedBy = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->approval_status = 'approved';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function reject($approvedBy = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->approval_status = 'rejected';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }
}
