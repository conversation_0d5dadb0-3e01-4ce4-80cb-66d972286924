<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdvanceInstallment extends Model
{
    use HasFactory;

    protected $table = 'advance_installments';

    protected $fillable = [
        'advance_id',
        'installment_number',
        'amount',
        'due_date',
        'paid_date',
        'status',
        'payroll_id'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'due_date' => 'date',
        'paid_date' => 'date',
        'installment_number' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const STATUSES = [
        'pending' => 'في الانتظار',
        'paid' => 'مدفوع'
    ];

    // Relationships
    public function advance(): BelongsTo
    {
        return $this->belongsTo(EmployeeAdvance::class, 'advance_id');
    }

    public function payroll(): BelongsTo
    {
        return $this->belongsTo(MonthlyPayroll::class, 'payroll_id');
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ريال';
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'pending' && $this->due_date < now()->toDateString();
    }

    public function getDaysUntilDueAttribute(): int
    {
        if ($this->status === 'paid') return 0;
        return now()->diffInDays($this->due_date, false);
    }

    // Scopes
    public function scopeByAdvance($query, $advanceId)
    {
        return $query->where('advance_id', $advanceId);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('due_date', '<', now()->toDateString());
    }

    public function scopeDueThisMonth($query)
    {
        return $query->where('status', 'pending')
                    ->whereYear('due_date', now()->year)
                    ->whereMonth('due_date', now()->month);
    }

    public function scopeDueNextMonth($query)
    {
        $nextMonth = now()->addMonth();
        return $query->where('status', 'pending')
                    ->whereYear('due_date', $nextMonth->year)
                    ->whereMonth('due_date', $nextMonth->month);
    }

    // Helper methods
    public function markAsPaid($payrollId = null, $paidDate = null): bool
    {
        if ($this->status === 'paid') {
            return false;
        }

        $this->status = 'paid';
        $this->paid_date = $paidDate ?? now()->toDateString();
        $this->payroll_id = $payrollId;

        $saved = $this->save();

        if ($saved) {
            // تحديث السلفة الأساسية
            $this->advance->payInstallment($this->amount);
        }

        return $saved;
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    public function isOverdue(): bool
    {
        return $this->is_overdue;
    }

    public function canBePaid(): bool
    {
        return $this->status === 'pending';
    }
}
