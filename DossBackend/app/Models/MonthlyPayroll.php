<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class MonthlyPayroll extends Model
{
    use HasFactory;

    protected $table = 'monthly_payrolls';

    protected $fillable = [
        'user_id',
        'company_id',
        'branch_id',
        'payroll_year',
        'payroll_month',
        'basic_salary',
        'allowances',
        'overtime_amount',
        'bonus_amount',
        'deduction_amount',
        'advance_amount',
        'insurance_deduction',
        'tax_deduction',
        'gross_salary',
        'net_salary',
        'status',
        'payment_date',
        'notes',
        'approved_by',
        'approved_at',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'payroll_year' => 'integer',
        'payroll_month' => 'integer',
        'basic_salary' => 'decimal:2',
        'allowances' => 'decimal:2',
        'overtime_amount' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'deduction_amount' => 'decimal:2',
        'advance_amount' => 'decimal:2',
        'insurance_deduction' => 'decimal:2',
        'tax_deduction' => 'decimal:2',
        'gross_salary' => 'decimal:2',
        'net_salary' => 'decimal:2',
        'payment_date' => 'date',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const STATUSES = [
        'draft' => 'مسودة',
        'approved' => 'معتمد',
        'paid' => 'مدفوع'
    ];

    const MONTHS = [
        1 => 'يناير',
        2 => 'فبراير',
        3 => 'مارس',
        4 => 'أبريل',
        5 => 'مايو',
        6 => 'يونيو',
        7 => 'يوليو',
        8 => 'أغسطس',
        9 => 'سبتمبر',
        10 => 'أكتوبر',
        11 => 'نوفمبر',
        12 => 'ديسمبر'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
            // حساب الراتب الإجمالي والصافي
            $model->calculateSalary();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
            // حساب الراتب الإجمالي والصافي
            $model->calculateSalary();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getMonthNameAttribute(): string
    {
        return self::MONTHS[$this->payroll_month] ?? $this->payroll_month;
    }

    public function getFormattedNetSalaryAttribute(): string
    {
        return number_format($this->net_salary, 2) . ' ريال';
    }

    public function getFormattedGrossSalaryAttribute(): string
    {
        return number_format($this->gross_salary, 2) . ' ريال';
    }

    public function getPayrollPeriodAttribute(): string
    {
        return $this->month_name . ' ' . $this->payroll_year;
    }

    // Scopes
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByPeriod($query, $year, $month)
    {
        return $query->where('payroll_year', $year)
                    ->where('payroll_month', $month);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    // Helper methods
    public function calculateSalary(): void
    {
        // حساب الراتب الإجمالي
        $this->gross_salary = $this->basic_salary + $this->allowances + 
                             $this->overtime_amount + $this->bonus_amount;

        // حساب إجمالي الخصومات
        $totalDeductions = $this->deduction_amount + $this->advance_amount + 
                          $this->insurance_deduction + $this->tax_deduction;

        // حساب الراتب الصافي
        $this->net_salary = $this->gross_salary - $totalDeductions;
    }

    public function approve($approvedBy = null): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $this->status = 'approved';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function markAsPaid($paymentDate = null): bool
    {
        if ($this->status !== 'approved') {
            return false;
        }

        $this->status = 'paid';
        $this->payment_date = $paymentDate ?? now()->toDateString();
        
        return $this->save();
    }

    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'draft';
    }

    public function canBePaid(): bool
    {
        return $this->status === 'approved';
    }
}
