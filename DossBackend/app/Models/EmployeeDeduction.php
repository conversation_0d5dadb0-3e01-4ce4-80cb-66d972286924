<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeDeduction extends Model
{
    use HasFactory;

    protected $table = 'employee_deductions';

    protected $fillable = [
        'user_id',
        'deduction_type',
        'amount',
        'description',
        'reason',
        'approval_status',
        'approved_by',
        'approved_at',
        'effective_date',
        'installments',
        'installment_amount',
        'remaining_amount',
        'is_recurring',
        'recurrence_period',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'installment_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'is_recurring' => 'boolean',
        'approved_at' => 'datetime',
        'effective_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants for deduction types
    const DEDUCTION_TYPES = [
        'disciplinary' => 'تأديبي',
        'loan' => 'قرض',
        'advance' => 'سلفة',
        'insurance' => 'تأمين',
        'tax' => 'ضريبة',
        'absence' => 'غياب',
        'late' => 'تأخير',
        'other' => 'أخرى'
    ];

    const APPROVAL_STATUSES = [
        'pending' => 'في الانتظار',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض'
    ];

    const RECURRENCE_PERIODS = [
        'monthly' => 'شهري',
        'quarterly' => 'ربع سنوي',
        'yearly' => 'سنوي'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
            // Set remaining amount equal to total amount initially
            if (!$model->remaining_amount) {
                $model->remaining_amount = $model->amount;
            }
            // Calculate installment amount if not set
            if (!$model->installment_amount && $model->installments > 1) {
                $model->installment_amount = $model->amount / $model->installments;
            }
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getDeductionTypeLabelAttribute(): string
    {
        return self::DEDUCTION_TYPES[$this->deduction_type] ?? $this->deduction_type;
    }

    public function getApprovalStatusLabelAttribute(): string
    {
        return self::APPROVAL_STATUSES[$this->approval_status] ?? $this->approval_status;
    }

    public function getRecurrencePeriodLabelAttribute(): string
    {
        return $this->recurrence_period ? (self::RECURRENCE_PERIODS[$this->recurrence_period] ?? $this->recurrence_period) : '';
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ريال';
    }

    public function getFormattedInstallmentAmountAttribute(): string
    {
        return number_format($this->installment_amount, 2) . ' ريال';
    }

    public function getFormattedRemainingAmountAttribute(): string
    {
        return number_format($this->remaining_amount, 2) . ' ريال';
    }

    public function getProgressPercentageAttribute(): float
    {
        if ($this->amount <= 0) return 0;
        return round((($this->amount - $this->remaining_amount) / $this->amount) * 100, 2);
    }

    // Scopes
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('deduction_type', $type);
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    public function scopeNonRecurring($query)
    {
        return $query->where('is_recurring', false);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeActive($query)
    {
        return $query->where('remaining_amount', '>', 0);
    }

    public function scopeCompleted($query)
    {
        return $query->where('remaining_amount', '<=', 0);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('effective_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function canBeApproved(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    public function isPending(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    public function isCompleted(): bool
    {
        return $this->remaining_amount <= 0;
    }

    public function isActive(): bool
    {
        return $this->remaining_amount > 0;
    }

    public function approve($approvedBy = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->approval_status = 'approved';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function reject($approvedBy = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->approval_status = 'rejected';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function processInstallment($amount = null): bool
    {
        if (!$this->isApproved() || $this->isCompleted()) {
            return false;
        }

        $deductionAmount = $amount ?? $this->installment_amount;
        
        if ($deductionAmount > $this->remaining_amount) {
            $deductionAmount = $this->remaining_amount;
        }

        $this->remaining_amount -= $deductionAmount;
        
        return $this->save();
    }
}
