<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeWarning extends Model
{
    use HasFactory;

    protected $table = 'employee_warnings';

    protected $fillable = [
        'user_id',
        'warning_type',
        'category',
        'title',
        'description',
        'severity',
        'incident_date',
        'warning_date',
        'issued_by',
        'escalation_level',
        'acknowledged_by_employee',
        'acknowledgment_date',
        'employee_response',
        'improvement_plan',
        'review_date',
        'expiry_date',
        'status',
        'next_action',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'acknowledged_by_employee' => 'boolean',
        'incident_date' => 'date',
        'warning_date' => 'date',
        'acknowledgment_date' => 'date',
        'review_date' => 'date',
        'expiry_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const WARNING_TYPES = [
        'verbal' => 'شفهي',
        'written' => 'كتابي',
        'final' => 'إنذار أخير',
        'suspension' => 'إيقاف'
    ];

    const CATEGORIES = [
        'attendance' => 'الحضور',
        'performance' => 'الأداء',
        'conduct' => 'السلوك',
        'policy_violation' => 'مخالفة سياسة',
        'safety' => 'السلامة',
        'insubordination' => 'عدم الطاعة',
        'other' => 'أخرى'
    ];

    const STATUSES = [
        'active' => 'نشط',
        'acknowledged' => 'معترف به',
        'expired' => 'منتهي الصلاحية',
        'resolved' => 'محلول',
        'escalated' => 'مصعد'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function issuedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'issued_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getWarningTypeLabelAttribute(): string
    {
        return self::WARNING_TYPES[$this->warning_type] ?? $this->warning_type;
    }

    public function getCategoryLabelAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? $this->category;
    }

    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'active' => 'badge-warning',
            'acknowledged' => 'badge-info',
            'expired' => 'badge-secondary',
            'resolved' => 'badge-success',
            'escalated' => 'badge-danger',
            default => 'badge-light'
        };
    }

    public function getWarningTypeBadgeClassAttribute(): string
    {
        return match($this->warning_type) {
            'verbal' => 'badge-info',
            'written' => 'badge-warning',
            'final' => 'badge-danger',
            'suspension' => 'badge-dark',
            default => 'badge-light'
        };
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date < now()->toDateString();
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active' && !$this->is_expired;
    }

    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->expiry_date) return null;
        
        $today = now()->toDateString();
        if ($this->expiry_date->format('Y-m-d') < $today) return 0;
        
        return now()->diffInDays($this->expiry_date);
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('warning_type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function($q) {
                        $q->whereNull('expiry_date')
                          ->orWhere('expiry_date', '>=', now()->toDateString());
                    });
    }

    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now()->toDateString());
    }

    public function scopeAcknowledged($query)
    {
        return $query->where('acknowledged_by_employee', true);
    }

    public function scopeUnacknowledged($query)
    {
        return $query->where('acknowledged_by_employee', false);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByEscalationLevel($query, $level)
    {
        return $query->where('escalation_level', $level);
    }

    public function scopeHighLevel($query)
    {
        return $query->where('escalation_level', '>=', 3);
    }

    public function scopeRequiresFollowUp($query)
    {
        return $query->whereNotNull('review_date')
                    ->where('review_date', '<=', now()->toDateString())
                    ->where('status', 'active');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('warning_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function canBeAcknowledged(): bool
    {
        return !$this->acknowledged_by_employee;
    }

    public function canBeEscalated(): bool
    {
        return $this->status === 'active' && $this->escalation_level < 5;
    }

    public function canBeResolved(): bool
    {
        return in_array($this->status, ['active', 'acknowledged']);
    }

    public function isActive(): bool
    {
        return $this->status === 'active' && !$this->is_expired;
    }

    public function isAcknowledged(): bool
    {
        return $this->acknowledged_by_employee;
    }

    public function isExpired(): bool
    {
        return $this->is_expired;
    }

    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    public function isEscalated(): bool
    {
        return $this->status === 'escalated';
    }

    public function acknowledge(): bool
    {
        if (!$this->canBeAcknowledged()) {
            return false;
        }

        $this->acknowledged_by_employee = true;
        $this->acknowledgment_date = now()->toDateString();
        $this->status = 'acknowledged';
        
        return $this->save();
    }

    public function escalate(): bool
    {
        if (!$this->canBeEscalated()) {
            return false;
        }

        $this->escalation_level += 1;
        $this->status = 'escalated';
        
        return $this->save();
    }

    public function resolve($notes = null): bool
    {
        if (!$this->canBeResolved()) {
            return false;
        }

        $this->status = 'resolved';
        if ($notes) {
            $this->notes = $notes;
        }
        
        return $this->save();
    }

    public function updateStatus(): bool
    {
        if ($this->is_expired && $this->status === 'active') {
            $this->status = 'expired';
            return $this->save();
        }
        
        return false;
    }

    public function getWarningHistory($userId): array
    {
        return self::where('user_id', $userId)
                  ->orderBy('warning_date', 'desc')
                  ->get()
                  ->toArray();
    }

    public function getActiveWarningsCount($userId): int
    {
        return self::where('user_id', $userId)
                  ->active()
                  ->count();
    }

    public function getHighestEscalationLevel($userId): int
    {
        return self::where('user_id', $userId)
                  ->active()
                  ->max('escalation_level') ?? 0;
    }
}
