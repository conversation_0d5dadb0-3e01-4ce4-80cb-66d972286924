<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeComplaint extends Model
{
    use HasFactory;

    protected $table = 'employee_complaints';

    protected $fillable = [
        'user_id',
        'complaint_against_id',
        'title',
        'description',
        'category',
        'severity',
        'status',
        'is_anonymous',
        'incident_date',
        'location',
        'witnesses',
        'assigned_to',
        'assigned_at',
        'investigation_notes',
        'resolution',
        'resolved_by',
        'resolved_at',
        'follow_up_required',
        'follow_up_date',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'is_anonymous' => 'boolean',
        'follow_up_required' => 'boolean',
        'incident_date' => 'date',
        'follow_up_date' => 'date',
        'assigned_at' => 'datetime',
        'resolved_at' => 'datetime',
        'witnesses' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const CATEGORIES = [
        'harassment' => 'تحرش',
        'discrimination' => 'تمييز',
        'safety' => 'سلامة',
        'management' => 'إدارة',
        'colleague' => 'زميل عمل',
        'workplace_environment' => 'بيئة العمل',
        'policy_violation' => 'مخالفة سياسة',
        'other' => 'أخرى'
    ];

    const SEVERITIES = [
        'low' => 'منخفضة',
        'medium' => 'متوسطة',
        'high' => 'عالية',
        'critical' => 'حرجة'
    ];

    const STATUSES = [
        'submitted' => 'مقدمة',
        'investigating' => 'قيد التحقيق',
        'resolved' => 'محلولة',
        'closed' => 'مغلقة',
        'escalated' => 'مصعدة',
        'on_hold' => 'معلقة'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function complaintAgainst(): BelongsTo
    {
        return $this->belongsTo(User::class, 'complaint_against_id');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function resolvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getCategoryLabelAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? $this->category;
    }

    public function getSeverityLabelAttribute(): string
    {
        return self::SEVERITIES[$this->severity] ?? $this->severity;
    }

    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'submitted' => 'badge-info',
            'investigating' => 'badge-warning',
            'resolved' => 'badge-success',
            'closed' => 'badge-secondary',
            'escalated' => 'badge-danger',
            'on_hold' => 'badge-light',
            default => 'badge-light'
        };
    }

    public function getSeverityBadgeClassAttribute(): string
    {
        return match($this->severity) {
            'low' => 'badge-light',
            'medium' => 'badge-info',
            'high' => 'badge-warning',
            'critical' => 'badge-danger',
            default => 'badge-light'
        };
    }

    public function getWitnessesListAttribute(): string
    {
        if (!$this->witnesses || !is_array($this->witnesses)) {
            return '';
        }
        return implode(', ', $this->witnesses);
    }

    // Scopes
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeInvestigating($query)
    {
        return $query->where('status', 'investigating');
    }

    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    public function scopeEscalated($query)
    {
        return $query->where('status', 'escalated');
    }

    public function scopeAnonymous($query)
    {
        return $query->where('is_anonymous', true);
    }

    public function scopeNonAnonymous($query)
    {
        return $query->where('is_anonymous', false);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeAgainstUser($query, $userId)
    {
        return $query->where('complaint_against_id', $userId);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeHighSeverity($query)
    {
        return $query->whereIn('severity', ['high', 'critical']);
    }

    public function scopeRequiresFollowUp($query)
    {
        return $query->where('follow_up_required', true);
    }

    public function scopeOverdueFollowUp($query)
    {
        return $query->where('follow_up_required', true)
                    ->where('follow_up_date', '<', now()->toDateString());
    }

    // Helper methods
    public function canBeAssigned(): bool
    {
        return $this->status === 'submitted';
    }

    public function canBeInvestigated(): bool
    {
        return in_array($this->status, ['submitted', 'investigating']);
    }

    public function canBeResolved(): bool
    {
        return $this->status === 'investigating';
    }

    public function canBeClosed(): bool
    {
        return $this->status === 'resolved';
    }

    public function canBeEscalated(): bool
    {
        return in_array($this->status, ['investigating', 'resolved']);
    }

    public function isSubmitted(): bool
    {
        return $this->status === 'submitted';
    }

    public function isInvestigating(): bool
    {
        return $this->status === 'investigating';
    }

    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    public function isClosed(): bool
    {
        return $this->status === 'closed';
    }

    public function isEscalated(): bool
    {
        return $this->status === 'escalated';
    }

    public function assignTo($investigatorId): bool
    {
        if (!$this->canBeAssigned()) {
            return false;
        }

        $this->assigned_to = $investigatorId;
        $this->assigned_at = now();
        $this->status = 'investigating';
        
        return $this->save();
    }

    public function resolve($resolution, $resolvedBy = null): bool
    {
        if (!$this->canBeResolved()) {
            return false;
        }

        $this->status = 'resolved';
        $this->resolution = $resolution;
        $this->resolved_by = $resolvedBy ?? Auth::id();
        $this->resolved_at = now();
        
        return $this->save();
    }

    public function close(): bool
    {
        if (!$this->canBeClosed()) {
            return false;
        }

        $this->status = 'closed';
        return $this->save();
    }

    public function escalate(): bool
    {
        if (!$this->canBeEscalated()) {
            return false;
        }

        $this->status = 'escalated';
        return $this->save();
    }

    public function putOnHold(): bool
    {
        $this->status = 'on_hold';
        return $this->save();
    }

    public function addWitness($witnessName): bool
    {
        $witnesses = $this->witnesses ?? [];
        if (!in_array($witnessName, $witnesses)) {
            $witnesses[] = $witnessName;
            $this->witnesses = $witnesses;
            return $this->save();
        }
        return false;
    }

    public function removeWitness($witnessName): bool
    {
        $witnesses = $this->witnesses ?? [];
        $key = array_search($witnessName, $witnesses);
        if ($key !== false) {
            unset($witnesses[$key]);
            $this->witnesses = array_values($witnesses);
            return $this->save();
        }
        return false;
    }
}
