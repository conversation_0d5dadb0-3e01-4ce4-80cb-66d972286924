<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeSalary extends Model
{
    use HasFactory;

    protected $table = 'employee_salaries';

    protected $fillable = [
        'user_id',
        'basic_salary',
        'housing_allowance',
        'transport_allowance',
        'food_allowance',
        'other_allowances',
        'total_salary',
        'effective_date',
        'end_date',
        'is_active',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'basic_salary' => 'decimal:2',
        'housing_allowance' => 'decimal:2',
        'transport_allowance' => 'decimal:2',
        'food_allowance' => 'decimal:2',
        'other_allowances' => 'decimal:2',
        'total_salary' => 'decimal:2',
        'effective_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
            // حساب إجمالي الراتب تلقائياً
            $model->total_salary = $model->basic_salary + $model->housing_allowance + 
                                 $model->transport_allowance + $model->food_allowance + 
                                 $model->other_allowances;
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
            // حساب إجمالي الراتب تلقائياً
            $model->total_salary = $model->basic_salary + $model->housing_allowance + 
                                 $model->transport_allowance + $model->food_allowance + 
                                 $model->other_allowances;
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getFormattedBasicSalaryAttribute(): string
    {
        return number_format($this->basic_salary, 2) . ' ريال';
    }

    public function getFormattedTotalSalaryAttribute(): string
    {
        return number_format($this->total_salary, 2) . ' ريال';
    }

    public function getTotalAllowancesAttribute(): float
    {
        return $this->housing_allowance + $this->transport_allowance + 
               $this->food_allowance + $this->other_allowances;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeCurrent($query)
    {
        return $query->where('effective_date', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    public function scopeEffectiveOn($query, $date)
    {
        return $query->where('effective_date', '<=', $date)
                    ->where(function($q) use ($date) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', $date);
                    });
    }

    // Helper methods
    public function isActive(): bool
    {
        return $this->is_active && 
               $this->effective_date <= now() && 
               ($this->end_date === null || $this->end_date >= now());
    }

    public function deactivate(): bool
    {
        $this->is_active = false;
        $this->end_date = now()->toDateString();
        return $this->save();
    }

    public function activate(): bool
    {
        $this->is_active = true;
        $this->end_date = null;
        return $this->save();
    }
}
