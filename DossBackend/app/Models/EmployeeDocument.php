<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeDocument extends Model
{
    use HasFactory;

    protected $table = 'employee_documents';

    const UPLOAD_PATH = 'uploads/employee_documents/';
    const RECORDS_PER_PAGE = 20;

    // Document types with Arabic labels
    const DOCUMENT_TYPES = [
        'cv' => 'السيرة الذاتية',
        'contract' => 'عقد العمل',
        'certificate' => 'الشهادات',
        'id_copy' => 'صورة الهوية',
        'photo' => 'الصورة الشخصية',
        'medical_report' => 'التقرير الطبي',
        'qualification' => 'المؤهلات',
        'experience_letter' => 'خطاب الخبرة',
        'other' => 'أخرى'
    ];

    // Required documents for new employees
    const REQUIRED_DOCUMENTS = [
        'cv',
        'id_copy',
        'photo',
        'certificate'
    ];

    // Allowed file types
    const ALLOWED_MIME_TYPES = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    const ALLOWED_EXTENSIONS = [
        'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'
    ];

    const MAX_FILE_SIZE = 5120; // 5MB in KB

    protected $fillable = [
        'user_id',
        'document_type',
        'document_name',
        'original_name',
        'file_path',
        'file_size',
        'mime_type',
        'file_extension',
        'description',
        'is_required',
        'is_verified',
        'verified_at',
        'verified_by',
        'verification_notes',
        'uploaded_by'
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $appends = [
        'document_type_label',
        'file_size_formatted',
        'full_file_path'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uploaded_by = Auth::id();
        });
    }

    /**
     * Get the employee that owns the document
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the user who uploaded the document
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the user who verified the document
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get document type label in Arabic
     */
    public function getDocumentTypeLabelAttribute(): string
    {
        return self::DOCUMENT_TYPES[$this->document_type] ?? 'غير محدد';
    }

    /**
     * Get formatted file size
     */
    public function getFileSizeFormattedAttribute(): string
    {
        $bytes = $this->file_size;
        
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Get full file path URL
     */
    public function getFullFilePathAttribute(): string
    {
        return asset(self::UPLOAD_PATH . $this->file_path);
    }

    /**
     * Check if document type is required
     */
    public function isRequiredDocument(): bool
    {
        return in_array($this->document_type, self::REQUIRED_DOCUMENTS);
    }

    /**
     * Check if file type is allowed
     */
    public static function isAllowedFileType(string $mimeType, string $extension): bool
    {
        return in_array($mimeType, self::ALLOWED_MIME_TYPES) && 
               in_array(strtolower($extension), self::ALLOWED_EXTENSIONS);
    }

    /**
     * Check if file size is within limit
     */
    public static function isValidFileSize(int $sizeInBytes): bool
    {
        return $sizeInBytes <= (self::MAX_FILE_SIZE * 1024);
    }

    /**
     * Scope to get documents by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Scope to get verified documents
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get unverified documents
     */
    public function scopeUnverified($query)
    {
        return $query->where('is_verified', false);
    }

    /**
     * Scope to get required documents
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }
}
