<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeResignation extends Model
{
    use HasFactory;

    protected $table = 'employee_resignations';

    protected $fillable = [
        'user_id',
        'resignation_type',
        'reason',
        'notice_period',
        'last_working_date',
        'resignation_date',
        'approval_status',
        'approved_by',
        'approved_at',
        'exit_interview_completed',
        'exit_interview_date',
        'exit_interview_notes',
        'handover_completed',
        'handover_to',
        'final_settlement',
        'rehire_eligible',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'exit_interview_completed' => 'boolean',
        'handover_completed' => 'boolean',
        'rehire_eligible' => 'boolean',
        'final_settlement' => 'decimal:2',
        'last_working_date' => 'date',
        'resignation_date' => 'date',
        'exit_interview_date' => 'date',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const RESIGNATION_TYPES = [
        'voluntary' => 'طوعية',
        'involuntary' => 'غير طوعية',
        'retirement' => 'تقاعد',
        'termination' => 'فصل',
        'mutual_agreement' => 'اتفاق متبادل'
    ];

    const APPROVAL_STATUSES = [
        'pending' => 'في الانتظار',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function handoverTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'handover_to');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getResignationTypeLabelAttribute(): string
    {
        return self::RESIGNATION_TYPES[$this->resignation_type] ?? $this->resignation_type;
    }

    public function getApprovalStatusLabelAttribute(): string
    {
        return self::APPROVAL_STATUSES[$this->approval_status] ?? $this->approval_status;
    }

    public function getFormattedFinalSettlementAttribute(): string
    {
        return $this->final_settlement ? number_format($this->final_settlement, 2) . ' ريال' : '';
    }

    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->approval_status) {
            'pending' => 'badge-warning',
            'approved' => 'badge-success',
            'rejected' => 'badge-danger',
            default => 'badge-light'
        };
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->last_working_date < now()->toDateString();
    }

    public function getDaysRemainingAttribute(): int
    {
        if ($this->last_working_date < now()->toDateString()) {
            return 0;
        }
        return now()->diffInDays($this->last_working_date);
    }

    public function getNoticePeriodStatusAttribute(): string
    {
        $daysRemaining = $this->days_remaining;
        if ($daysRemaining <= 0) {
            return 'منتهية';
        } elseif ($daysRemaining <= 7) {
            return 'أسبوع أو أقل';
        } elseif ($daysRemaining <= 30) {
            return 'شهر أو أقل';
        } else {
            return 'أكثر من شهر';
        }
    }

    // Scopes
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('resignation_type', $type);
    }

    public function scopeVoluntary($query)
    {
        return $query->where('resignation_type', 'voluntary');
    }

    public function scopeInvoluntary($query)
    {
        return $query->where('resignation_type', 'involuntary');
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeExitInterviewCompleted($query)
    {
        return $query->where('exit_interview_completed', true);
    }

    public function scopeExitInterviewPending($query)
    {
        return $query->where('exit_interview_completed', false);
    }

    public function scopeHandoverCompleted($query)
    {
        return $query->where('handover_completed', true);
    }

    public function scopeHandoverPending($query)
    {
        return $query->where('handover_completed', false);
    }

    public function scopeRehireEligible($query)
    {
        return $query->where('rehire_eligible', true);
    }

    public function scopeExpired($query)
    {
        return $query->where('last_working_date', '<', now()->toDateString());
    }

    public function scopeActive($query)
    {
        return $query->where('approval_status', 'approved')
                    ->where('last_working_date', '>=', now()->toDateString());
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('resignation_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function canBeApproved(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    public function isPending(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    public function isVoluntary(): bool
    {
        return $this->resignation_type === 'voluntary';
    }

    public function isInvoluntary(): bool
    {
        return $this->resignation_type === 'involuntary';
    }

    public function approve($approvedBy = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->approval_status = 'approved';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function reject($approvedBy = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->approval_status = 'rejected';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function completeExitInterview($notes = null, $date = null): bool
    {
        $this->exit_interview_completed = true;
        $this->exit_interview_date = $date ?? now()->toDateString();
        if ($notes) {
            $this->exit_interview_notes = $notes;
        }
        
        return $this->save();
    }

    public function completeHandover($handoverTo = null): bool
    {
        $this->handover_completed = true;
        if ($handoverTo) {
            $this->handover_to = $handoverTo;
        }
        
        return $this->save();
    }

    public function setFinalSettlement($amount): bool
    {
        $this->final_settlement = $amount;
        return $this->save();
    }

    public function markAsRehireEligible($eligible = true): bool
    {
        $this->rehire_eligible = $eligible;
        return $this->save();
    }

    public function isReadyForProcessing(): bool
    {
        return $this->isApproved() && 
               $this->exit_interview_completed && 
               $this->handover_completed;
    }
}
