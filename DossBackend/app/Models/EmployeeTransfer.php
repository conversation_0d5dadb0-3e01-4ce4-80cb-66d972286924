<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class EmployeeTransfer extends Model
{
    use HasFactory;

    protected $table = 'employee_transfers';

    protected $fillable = [
        'user_id',
        'transfer_type',
        'from_branch_id',
        'to_branch_id',
        'from_department_id',
        'to_department_id',
        'from_post_id',
        'to_post_id',
        'reason',
        'request_date',
        'effective_date',
        'end_date',
        'approval_status',
        'approved_by',
        'approved_at',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'request_date' => 'date',
        'effective_date' => 'date',
        'end_date' => 'date',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Constants
    const TRANSFER_TYPES = [
        'branch' => 'نقل فرع',
        'department' => 'نقل إدارة',
        'position' => 'نقل وظيفة',
        'temporary' => 'نقل مؤقت',
        'permanent' => 'نقل دائم'
    ];

    const APPROVAL_STATUSES = [
        'pending' => 'في الانتظار',
        'approved' => 'معتمد',
        'rejected' => 'مرفوض'
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_by = Auth::id();
        });

        static::updating(function ($model) {
            $model->updated_by = Auth::id();
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function fromBranch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'from_branch_id');
    }

    public function toBranch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'to_branch_id');
    }

    public function fromDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'from_department_id');
    }

    public function toDepartment(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'to_department_id');
    }

    public function fromPost(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'from_post_id');
    }

    public function toPost(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'to_post_id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getTransferTypeLabelAttribute(): string
    {
        return self::TRANSFER_TYPES[$this->transfer_type] ?? $this->transfer_type;
    }

    public function getApprovalStatusLabelAttribute(): string
    {
        return self::APPROVAL_STATUSES[$this->approval_status] ?? $this->approval_status;
    }

    public function getIsTemporaryAttribute(): bool
    {
        return $this->transfer_type === 'temporary';
    }

    public function getIsPermanentAttribute(): bool
    {
        return $this->transfer_type === 'permanent';
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->end_date && $this->end_date < now()->toDateString();
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->isApproved() && 
               $this->effective_date <= now()->toDateString() && 
               (!$this->end_date || $this->end_date >= now()->toDateString());
    }

    // Scopes
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('transfer_type', $type);
    }

    public function scopeTemporary($query)
    {
        return $query->where('transfer_type', 'temporary');
    }

    public function scopePermanent($query)
    {
        return $query->where('transfer_type', 'permanent');
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeActive($query)
    {
        return $query->where('approval_status', 'approved')
                    ->where('effective_date', '<=', now()->toDateString())
                    ->where(function($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now()->toDateString());
                    });
    }

    public function scopeExpired($query)
    {
        return $query->where('approval_status', 'approved')
                    ->whereNotNull('end_date')
                    ->where('end_date', '<', now()->toDateString());
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('effective_date', [$startDate, $endDate]);
    }

    // Helper methods
    public function canBeApproved(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function canBeRejected(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isApproved(): bool
    {
        return $this->approval_status === 'approved';
    }

    public function isPending(): bool
    {
        return $this->approval_status === 'pending';
    }

    public function isRejected(): bool
    {
        return $this->approval_status === 'rejected';
    }

    public function approve($approvedBy = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->approval_status = 'approved';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function reject($approvedBy = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->approval_status = 'rejected';
        $this->approved_by = $approvedBy ?? Auth::id();
        $this->approved_at = now();
        
        return $this->save();
    }

    public function executeTransfer(): bool
    {
        if (!$this->isApproved() || $this->effective_date > now()->toDateString()) {
            return false;
        }

        $user = $this->user;
        
        // Update user's organizational structure
        if ($this->to_branch_id) {
            $user->branch_id = $this->to_branch_id;
        }
        
        if ($this->to_department_id) {
            $user->department_id = $this->to_department_id;
        }
        
        if ($this->to_post_id) {
            $user->post_id = $this->to_post_id;
        }

        return $user->save();
    }
}
